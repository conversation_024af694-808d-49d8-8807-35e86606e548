# ProGuard 混淆配置测试指南

## 配置完成情况

✅ **已完成的配置：**

1. **build.gradle.kts 配置**
   - Release 构建类型启用混淆：`isMinifyEnabled = true`
   - Release 构建类型启用资源压缩：`isShrinkResources = true`
   - Debug 构建类型保持不混淆，便于调试
   - 使用优化版本的 ProGuard 配置文件

2. **proguard-rules.pro 完善**
   - Kotlin 相关混淆规则
   - Android 系统组件保留规则
   - 网络请求库（Retrofit、OkHttp）规则
   - JSON 序列化库（Gson、Kotlinx Serialization）规则
   - 图片加载库（Glide）规则
   - 数据库（Room）规则
   - 第三方 SDK 规则（Agora、融云、Adjust 等）
   - 项目特定类保留规则

## 测试步骤

### 1. 构建测试

```bash
# 清理项目
./gradlew clean

# 构建 Release 版本（启用混淆）
./gradlew assembleRelease

# 构建 Debug 版本（不启用混淆）
./gradlew assembleDebug
```

### 2. 检查混淆效果

构建完成后，检查以下文件：

```
app/build/outputs/mapping/release/
├── mapping.txt          # 混淆映射文件
├── seeds.txt           # 保留的类和成员
├── usage.txt           # 被移除的代码
└── resources.txt       # 资源优化信息
```

### 3. 验证关键功能

安装 Release 版本 APK 后，测试以下功能：

- [ ] 应用启动正常
- [ ] 网络请求正常（登录、获取数据等）
- [ ] 图片加载正常
- [ ] 数据库操作正常
- [ ] 音视频通话功能正常
- [ ] 第三方 SDK 功能正常（融云、Agora 等）
- [ ] 序列化/反序列化正常

### 4. 常见问题排查

如果出现以下问题，可能需要调整混淆规则：

**问题1：网络请求失败**
- 检查数据模型类是否被正确保留
- 确认 API 接口类没有被混淆

**问题2：崩溃或异常**
- 查看崩溃日志，使用 mapping.txt 还原混淆后的类名
- 添加相应的 -keep 规则

**问题3：第三方库功能异常**
- 检查第三方库的混淆规则是否完整
- 参考官方文档添加必要的保留规则

## 优化建议

### 1. 性能优化

可以根据需要启用以下配置：

```proguard
# 移除日志输出（生产环境）
-assumenosideeffects class android.util.Log {
    public static *** v(...);
    public static *** d(...);
    public static *** i(...);
    public static *** w(...);
    public static *** e(...);
}

# 移除 Timber 日志（生产环境）
-assumenosideeffects class timber.log.Timber* {
    public static *** v(...);
    public static *** d(...);
    public static *** i(...);
    public static *** w(...);
    public static *** e(...);
}
```

### 2. 安全性增强

```proguard
# 更激进的混淆选项
-overloadaggressively
-repackageclasses ''
-allowaccessmodification
```

### 3. 调试支持

在 Debug 版本中可以启用部分混淆进行测试：

```kotlin
debug {
    isMinifyEnabled = true  // 启用混淆
    isShrinkResources = false  // 不压缩资源
    isDebuggable = true  // 保持可调试
    proguardFiles(
        getDefaultProguardFile("proguard-android.txt"),  // 使用非优化版本
        "proguard-rules.pro"
    )
}
```

## 注意事项

1. **备份重要文件**：混淆配置可能影响应用功能，建议先备份当前可用版本

2. **渐进式测试**：建议先在测试环境充分验证后再发布到生产环境

3. **保留映射文件**：`mapping.txt` 文件对于调试生产环境问题非常重要，务必保存

4. **版本控制**：将混淆规则文件纳入版本控制，便于团队协作和问题追踪

5. **持续监控**：发布后密切关注崩溃率和用户反馈，及时调整混淆规则
