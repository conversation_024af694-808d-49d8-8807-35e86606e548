[versions]
adjustAndroid = "5.4.2"
agp = "8.12.1"
billing = "8.0.0"
fragmentKtx = "1.8.9"
glide = "4.16.0"
installreferrer = "2.2"
kotlin = "2.2.10"
coreKtx = "1.17.0"
junit = "4.13.2"
junitVersion = "1.3.0"
espressoCore = "3.7.0"
appcompat = "1.7.1"
material = "1.12.0"
constraintlayout = "2.2.1"
lifecycleLivedataKtx = "2.9.2"
lifecycleViewmodelKtx = "2.9.2"
media3Exoplayer = "1.8.0"
navigationFragmentKtx = "2.9.3"
navigationUiKtx = "2.9.3"
playServicesAdsIdentifier = "18.2.0"
playServicesAuth = "21.4.0"
retrofit = "3.0.0"
retrofitKotlinxSerializationConverter = "1.0.0"
okhttp = "4.12.0"
retrofitGsonConverter = "3.0.0"
kotlinxSerialization = "1.9.0"
viewpager2 = "1.1.0"
swiperefreshlayout = "1.1.0"
cronetEmbedded = "119.6045.31"
roomVersion = "2.7.2"
emoji2Emojipicker = "1.5.0"
facebook-android-sdk = "18.1.3"
google-service = "4.4.3"
firebase-bom = "33.16.0" # 不要更改最新，34.0.0开始需要minSdkVersion23
firebase-analytics = "22.5.0"  # 不要更改最新，23.0.0开始需要minSdkVersion23 「https://firebase.google.com/support/release-notes/android」
credentials = "1.5.0"
identityGoogleid = "1.1.1"

[libraries]
adjust-android = { module = "com.adjust.sdk:adjust-android", version.ref = "adjustAndroid" }
adjust-android-webbridge = { module = "com.adjust.sdk:adjust-android-webbridge", version.ref = "adjustAndroid" }
androidx-core-ktx = { group = "androidx.core", name = "core-ktx", version.ref = "coreKtx" }
androidx-fragment-ktx = { module = "androidx.fragment:fragment-ktx", version.ref = "fragmentKtx" }
androidx-media3-exoplayer = { module = "androidx.media3:media3-exoplayer", version.ref = "media3Exoplayer" }
androidx-media3-ui = { module = "androidx.media3:media3-ui", version.ref = "media3Exoplayer" }
androidx-viewpager2 = { module = "androidx.viewpager2:viewpager2", version.ref = "viewpager2" }
billing = { module = "com.android.billingclient:billing", version.ref = "billing" }
glide-compiler = { module = "com.github.bumptech.glide:compiler", version.ref = "glide" }
glide = { module = "com.github.bumptech.glide:glide", version.ref = "glide" }
installreferrer = { module = "com.android.installreferrer:installreferrer", version.ref = "installreferrer" }
junit = { group = "junit", name = "junit", version.ref = "junit" }
androidx-junit = { group = "androidx.test.ext", name = "junit", version.ref = "junitVersion" }
androidx-espresso-core = { group = "androidx.test.espresso", name = "espresso-core", version.ref = "espressoCore" }
androidx-appcompat = { group = "androidx.appcompat", name = "appcompat", version.ref = "appcompat" }
material = { group = "com.google.android.material", name = "material", version.ref = "material" }
androidx-constraintlayout = { group = "androidx.constraintlayout", name = "constraintlayout", version.ref = "constraintlayout" }
androidx-lifecycle-livedata-ktx = { group = "androidx.lifecycle", name = "lifecycle-livedata-ktx", version.ref = "lifecycleLivedataKtx" }
androidx-lifecycle-viewmodel-ktx = { group = "androidx.lifecycle", name = "lifecycle-viewmodel-ktx", version.ref = "lifecycleViewmodelKtx" }
androidx-navigation-fragment-ktx = { group = "androidx.navigation", name = "navigation-fragment-ktx", version.ref = "navigationFragmentKtx" }
androidx-navigation-ui-ktx = { group = "androidx.navigation", name = "navigation-ui-ktx", version.ref = "navigationUiKtx" }
play-services-ads-identifier = { module = "com.google.android.gms:play-services-ads-identifier", version.ref = "playServicesAdsIdentifier" }
play-services-auth = { module = "com.google.android.gms:play-services-auth", version.ref = "playServicesAuth" }
retrofit = { group = "com.squareup.retrofit2", name = "retrofit", version.ref = "retrofit" }
retrofit-kotlinx-serialization-converter = { group = "com.jakewharton.retrofit", name = "retrofit2-kotlinx-serialization-converter", version.ref = "retrofitKotlinxSerializationConverter" }
retrofit-converter-gson = { group = "com.squareup.retrofit2", name = "converter-gson", version.ref = "retrofitGsonConverter" }
okhttp = { group = "com.squareup.okhttp3", name = "okhttp", version.ref = "okhttp" }
kotlinx-serialization-json = { group = "org.jetbrains.kotlinx", name = "kotlinx-serialization-json", version.ref = "kotlinxSerialization" }
okhttp-logging-interceptor = { group = "com.squareup.okhttp3", name = "logging-interceptor", version.ref = "okhttp" }
androidx-swiperefreshlayout = { group = "androidx.swiperefreshlayout", name = "swiperefreshlayout", version.ref = "swiperefreshlayout" }
cronet-embedded = { group = "org.chromium.net", name = "cronet-embedded", version.ref = "cronetEmbedded" }
room-runtime = { group = "androidx.room", name = "room-runtime", version.ref = "roomVersion" }
room-compiler = { group = "androidx.room", name = "room-compiler", version.ref = "roomVersion" }
room-ktx = { group = "androidx.room", name = "room-ktx", version.ref = "roomVersion" }
androidx-emoji2-emojipicker = { group = "androidx.emoji2", name = "emoji2-emojipicker", version.ref = "emoji2Emojipicker" }
facebook-android-sdk = { group = "com.facebook.android", name = "facebook-android-sdk", version.ref = "facebook-android-sdk" }
facebook-core = { group = "com.facebook.android", name = "facebook-core", version.ref = "facebook-android-sdk" }
firebase-bom = { group = "com.google.firebase", name = "firebase-bom", version.ref = "firebase-bom" }
firebase-analytics = { group = "com.google.firebase", name = "firebase-analytics", version.ref = "firebase-analytics"}
androidx-credentials = { group = "androidx.credentials", name = "credentials", version.ref = "credentials"}
androidx-credentials-play-services-auth = { group = "androidx.credentials", name = "credentials-play-services-auth", version.ref = "credentials"}
android-libraries-identity-googleid = { group = "com.google.android.libraries.identity.googleid", name = "googleid", version.ref = "identityGoogleid"}

[plugins]
android-application = { id = "com.android.application", version.ref = "agp" }
kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }
google-services = { id = "com.google.gms.google-services", version.ref = "google-service" }
room = { id = "androidx.room", version.ref = "roomVersion" }

