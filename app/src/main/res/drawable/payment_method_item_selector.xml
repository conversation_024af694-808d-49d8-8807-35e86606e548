<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- 选中状态 -->
    <item android:state_selected="true">
        <shape android:shape="rectangle">
            <solid android:color="#EFFFFE" />
            <corners android:radius="16dp" />
            <stroke
                android:width="1dp"
                android:color="#1ECEC3" />
        </shape>
    </item>
    
    <!-- 按下状态 -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/transparent" />
            <corners android:radius="16dp" />
            <stroke
                android:width="1dp"
                android:color="#E2E2E2" />
        </shape>
    </item>
    
    <!-- 默认状态 -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@color/transparent" />
            <corners android:radius="16dp" />
            <stroke
                android:width="1dp"
                android:color="#E2E2E2" />
        </shape>
    </item>
    
</selector>
