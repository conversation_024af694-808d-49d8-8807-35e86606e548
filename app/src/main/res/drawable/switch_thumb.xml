<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 开启状态 -->
    <item android:state_checked="true">
        <shape android:shape="oval">
            <solid android:color="@android:color/white" />
            <stroke android:width="3dp" android:color="@color/orange" />
            <size android:width="25dp" android:height="25dp" />
        </shape>
    </item>
    <!-- 关闭状态 -->
    <item android:state_checked="false">
        <shape android:shape="oval">
            <solid android:color="@android:color/white" />
            <stroke android:width="3dp" android:color="@color/switch_thumb_false" />
            <size android:width="25dp" android:height="25dp" />
        </shape>
    </item>
</selector>