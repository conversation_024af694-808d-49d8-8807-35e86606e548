<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="25dp"
        android:background="@color/transparent">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/bg_gift_bottomsheet"
            android:orientation="vertical"
            android:paddingTop="15dp"
            android:paddingBottom="15dp">
            <!-- 顶部金币数量 -->
            <TextView
                android:id="@+id/tv_coin_balance"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="17dp"
                android:drawableLeft="@drawable/coin"
                android:padding="1dp"
                android:text=" 1000"
                android:textColor="@android:color/white"
                android:textSize="14sp"
                android:fontFamily="@font/roboto_medium"/>
            <TextView
                android:id="@+id/tv_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/tv_coin_balance"
                android:textAlignment="center"
                android:layout_marginTop="30dp"
                android:text="@string/insufficient_balance"
                android:textColor="#fff"
                android:textSize="16sp"
                android:fontFamily="@font/roboto_medium"/>

            <TextView
                android:id="@+id/tv_broadcast_price"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/tv_title"
                android:textAlignment="center"
                android:layout_marginTop="11dp"
                android:text="Her video call price"
                android:textColor="#99FFFFFF"
                android:textSize="14sp"
                android:fontFamily="@font/roboto_regular"/>

            <com.score.callmetest.ui.widget.RechargeSubView
                android:id="@+id/recharge_sub_view"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="17dp"
                android:layout_below="@+id/tv_broadcast_price" />
        </RelativeLayout>

    </FrameLayout>

    <ImageView
        android:id="@+id/emoji"
        android:layout_width="68dp"
        android:layout_height="68dp"
        android:layout_gravity="center_horizontal"
        android:src="@drawable/emoji_call_unshadow" />


</FrameLayout>
