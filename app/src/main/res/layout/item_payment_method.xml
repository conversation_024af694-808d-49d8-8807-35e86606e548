<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:card_view="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@android:color/transparent"
    card_view:cardCornerRadius="12dp"
    card_view:cardElevation="0dp">

    <com.score.callmetest.ui.widget.AlphaLinearLayout
        android:id="@+id/item_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/payment_method_item_selector"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:padding="16dp">

        <!-- 名称、优惠、标签 -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="7dp"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <LinearLayout
                android:id="@+id/ll_item_payment_method"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentStart="true"
                android:layout_centerVertical="true"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                card_view:layout_constrainedWidth="true"
                card_view:layout_constraintBottom_toTopOf="@id/ll_item_payment_method_more"
                card_view:layout_constraintEnd_toStartOf="@id/tv_tag"
                card_view:layout_constraintHorizontal_bias="0"
                card_view:layout_constraintStart_toStartOf="parent"
                card_view:layout_constraintTop_toTopOf="parent">

                <!-- 支付方式图标 -->
                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/iv_icon"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:scaleType="centerInside"
                    tools:src="@drawable/ic_google" />


                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tv_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/roboto_regular"
                    android:textColor="@color/black"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    tools:text="Credit Card" />


                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/iv_discount"
                    android:layout_width="13dp"
                    android:layout_height="13dp"
                    android:layout_marginStart="5dp"
                    android:src="@drawable/coin"
                    android:visibility="gone"
                    tools:visibility="visible" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tv_discount"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="3dp"
                    android:textColor="#FF6C00"
                    android:textSize="14sp"
                    android:visibility="gone"
                    tools:text="+50%"
                    tools:visibility="visible" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_item_payment_method_more"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentStart="true"
                android:layout_centerVertical="true"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:visibility="gone"
                tools:visibility="visible"
                card_view:layout_constrainedWidth="true"
                card_view:layout_constraintBottom_toBottomOf="parent"
                card_view:layout_constraintEnd_toStartOf="@id/tv_tag"
                card_view:layout_constraintHorizontal_bias="0"
                card_view:layout_constraintStart_toStartOf="parent"
                card_view:layout_constraintTop_toBottomOf="@id/ll_item_payment_method">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/iv_bt_discount"
                    android:layout_width="13dp"
                    android:layout_height="13dp"
                    android:layout_marginStart="5dp"
                    android:src="@drawable/coin" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tv_bt_discount"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="3dp"
                    android:textColor="#FF6C00"
                    android:textSize="14sp"
                    tools:text="+50% More Coins" />

            </LinearLayout>


            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_tag"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="7dp"
                android:fontFamily="@font/roboto_regular"
                android:textColor="#939396"
                android:textSize="12sp"
                android:visibility="gone"
                card_view:layout_constraintBottom_toBottomOf="parent"
                card_view:layout_constraintEnd_toEndOf="parent"
                card_view:layout_constraintTop_toTopOf="parent"
                tools:text="Recommend"
                tools:visibility="visible" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <!-- 选择框 -->
        <View
            android:id="@+id/iv_selected"
            android:layout_width="22dp"
            android:layout_height="22dp"
            android:background="@drawable/payment_method_rb_selector" />
    </com.score.callmetest.ui.widget.AlphaLinearLayout>
</androidx.cardview.widget.CardView>
