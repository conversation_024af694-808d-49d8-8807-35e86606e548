<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/mycard_top_root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.mine.mycard.MyCardActivity">

    <ImageView
        android:id="@+id/img_bg"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/bg_mycard_light"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <LinearLayout
        android:id="@+id/line_top"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:layout_marginTop="44dp"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <!-- 返回按钮 -->
        <com.score.callmetest.ui.widget.AlphaImageView
            android:id="@+id/btn_return"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_gravity="center"
            android:layout_marginStart="15dp"
            android:scaleType="fitCenter"
            android:src="@drawable/nav_btn_back" />

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_gravity="center"
            android:layout_marginEnd="47dp"
            android:gravity="center"
            android:fontFamily="@font/roboto_medium"
            android:text="My Card"
            android:textColor="@color/black"
            android:textSize="18sp"
            android:textStyle="bold" />
    </LinearLayout>

    <!--  tab  -->
    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tab_follow"
        style="@style/CustomTabStyle"
        android:layout_width="wrap_content"
        android:layout_height="44dp"
        android:background="@android:color/transparent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/line_top"
        app:tabGravity="center"
        app:tabIndicatorHeight="0dp"
        app:tabMode="fixed" />

    <!-- ViewPager2用于承载内容 -->
    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/view_pager"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:overScrollMode="never"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tab_follow" />

</androidx.constraintlayout.widget.ConstraintLayout>
