<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_mine_function_item_selector">

    <!-- 头像 -->
    <com.score.callmetest.ui.widget.CircleIconButton
        android:id="@+id/iv_avatar"
        android:layout_width="52dp"
        android:layout_height="52dp"
        android:layout_marginVertical="12dp"
        android:layout_marginStart="15dp"
        android:clickable="true"
        android:focusable="true"
        android:scaleType="centerCrop"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/placeholder" />

    <!-- 在线状态指示器 -->
    <View
        android:id="@+id/view_online_status"
        android:layout_width="12dp"
        android:layout_height="12dp"
        android:layout_marginEnd="3dp"
        android:layout_marginBottom="3dp"
        android:background="@drawable/shape_online_status"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintBottom_toBottomOf="@id/iv_avatar"
        app:layout_constraintEnd_toEndOf="@id/iv_avatar" />

    <!-- 用户名 -->
    <TextView
        android:id="@+id/tv_username"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:layout_marginEnd="8dp"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/black"
        android:textSize="15sp"
        android:textStyle="bold"
        android:fontFamily="@font/roboto_bold"
        app:layout_constraintEnd_toStartOf="@id/tv_timestamp"
        app:layout_constraintStart_toEndOf="@id/iv_avatar"
        app:layout_constraintTop_toTopOf="@id/iv_avatar"
        app:layout_constraintBottom_toTopOf="@+id/tv_last_message"
        tools:text="Kathleen" />

    <!-- 最后消息内容 -->
    <TextView
        android:id="@+id/tv_last_message"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:layout_marginEnd="8dp"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/text_gray"
        android:textSize="14sp"
        android:fontFamily="@font/roboto_regular"
        app:layout_constraintEnd_toStartOf="@id/tv_unread_count"
        app:layout_constraintStart_toStartOf="@id/tv_username"
        app:layout_constraintBottom_toBottomOf="@id/iv_avatar"
        app:layout_constraintTop_toBottomOf="@+id/tv_username"
        tools:text="Nice to meet you !" />

    <!-- 时间戳 -->
    <TextView
        android:id="@+id/tv_timestamp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="21dp"
        android:layout_marginEnd="12dp"
        android:textColor="@color/text_gray"
        android:textSize="12sp"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible"
        tools:text="2023.02.05" />

    <!-- 未读消息数 -->
    <TextView
        android:id="@+id/tv_unread_count"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="3dp"
        android:background="@drawable/shape_unread_count"
        android:visibility="gone"
        android:gravity="center"
        android:paddingHorizontal="7dp"
        android:paddingVertical="2dp"
        android:textColor="@android:color/white"
        android:textSize="12sp"
        app:layout_constraintEnd_toEndOf="@id/tv_timestamp"
        app:layout_constraintTop_toBottomOf="@id/tv_timestamp"
        tools:text="99+"
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout>