<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <com.opensource.svgaplayer.SVGAImageView
        android:id="@+id/follow_bg_svga"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clickable="false"
        android:focusable="false"
        android:scaleType="fitXY"
        android:visibility="visible" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingHorizontal="10dp"
        android:paddingVertical="4dp">

        <ImageView
            android:id="@+id/follow_icon"
            android:layout_width="11dp"
            android:layout_height="11dp"
            android:src="@drawable/add_friend" />

        <com.opensource.svgaplayer.SVGAImageView
            android:id="@+id/follow_action_svga"
            android:layout_width="11dp"
            android:layout_height="11dp"
            android:clickable="false"
            android:focusable="false"
            android:scaleType="fitXY"
            android:visibility="gone" />

        <TextView
            android:id="@+id/follow_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="3dp"
            android:text="Follow"
            android:textColor="@android:color/white"
            android:textSize="13sp"
            android:textStyle="bold" />

    </LinearLayout>
</FrameLayout>