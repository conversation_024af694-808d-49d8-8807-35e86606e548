<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root_content"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/chat_bg">

    <androidx.appcompat.widget.AppCompatImageView
        android:layout_width="match_parent"
        android:layout_height="90dp"
        android:background="@drawable/main_bg"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- 顶部工具栏 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        app:layout_constraintTop_toTopOf="parent">

        <!-- 返回按钮 -->
        <com.score.callmetest.ui.widget.AlphaImageView
            android:id="@+id/btnBack"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_marginStart="15dp"
            android:scaleType="fitCenter"
            android:src="@drawable/nav_btn_back"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:contentDescription="返回" />

        <View
            android:id="@+id/onlineStatusDot"
            android:layout_width="8dp"
            android:layout_height="8dp"
            android:layout_marginStart="8dp"
            android:layout_marginEnd="4dp"
            android:background="@drawable/shape_circle_green"
            android:visibility="gone"
            tools:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/tvTitle"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toEndOf="@id/btnBack"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- 标题（用户名称） -->
        <TextView
            android:id="@+id/tvTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginEnd="8dp"
            android:ellipsize="marquee"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:marqueeRepeatLimit="marquee_forever"
            android:singleLine="true"
            android:textColor="@color/black"
            android:textSize="18sp"
            android:textStyle="bold"
            app:layout_constrainedWidth="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/btnMenu"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintStart_toEndOf="@id/onlineStatusDot"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="----------------------------------------------CCHUBBy 🔥" />

        <!-- 右侧菜单按钮 -->
        <com.score.callmetest.ui.widget.AlphaImageView
            android:id="@+id/btnMenu"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_gravity="end"
            android:layout_marginEnd="15dp"
            android:scaleType="fitCenter"
            android:src="@drawable/chat_more"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:contentDescription="更多选项" />


        <TextView
            android:id="@+id/tvSum"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="@id/btnBack"
            app:layout_constraintStart_toEndOf="@id/btnBack"
            android:layout_marginTop="-3dp"
            android:layout_marginStart="-12dp"
            android:textColor="#FFF"
            android:paddingHorizontal="4dp"
            android:paddingVertical="1dp"
            android:textSize="10sp"
            android:fontFamily="@font/roboto_bold"
            android:textStyle="bold"
            android:visibility="gone"
            android:background="@drawable/shape_unread_count"
            tools:visibility="visible"
            tools:text="99+"
            android:elevation="1dp"
            android:minWidth="16dp"
            android:gravity="center" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- 统一的消息列表（包含主播card和消息） -->
    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipeRefreshLayout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="0dp"
        android:overScrollMode="never"
        app:layout_constraintBottom_toTopOf="@id/inputLayout"
        app:layout_constraintTop_toBottomOf="@id/toolbar"
        app:layout_constraintVertical_bias="0.0"
        app:layout_constraintVertical_chainStyle="packed">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clipToPadding="false"
            android:overScrollMode="never"
            android:paddingBottom="8dp"
            tools:listitem="@layout/item_chat_message_left" />

    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    <!-- 无消息提示 -->
    <ViewStub
        android:id="@+id/empty_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/swipeRefreshLayout"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/swipeRefreshLayout"
        tools:visibility="visible"
        android:inflatedId="@+id/layout_empty_rv_parent"
        android:layout="@layout/layout_empty_rv_bg" />

    <!-- 底部输入框区域 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/inputLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"
        tools:visibility="visible"
        android:paddingTop="7dp"
        android:paddingBottom="7dp"
        android:background="@drawable/bottom_sheet_rounded_top_bg"
        app:layout_constraintBottom_toTopOf="@id/board_container">

        <!-- 切换语音按钮 -->
        <com.score.callmetest.ui.widget.AlphaImageView
            android:id="@+id/btnVoice"
            android:layout_width="34dp"
            android:layout_height="34dp"
            android:layout_marginStart="12dp"
            android:scaleType="fitCenter"
            android:src="@drawable/chat_voice"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="@id/etMessage"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/etMessage"
            tools:visibility="invisible" />

        <!-- 切换键盘按钮 -->
        <com.score.callmetest.ui.widget.AlphaImageView
            android:id="@+id/btnKeyboard"
            android:layout_width="34dp"
            android:layout_height="34dp"
            android:layout_marginStart="12dp"
            android:scaleType="fitCenter"
            android:src="@drawable/chat_keyboard"
            android:visibility="invisible"
            app:layout_constraintBottom_toBottomOf="@id/etMessage"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/etMessage"
            tools:visibility="visible" />

        <!-- 输入框 -->
        <androidx.appcompat.widget.AppCompatEditText
            android:id="@+id/etMessage"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="8dp"
            android:background="@drawable/bg_input_box"
            android:gravity="center_vertical"
            android:hint="@string/chat_input_hint"
            android:importantForAutofill="no"
            android:inputType="textMultiLine"
            android:maxLines="4"
            android:minHeight="44dp"
            android:paddingHorizontal="20dp"
            android:textColor="@color/black"
            android:textSize="14sp"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/btnEmoji"
            app:layout_constraintStart_toEndOf="@id/btnVoice"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="invisible" />

        <!-- 长按录音 -->
        <com.score.callmetest.ui.widget.AlphaTextView
            android:id="@+id/btnTalk"
            android:layout_width="0dp"
            android:layout_height="44dp"
            android:layout_marginHorizontal="8dp"
            android:background="@drawable/bg_input_box"
            android:gravity="center"
            android:text="@string/hold_talk"
            android:textColor="@color/black"
            android:textSize="14sp"
            android:visibility="invisible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/btnImage"
            app:layout_constraintStart_toEndOf="@id/btnKeyboard"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible" />


        <!-- 表情按钮 -->
        <com.score.callmetest.ui.widget.AlphaImageView
            android:id="@+id/btnEmoji"
            android:layout_width="34dp"
            android:layout_height="34dp"
            android:layout_marginEnd="9dp"
            android:scaleType="fitCenter"
            android:src="@drawable/chat_emoji"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="@id/etMessage"
            app:layout_constraintEnd_toStartOf="@id/btnImage"
            app:layout_constraintTop_toTopOf="@id/etMessage"
            tools:visibility="invisible" />

        <!-- 图片按钮 -->
        <com.score.callmetest.ui.widget.AlphaImageView
            android:id="@+id/btnImage"
            android:layout_width="34dp"
            android:layout_height="34dp"
            android:layout_marginEnd="12dp"
            android:src="@drawable/chat_pic"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="@id/etMessage"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/etMessage"
            tools:visibility="visible" />

        <!-- 发送按钮 -->
        <com.score.callmetest.ui.widget.AlphaImageView
            android:id="@+id/btnSend"
            android:layout_width="34dp"
            android:layout_height="34dp"
            android:layout_marginEnd="12dp"
            android:src="@drawable/chat_send"
            android:visibility="invisible"
            app:layout_constraintBottom_toBottomOf="@id/etMessage"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/etMessage"
            tools:visibility="invisible" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!--  keyboard  -->
    <FrameLayout
        android:id="@+id/board_container"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_width="match_parent"
        android:visibility="gone"
        android:background="@android:color/white"
        tools:visibility="visible"
        android:layout_height="@dimen/board_height">

        <!--  emoji  -->
        <androidx.emoji2.emojipicker.EmojiPickerView
            android:id="@+id/emoji_picker"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@android:color/white"
            android:visibility="gone"
            app:emojiGridColumns="9"
            app:layout_constraintBottom_toBottomOf="parent"
            tools:visibility="visible" />

    </FrameLayout>

    <!-- 礼物按钮（悬浮） -->
    <com.score.callmetest.ui.widget.AlphaImageView
        android:id="@+id/fabGift"
        android:layout_width="60dp"
        android:layout_height="60dp"
        android:layout_marginEnd="11dp"
        android:layout_marginBottom="15dp"
        android:scaleType="fitCenter"
        android:src="@drawable/chat_gift"
        app:layout_constraintBottom_toTopOf="@id/fabVideoCall"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- 视频通话按钮（悬浮） -->
    <com.score.callmetest.ui.widget.AlphaImageView
        android:id="@+id/fabVideoCall"
        android:layout_width="46dp"
        android:layout_height="46dp"
        android:layout_marginEnd="18dp"
        android:layout_marginBottom="16dp"
        android:background="@color/transparent"
        android:scaleType="fitCenter"
        android:src="@drawable/video_gray"
        app:layout_constraintBottom_toTopOf="@id/inputLayout"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- 礼物展示背景动画容器 -->
    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/gift_animation_bg_image"
        android:layout_width="318dp"
        android:layout_height="318dp"
        android:layout_gravity="center"
        android:scaleType="fitCenter"
        android:src="@drawable/gift_light_bg"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible" />

    <!-- 礼物展示动画容器 -->
    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/gift_animation_image"
        android:layout_width="240dp"
        android:layout_height="240dp"
        android:layout_gravity="center"
        android:scaleType="fitCenter"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout> 