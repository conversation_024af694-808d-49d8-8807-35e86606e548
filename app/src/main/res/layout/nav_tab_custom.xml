<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:clipChildren="false"
    android:clipToPadding="false"
    android:padding="8dp">

    <ImageView
        android:id="@+id/tab_icon"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:layout_centerInParent="true"
        android:visibility="visible" />

    <com.opensource.svgaplayer.SVGAImageView
        android:id="@+id/tab_svga"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:layout_centerInParent="true"
        android:visibility="gone" />

    <TextView
        android:id="@+id/tab_sum"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignTop="@id/tab_icon"
        android:layout_alignEnd="@id/tab_icon"
        android:layout_marginTop="-3dp"
        android:layout_marginEnd="-3dp"
        android:textColor="#FFF"
        android:paddingHorizontal="6dp"
        android:paddingVertical="1dp"
        android:textSize="12sp"
        android:fontFamily="@font/roboto_bold"
        android:textStyle="bold"
        android:visibility="gone"
        android:background="@drawable/shape_unread_count"
        tools:visibility="visible"
        tools:text="99+"
        android:minWidth="16dp"
        android:gravity="center" />

</RelativeLayout>