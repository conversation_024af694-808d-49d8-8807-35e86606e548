<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/layout_empty_rv_parent"
    android:layout_width="match_parent"
    android:layout_height="match_parent">


    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/layout_empty_rv_bg_img"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:scaleType="fitCenter"
        android:src="@drawable/empty"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.23" />

    <TextView
        android:id="@+id/layout_empty_rv_bg_hint_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="@string/msg_list_empty"
        android:textColor="@color/empty_rv_text"
        android:textSize="14sp"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toBottomOf="@id/layout_empty_rv_bg_img"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />


</androidx.constraintlayout.widget.ConstraintLayout>