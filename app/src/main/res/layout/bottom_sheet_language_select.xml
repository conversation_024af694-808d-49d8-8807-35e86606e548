<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="24dp">

    <TextView
        android:id="@+id/tv_language_zh"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:gravity="center_vertical"
        android:text="简体中文"
        android:textSize="18sp"
        android:paddingStart="16dp"
        android:paddingEnd="16dp"
        android:background="?android:attr/selectableItemBackground"
        android:clickable="true" />

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="#EEEEEE" />

    <TextView
        android:id="@+id/tv_language_en"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:gravity="center_vertical"
        android:text="English"
        android:textSize="18sp"
        android:paddingStart="16dp"
        android:paddingEnd="16dp"
        android:background="?android:attr/selectableItemBackground"
        android:clickable="true" />

</LinearLayout> 