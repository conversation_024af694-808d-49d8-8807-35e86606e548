<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="80dp"
    android:layout_height="80dp"
    android:layout_marginEnd="8dp">

    <ImageView
        android:id="@+id/ivPhoto"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop"
        android:background="@drawable/round_corner_bg" />

    <ImageView
        android:id="@+id/btnDelete"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_alignParentTop="true"
        android:layout_alignParentEnd="true"
        android:layout_marginTop="4dp"
        android:layout_marginEnd="4dp"
        android:background="@drawable/circle_delete_bg"
        android:src="@drawable/ic_close"
        android:scaleType="center" />

</RelativeLayout> 