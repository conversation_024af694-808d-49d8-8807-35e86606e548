<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/bottomSheetRoot"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bottom_sheet_rounded_top_bg">

    <ImageView
        android:layout_width="match_parent"
        android:layout_height="55dp"
        android:scaleType="centerCrop"
        android:src="@drawable/bottom_sheet_bg" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        android:layout_marginTop="35dp"
        android:layout_marginBottom="15dp"
        android:orientation="vertical">

        <!-- 操作按钮组 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@android:color/transparent"
            android:orientation="vertical">

            <com.score.callmetest.ui.widget.AlphaTextView
                android:id="@+id/btn_unfollow"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:layout_marginBottom="12dp"
                android:background="@drawable/bg_btn_rounded_gray"
                android:gravity="center"
                android:text="Unfollow"
                android:textColor="@color/black"
                android:textSize="16sp"
                android:textStyle="bold"
                android:visibility="gone" />

            <com.score.callmetest.ui.widget.AlphaTextView
                android:id="@+id/btn_block"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:layout_marginBottom="12dp"
                android:background="@drawable/bg_btn_rounded_gray"
                android:gravity="center"
                android:text="Block"
                android:textColor="@color/black"
                android:textSize="16sp"
                android:textStyle="bold"
                android:typeface="sans" />

            <com.score.callmetest.ui.widget.AlphaTextView
                android:id="@+id/btn_report"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:background="@drawable/bg_btn_rounded_gray"
                android:gravity="center"
                android:text="Report"
                android:textColor="@color/black"
                android:textSize="16sp"
                android:textStyle="bold"
                android:typeface="sans" />
        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="16dp" />

        <!-- Cancel按钮高亮 -->
        <com.score.callmetest.ui.widget.AlphaTextView
            android:id="@+id/btn_cancel"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:background="@drawable/bg_btn_rounded_blue"
            android:gravity="center"
            android:text="Cancel"
            android:textColor="@color/black"
            android:textSize="16sp"
            android:textStyle="bold" />

    </LinearLayout>
</FrameLayout>