<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingStart="60dp"
    android:paddingTop="4dp"
    android:paddingEnd="8dp"
    android:paddingBottom="4dp">

    <!-- 头像 -->
    <ImageView
        android:id="@+id/ivAvatar"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="8dp"
        android:contentDescription="@string/avatar"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/default_avatar" />

    <!-- 语音消息容器 -->
    <LinearLayout
        android:id="@+id/llVoiceContainer"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="6dp"
        android:background="@drawable/bg_chat_bubble_right"
        android:gravity="center_vertical"
        android:minWidth="62dp"
        android:orientation="horizontal"
        android:padding="9dp"
        app:layout_constraintEnd_toStartOf="@+id/ivAvatar"
        app:layout_constraintTop_toTopOf="parent">

        <!-- 语音时长 -->
        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvDuration"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="3dp"
            android:textColor="@color/black"
            android:textSize="15sp"
            tools:text="12'"/>

        <!-- 语音图标 -->
        <com.opensource.svgaplayer.SVGAImageView
            android:id="@+id/ivVoice"
            android:layout_width="18dp"
            android:layout_height="18dp"
            android:layout_marginStart="4dp"
            android:src="@drawable/chart_voice_right"
            app:autoPlay="false"
            app:clearsAfterDetached="false" />
   <!--     <ImageView
            android:id="@+id/ivVoice"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_marginStart="4dp"
            android:src="@drawable/chart_voice_right" />-->
    </LinearLayout>

    <!-- 发送中状态 -->
    <ProgressBar
        android:id="@+id/progressSending"
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:layout_marginEnd="4dp"
        android:indeterminateTint="@color/gray_999"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/llVoiceContainer"
        app:layout_constraintEnd_toStartOf="@+id/llVoiceContainer"
        app:layout_constraintTop_toTopOf="@+id/llVoiceContainer"
        tools:visibility="visible" />

    <!-- 状态图标 -->
    <ImageView
        android:id="@+id/ivStatus"
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:layout_marginEnd="4dp"
        android:contentDescription="@string/message_status"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="@+id/llVoiceContainer"
        app:layout_constraintEnd_toStartOf="@+id/llVoiceContainer"
        app:layout_constraintTop_toTopOf="@+id/llVoiceContainer"
        tools:src="@drawable/ic_sent" />

    <!-- 重发按钮 -->
    <ImageView
        android:id="@+id/ivResend"
        android:layout_width="18dp"
        android:layout_height="18dp"
        android:layout_marginEnd="4dp"
        android:contentDescription="@string/resend"
        android:src="@drawable/chat_alert"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/llVoiceContainer"
        app:layout_constraintEnd_toStartOf="@+id/llVoiceContainer"
        app:layout_constraintTop_toTopOf="@+id/llVoiceContainer" />

</androidx.constraintlayout.widget.ConstraintLayout> 