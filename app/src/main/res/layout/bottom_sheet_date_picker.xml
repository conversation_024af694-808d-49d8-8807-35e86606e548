<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingBottom="24dp">

    <!-- 顶部标题栏 -->
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="18dp"
        android:paddingStart="20dp"
        android:paddingEnd="20dp">

        <TextView
            android:id="@+id/btn_cancel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentStart="true"
            android:gravity="center_vertical"
            android:text="Cancel"
            android:textStyle="bold"
            android:textColor="@color/black_50"
            android:textSize="14sp" />


        <TextView
            android:id="@+id/btn_confirm"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:gravity="center_vertical"
            android:text="Confirm"
            android:textColor="@color/black"
            android:textSize="14sp"
            android:textStyle="bold" />
    </RelativeLayout>


    <!-- 日期选择器容器 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="horizontal"
        android:paddingTop="16dp"
        android:paddingBottom="16dp">
        <!-- 月份选择器 -->
        <NumberPicker
            android:id="@+id/month_picker"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1" />

        <!-- 日选择器 -->
        <NumberPicker
            android:id="@+id/day_picker"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1" />

        <!-- 年份选择器 -->
        <NumberPicker
            android:id="@+id/year_picker"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1" />
    </LinearLayout>

</LinearLayout> 