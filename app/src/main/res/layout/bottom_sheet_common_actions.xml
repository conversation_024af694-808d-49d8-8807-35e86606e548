<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/bottomSheetRoot"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bottom_sheet_rounded_top_bg">

    <View
        android:layout_width="match_parent"
        android:layout_height="55dp"
        android:background="@drawable/bottom_sheet_bg" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="35dp"
        android:layout_marginBottom="15dp"
        android:orientation="vertical"
        android:paddingHorizontal="16dp">

        <!-- 标题（可选） -->
        <TextView
            android:id="@+id/tv_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:gravity="center"
            android:text="Title"
            android:textColor="@color/black"
            android:textSize="16sp"
            android:textStyle="bold"
            android:visibility="gone" />

        <!-- 操作按钮容器 -->
        <LinearLayout
            android:id="@+id/action_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@android:color/transparent"
            android:orientation="vertical" />

        <View
            android:layout_width="match_parent"
            android:layout_height="17dp" />

        <!-- Cancel按钮 -->
        <com.score.callmetest.ui.widget.AlphaTextView
            android:id="@+id/btn_cancel"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:background="@drawable/bottom_sheet_button_bg"
            android:gravity="center"
            android:text="@string/str_btn_cancel"
            android:textColor="@android:color/white"
            android:textSize="15sp"
            android:textStyle="bold" />

    </LinearLayout>

</FrameLayout> 