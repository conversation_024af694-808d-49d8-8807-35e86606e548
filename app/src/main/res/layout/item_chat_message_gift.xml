<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingVertical="10dp">

    <!-- 礼物消息文本-左 -->
    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvGiftStartMessage"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="@string/send_gift_msg"
        android:textColor="@color/black_chat_gift"
        android:textSize="14sp"
        app:layout_constraintBottom_toBottomOf="@id/ivGift"
        app:layout_constraintEnd_toStartOf="@id/ivGift"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/ivGift" />

    <!-- 礼物图标 -->
    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivGift"
        android:layout_width="34dp"
        android:layout_height="34dp"
        android:layout_marginStart="2dp"
        android:layout_marginEnd="2dp"
        android:scaleType="centerCrop"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/tvGiftEndMessage"
        app:layout_constraintStart_toEndOf="@id/tvGiftStartMessage"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/blue_rose" />

    <!-- 礼物消息文本-右 -->
    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvGiftEndMessage"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:gravity="center"
        android:text="@string/send_gift_msg_suffix"
        android:textColor="@color/gray_chat_translated"
        android:textSize="16sp"
        app:layout_constraintBottom_toBottomOf="@id/ivGift"
        app:layout_constraintEnd_toStartOf="@id/ivFail"
        app:layout_constraintStart_toEndOf="@id/ivGift"
        app:layout_constraintTop_toTopOf="@id/ivGift" />

    <!-- 发送中状态 -->
    <ProgressBar
        android:id="@+id/progressSending"
        android:layout_width="19dp"
        android:layout_height="19dp"
        android:layout_marginStart="2dp"
        android:indeterminateTint="@color/gray_999"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/ivGift"
        app:layout_constraintStart_toEndOf="@id/tvGiftEndMessage"
        app:layout_constraintTop_toTopOf="@id/ivGift"
        tools:visibility="visible" />

    <!-- 失败图标 -->
    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivFail"
        android:layout_width="19dp"
        android:layout_height="19dp"
        android:layout_marginStart="2dp"
        android:src="@drawable/chat_alert"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/ivGift"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tvGiftEndMessage"
        app:layout_constraintTop_toTopOf="@id/ivGift"
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout> 