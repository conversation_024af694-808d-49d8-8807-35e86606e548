<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/bottomSheetRoot"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bottom_sheet_rounded_top_bg"
    android:orientation="vertical">

    <ImageView
        android:layout_width="match_parent"
        android:layout_height="55dp"
        android:scaleType="centerCrop"
        android:src="@drawable/bottom_sheet_bg" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        android:layout_marginTop="35dp"
        android:layout_marginBottom="15dp"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/report_options_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@android:color/transparent"
            android:orientation="vertical" />

        <View
            android:layout_width="match_parent"
            android:layout_height="16dp" />

        <com.score.callmetest.ui.widget.AlphaTextView
            android:id="@+id/btn_cancel"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:background="@drawable/bg_btn_rounded_blue"
            android:gravity="center"
            android:text="Cancel"
            android:textColor="@color/black"
            android:textSize="16sp"
            android:textStyle="bold" />

    </LinearLayout>

</RelativeLayout>