<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingStart="8dp"
    android:paddingTop="4dp"
    android:paddingEnd="60dp"
    android:paddingBottom="4dp">

    <!-- 头像 -->
    <com.score.callmetest.ui.widget.AlphaImageView
        android:id="@+id/ivAvatar"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginStart="8dp"
        android:layout_marginTop="16dp"
        android:contentDescription="@string/avatar"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/default_avatar" />

    <!-- 通话记录内容 -->
    <com.score.callmetest.ui.widget.AlphaLinearLayout
        android:id="@+id/callContentParent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="6dp"
        android:layout_marginTop="16dp"
        android:background="@drawable/bg_chat_bubble_left"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingStart="12dp"
        android:paddingTop="10dp"
        android:paddingEnd="12dp"
        android:paddingBottom="10dp"
        app:layout_constraintStart_toEndOf="@id/ivAvatar"
        app:layout_constraintTop_toTopOf="parent">

        <!-- 通话状态文本 -->
        <TextView
            android:id="@+id/tvCallStatus"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="8dp"
            android:fontFamily="@font/roboto_regular"
            android:textColor="@color/black"
            android:textSize="14sp"
            tools:text="Duration : 01:02:20" />

        <!-- 通话图标 -->
        <ImageView
            android:id="@+id/ivCallIcon"
            android:layout_width="22dp"
            android:layout_height="22dp"
            android:scaleType="center"
            tools:src="@drawable/call_chat_connected" />

    </com.score.callmetest.ui.widget.AlphaLinearLayout>

    <!-- 时间==暂时不需要显示时间 -->
    <TextView
        android:id="@+id/tvTime"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginTop="2dp"
        android:textColor="@color/gray_999"
        android:textSize="10sp"
        android:visibility="gone"
        app:layout_constraintStart_toEndOf="@+id/ivAvatar"
        app:layout_constraintTop_toBottomOf="@+id/callContentParent"
        tools:ignore="SmallSp"
        tools:text="10:30" />

</androidx.constraintlayout.widget.ConstraintLayout>
