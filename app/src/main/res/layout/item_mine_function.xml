<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="54dp"
    android:paddingStart="25dp"
    android:paddingEnd="25dp">

    <ImageView
        android:id="@+id/iv_icon"
        android:layout_width="26dp"
        android:layout_height="26dp"
        android:layout_centerVertical="true"
        android:src="@drawable/ic_level" />

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginStart="12dp"
        android:layout_toEndOf="@id/iv_icon"
        android:text="My Level"
        android:textColor="@color/black"
        android:typeface="sans"
        android:textSize="14sp"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/tv_tip"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_toStartOf="@+id/right_arrow"
        android:layout_centerVertical="true"
        android:layout_marginEnd="2dp"
        android:text="Level 5"
        android:textColor="#888888"
        android:textSize="14sp" />

    <ImageView
        android:id="@+id/right_arrow"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:src="@drawable/ic_chevron_right" />
</RelativeLayout>