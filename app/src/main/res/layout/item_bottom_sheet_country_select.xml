<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/item_bottom_sheet_country_select"
    android:layout_width="match_parent"
    android:layout_height="50dp"
    android:orientation="horizontal"
    android:clickable="true"
    android:focusable="true">

    <com.score.callmetest.ui.widget.CountryIconView
        android:id="@+id/iv_country_icon"
        android:layout_width="31dp"
        android:layout_height="31dp"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="15dp"
        android:scaleType="centerCrop"
        android:src="@drawable/all" />

    <TextView
        android:id="@+id/tv_flag_country_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="14sp"
        android:textStyle="bold"
        android:layout_marginStart="8dp"
        android:textColor="@color/black"
        android:layout_gravity="center_vertical"
        android:text="Argentina"
        android:maxLines="1" />

</LinearLayout>