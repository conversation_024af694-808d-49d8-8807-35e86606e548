<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingStart="8dp"
    android:paddingTop="4dp"
    android:paddingEnd="60dp"
    android:paddingBottom="4dp">

    <!-- 头像 -->
    <ImageView
        android:id="@+id/ivAvatar"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginStart="8dp"
        android:layout_marginTop="16dp"
        android:contentDescription="@string/avatar"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/default_avatar" />

    <!-- 用户名称 -->
  <!--  <TextView
        android:id="@+id/tvName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:textColor="@color/gray_666"
        android:textSize="12sp"
        app:layout_constraintStart_toEndOf="@+id/ivAvatar"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="User Name" />-->

    <!-- 语音消息容器 -->
    <LinearLayout
        android:id="@+id/llVoiceContainer"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="6dp"
        android:layout_marginTop="16dp"
        android:background="@drawable/bg_chat_bubble_left"
        android:gravity="center_vertical"
        android:minWidth="62dp"
        android:orientation="horizontal"
        android:padding="9dp"
        app:layout_constraintStart_toEndOf="@+id/ivAvatar"
        app:layout_constraintTop_toTopOf="parent">

    <!-- 语音图标 -->
        <com.opensource.svgaplayer.SVGAImageView
            android:id="@+id/ivVoice"
            android:layout_width="18dp"
            android:layout_height="18dp"
            android:layout_marginStart="3dp"
            android:src="@drawable/chart_voice_left"
            app:autoPlay="false"
            app:clearsAfterDetached="false" />
   <!--     <ImageView
            android:id="@+id/ivVoice"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_marginEnd="4dp"
            android:src="@drawable/chart_voice_left" />-->

        <!-- 语音时长 -->
        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvDuration"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:textColor="@color/black"
            android:textSize="15sp"
            tools:text="12'" />
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout> 