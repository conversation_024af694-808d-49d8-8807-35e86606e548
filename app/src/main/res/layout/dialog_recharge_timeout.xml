<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:background="@drawable/bg_dialog_rounded"
    android:padding="20dp"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <!-- 标题 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Order Issue Detected"
        android:textSize="18sp"
        android:textColor="#000"
        android:textStyle="bold"
        android:gravity="center"
        android:layout_marginBottom="16dp"/>

    <!-- 内容 -->
    <TextView
        android:id="@+id/tv_message"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="We've detected an issue with your order. Rest assured, we'll address this promptly. Should you not receive your coins within 3 minutes, kindly reach out to our Customer Service."
        android:textSize="14sp"
        android:textColor="#666"
        android:gravity="center"
        android:layout_marginBottom="20dp"/>

    <!-- 按钮区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center">

        <Button
            android:id="@+id/btn_customer_service"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Customer Service"
            android:textColor="#FFF"
            android:background="@drawable/bg_btn_rounded_blue"
            android:padding="12dp"
            android:layout_marginEnd="8dp"/>

        <Button
            android:id="@+id/btn_ok"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="OK"
            android:textColor="#FFF"
            android:background="@drawable/bg_btn_rounded_gray"
            android:padding="12dp"
            android:layout_marginStart="8dp"/>

    </LinearLayout>

</LinearLayout> 