<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/layout_empty_bg_broadcaster_detail_parent"
    android:layout_width="match_parent"
    android:layout_height="match_parent">


    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/layout_empty_bg_broadcaster_detail_img"
        android:layout_width="56dp"
        android:layout_height="56dp"
        android:scaleType="fitCenter"
        android:src="@drawable/icon_block"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.2" />

    <TextView
        android:id="@+id/layout_empty_bg_broadcaster_detail_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="14dp"
        android:gravity="center"
        android:text="@string/you_have_blocked_this_user"
        android:textColor="@color/black"
        android:textSize="14sp"
        android:textStyle="bold"
        app:layout_constrainedWidth="true"
        app:layout_constraintTop_toBottomOf="@+id/layout_empty_bg_broadcaster_detail_img"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>