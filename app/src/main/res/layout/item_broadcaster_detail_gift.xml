<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/broadcaster_detail_gift"
    android:layout_width="78dp"
    android:layout_height="91dp"
    android:background="@color/msg_incall">

    <ImageView
        android:id="@+id/iv_gift"
        android:layout_width="50dp"
        android:layout_height="50dp"
        android:layout_marginTop="10dp"
        android:layout_gravity="top|center_horizontal"
        android:scaleType="centerCrop"
        android:src="@drawable/gift_placehold" />

    <TextView
        android:id="@+id/tv_gift_count"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="64dp"
        android:background="@android:color/transparent"
        android:text="x10"
        android:textAlignment="center"
        android:textColor="#FF000000"
        android:textSize="12sp"
        android:textStyle="bold" />


</FrameLayout>