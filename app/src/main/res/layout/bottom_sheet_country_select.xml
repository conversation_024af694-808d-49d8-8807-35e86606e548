<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <!-- 顶部标题栏 -->
    <RelativeLayout
        android:id="@+id/top_title"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:paddingStart="22dp"
        android:paddingEnd="16dp"
        android:background="@drawable/login_dialog_bg">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:layout_alignParentStart="true"
            android:text="Select Regions"
            android:textStyle="bold"
            android:textColor="#FF000000"
            android:textSize="16sp"/>

        <ImageView
            android:id="@+id/image_cancel"
            android:layout_width="22dp"
            android:layout_height="22dp"
            android:layout_alignParentEnd="true"
            android:layout_marginTop="20dp"
            android:src="@drawable/cancel_country_select" />
    </RelativeLayout>


    <!-- 国家选择器 -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_country"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:layout_marginTop="7dp"
        android:overScrollMode="never"
        android:paddingTop="4dp"
        android:paddingStart="22dp"
        android:paddingEnd="22dp"/>
</LinearLayout>