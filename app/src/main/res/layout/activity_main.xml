<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:clipChildren="false"
    android:clipToPadding="false">

    <!-- Fragment容器 -->
    <FrameLayout
        android:id="@+id/fragment_container"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@+id/custom_bottom_tab"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <FrameLayout
        android:id="@+id/fab1_layout"
        android:layout_width="56dp"
        android:layout_height="56dp"
        android:layout_marginEnd="20dp"
        android:layout_marginBottom="15dp"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@+id/fab2_layout"
        app:layout_constraintEnd_toEndOf="parent">

        <!-- 悬浮按钮：促销弹窗 -->
        <com.opensource.svgaplayer.SVGAImageView
            android:id="@+id/fab1"
            android:layout_width="52dp"
            android:layout_height="52dp"
            android:layout_gravity="center_horizontal"
            android:clickable="true"
            android:focusable="true"
            android:scaleType="center"
            android:visibility="visible" />
        <!-- 计时器，只用TextView -->
        <TextView
            android:id="@+id/tv_fab1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom|center_horizontal"
            android:background="@android:color/transparent"
            android:paddingHorizontal="2dp"
            android:paddingVertical="2dp"
            android:text="Get Coins"
            android:textAlignment="center"
            android:textColor="@android:color/white"
            android:textSize="9sp"
            android:fontFamily="@font/roboto_medium" />
    </FrameLayout>

    <FrameLayout
        android:id="@+id/fab2_layout"
        android:layout_width="56dp"
        android:layout_height="56dp"
        android:layout_marginEnd="20dp"
        android:layout_marginBottom="100dp"
        android:visibility="visible"
        app:layout_constraintBottom_toTopOf="@+id/custom_bottom_tab"
        app:layout_constraintEnd_toEndOf="parent">

        <!-- 悬浮按钮：半屏弹窗 -->
        <com.score.callmetest.ui.widget.AlphaImageView
            android:id="@+id/fab2"
            android:layout_width="wrap_content"
            android:layout_height="48dp"
            android:layout_gravity="center_horizontal"
            android:clickable="true"
            android:focusable="true"
            android:scaleType="center"
            android:src="@drawable/coins6"
            android:visibility="visible" />

        <TextView
            android:id="@+id/tv_fab2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom|center_horizontal"
            android:background="@android:color/transparent"
            android:paddingHorizontal="2dp"
            android:paddingVertical="2dp"
            android:text="Get Coins"
            android:textAlignment="center"
            android:textColor="@android:color/white"
            android:textSize="9sp"
            android:fontFamily="@font/roboto_medium" />
    </FrameLayout>



    <!-- 自定义底部tab容器, Navigator方案有点概率性出现意外导航崩溃问题，不使用 -->
    <LinearLayout
        android:id="@+id/custom_bottom_tab"
        android:layout_width="0dp"
        android:layout_height="56dp"
        android:orientation="horizontal"
        android:background="@android:color/white"
        android:clipChildren="false"
        android:clipToPadding="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />


</androidx.constraintlayout.widget.ConstraintLayout>