<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@android:color/white"
    android:elevation="4dp"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tv_hidden"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="24dp"
        android:paddingTop="16dp"
        android:paddingEnd="24dp"
        android:paddingBottom="16dp"
        android:text="Hidden"
        android:textColor="#000000"
        android:textSize="16sp" />

    <TextView
        android:id="@+id/tv_delete"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="24dp"
        android:paddingTop="16dp"
        android:paddingEnd="24dp"
        android:paddingBottom="16dp"
        android:text="Delete"
        android:textColor="#000000"
        android:textSize="16sp" />

    <TextView
        android:id="@+id/tv_pin"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="24dp"
        android:paddingTop="16dp"
        android:paddingEnd="24dp"
        android:paddingBottom="16dp"
        android:text="Pin"
        android:textColor="#000000"
        android:textSize="16sp" />

</LinearLayout> 