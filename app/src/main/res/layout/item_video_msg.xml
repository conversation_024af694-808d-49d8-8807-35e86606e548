<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/shape_video_msg_bg"
    android:paddingHorizontal="15dp"
    android:paddingVertical="10dp">

    <!-- 第一行  -->
    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_message"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:textColor="@color/video_msg_item_name"
        android:textSize="14sp"
        app:layout_constrainedWidth="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Ben:HITexttext！ HITexttext！ HIT" />

    <!-- 分割线和下方内容整体包裹，方便一起控制显示/隐藏，group_bottom.setVisibility(View.GONE)或View.VISIBLE -->
    <androidx.constraintlayout.widget.Group
        android:id="@+id/group_bottom"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="divider,tv_message_translate" />

    <!-- 分割线 -->
    <View
        android:id="@+id/divider"
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:layout_marginTop="10dp"
        android:background="@color/video_msg_item_divide"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_message"
        tools:visibility="visible" />

    <!-- 第二行 -->
    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_message_translate"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:gravity="center_vertical"
        android:textColor="@color/video_msg_item_name"
        android:textSize="14sp"
        android:visibility="gone"
        app:layout_constrainedWidth="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/divider"
        tools:text="Ben:HITexttext！ HITexttext！ HIT"
        tools:visibility="visible" />


</androidx.constraintlayout.widget.ConstraintLayout>