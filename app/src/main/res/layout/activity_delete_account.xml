<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/home_bg"
    android:orientation="vertical">


    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:elevation="0dp">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="44dp">

            <com.score.callmetest.ui.widget.AlphaImageView
                android:id="@+id/iv_back"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:layout_centerVertical="true"
                android:src="@drawable/nav_btn_back" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:text="Delete Account"
                android:textColor="@android:color/black"
                android:textSize="18sp"
                android:textStyle="bold" />
        </RelativeLayout>
    </androidx.appcompat.widget.Toolbar>

    <LinearLayout
        android:id="@+id/line_context"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="20dp"
        android:layout_marginStart="29dp"
        android:layout_marginEnd="29dp"
        android:orientation="vertical">
        <TextView
            android:id="@+id/tv_text1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:fontFamily="@font/roboto_regular"
            android:textSize="14sp"
            android:gravity="start"
            android:textColor="#282828"
            android:text="@string/delete_account_text1"/>

        <TextView
            android:id="@+id/tv_text2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="22dp"
            android:fontFamily="@font/roboto_regular"
            android:textSize="14sp"
            android:gravity="start"
            android:textColor="#282828"
            android:text="@string/delete_account_text2"/>

        <TextView
            android:id="@+id/tv_text3"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="22dp"
            android:fontFamily="@font/roboto_regular"
            android:textSize="14sp"
            android:gravity="start"
            android:textColor="#282828"
            android:text="@string/delete_account_text3"/>

        <!-- 添加一个占位用的 Space，让下面的按钮可以推到底部 -->
        <Space
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1" />

        <com.score.callmetest.ui.widget.AlphaLinearLayout
            android:id="@+id/btn_cancel_layout"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:layout_marginTop="24dp"
            android:layout_marginHorizontal="15dp"
            android:background="@drawable/bg_btn_rounded_blue"
            android:gravity="center"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/btn_cancel"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="Cancel"
                android:textColor="#fff"
                android:textSize="15sp"
                android:textStyle="bold" />
        </com.score.callmetest.ui.widget.AlphaLinearLayout>

        <TextView
            android:id="@+id/btn_delete"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="23dp"
            android:layout_marginBottom="40dp"
            android:textColor="#FF4F4F"
            android:textAlignment="center"
            android:fontFamily="@font/roboto_bold"
            android:textStyle="bold"
            android:textSize="14sp"
            android:text="@string/delete_account_text" />

    </LinearLayout>
</LinearLayout>