<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="match_parent"
    android:background="@android:color/transparent">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="29dp"
        android:background="@drawable/bg_dialog_rounded">

        <ImageView
            android:layout_width="match_parent"
            android:layout_height="90dp"
            android:background="@drawable/login_dialog_bg" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingHorizontal="32dp"
            android:paddingTop="73dp"
            android:paddingBottom="25dp">


            <TextView
                android:id="@+id/title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:gravity="center"
                android:text="Thank you for taking the time to review these Terms"
                android:textColor="@color/black"
                android:textSize="18sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="14dp"
                android:lineHeight="17dp"
                android:text="We hope you have a great experience with our app.  By using our app, you are agreeing to our Terms  Conditions and Privacy Policy."
                android:textColor="#808080"
                android:textSize="14sp"
                android:fontFamily="@font/roboto_medium"
                android:textStyle="normal" />

            <com.score.callmetest.ui.widget.AlphaTextView
                android:id="@+id/btn_agree"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:layout_marginTop="24dp"
                android:background="@drawable/bg_btn_rounded_blue"
                android:gravity="center"
                android:text="Agree"
                android:textColor="#000000"
                android:textSize="15sp"
                android:textStyle="bold" />

            <com.score.callmetest.ui.widget.AlphaTextView
                android:id="@+id/btn_cancel"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:layout_marginTop="12dp"
                android:background="@drawable/bg_btn_rounded_black"
                android:gravity="center"
                android:text="Cancel"
                android:textColor="#FFFFFF"
                android:textSize="15sp"
                android:textStyle="bold" />

        </LinearLayout>

    </FrameLayout>

    <ImageView
        android:id="@+id/emoji"
        android:layout_width="wrap_content"
        android:layout_height="91dp"
        android:layout_gravity="center_horizontal"
        android:src="@drawable/emoji_laugh" />
</FrameLayout>