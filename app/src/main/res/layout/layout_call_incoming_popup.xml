<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/root_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#661B1B27">

    <androidx.cardview.widget.CardView
        android:id="@+id/contentArea"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginHorizontal="22dp"
        android:background="@android:color/white"
        app:cardCornerRadius="18dp">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <ImageView
                android:id="@+id/ivAvatar"
                android:layout_width="332dp"
                android:layout_height="332dp"
                android:layout_centerHorizontal="true"
                android:scaleType="centerCrop" />

            <View
                android:id="@+id/bottom_shadow"
                android:layout_width="match_parent"
                android:layout_height="130dp"
                android:layout_alignBottom="@+id/ivAvatar" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_alignStart="@+id/ivAvatar"
                android:layout_alignBottom="@+id/ivAvatar"
                android:layout_marginStart="15dp"
                android:layout_marginBottom="15dp"
                android:gravity="center_vertical"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tvNickname"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:ellipsize="marquee"
                    android:focusable="true"
                    android:includeFontPadding="false"
                    android:singleLine="true"
                    android:textColor="@android:color/white"
                    android:textSize="15sp"
                    android:textStyle="bold" />

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="7dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:id="@+id/age_layout"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="6dp"
                        android:gravity="center"
                        android:orientation="horizontal"
                        android:paddingHorizontal="8dp"
                        android:paddingVertical="2dp">

                        <ImageView
                            android:id="@+id/age_icon"
                            android:layout_width="14dp"
                            android:layout_height="14dp"
                            android:src="@drawable/chat_girl"
                            android:tintMode="src_in"
                            app:tint="@android:color/white" />

                        <TextView
                            android:id="@+id/tv_age"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="22"
                            android:textColor="@android:color/white"
                            android:textSize="12sp"
                            android:textStyle="bold"
                            android:typeface="sans" />
                    </LinearLayout>

                    <TextView
                        android:id="@+id/tv_country"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:paddingHorizontal="8dp"
                        android:paddingVertical="2dp"
                        android:text="India"
                        android:textColor="@android:color/white"
                        android:textSize="12sp"
                        android:textStyle="bold"
                        android:typeface="sans" />
                </LinearLayout>
            </LinearLayout>

            <TextView
                android:id="@+id/tv_tip"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/ivAvatar"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="20dp"
                android:textColor="@color/black"
                android:textSize="14sp"
                android:textStyle="bold"
                android:typeface="sans" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/tv_tip"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="15dp"
                android:layout_marginBottom="15dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <com.score.callmetest.ui.widget.AlphaImageView
                    android:id="@+id/btnReject"
                    android:layout_width="95dp"
                    android:layout_height="95dp"
                    android:layout_marginEnd="17dp"
                    android:padding="18dp"
                    android:src="@drawable/ic_hangup" />

                <com.score.callmetest.ui.widget.AlphaSVGAImageView
                    android:id="@+id/btnAccept"
                    android:layout_width="95dp"
                    android:layout_height="95dp" />
            </LinearLayout>
        </RelativeLayout>

    </androidx.cardview.widget.CardView>

</FrameLayout>