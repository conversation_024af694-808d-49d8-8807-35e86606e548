<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_mine_function_item_selector">

    <!-- 头像 -->
    <com.score.callmetest.ui.widget.CircleIconButton
        android:id="@+id/iv_avatar"
        android:layout_width="52dp"
        android:layout_height="52dp"
        android:layout_marginVertical="12dp"
        android:layout_marginStart="15dp"
        android:scaleType="centerCrop"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/placeholder" />
    <!-- 在线状态指示器 -->
    <View
        android:id="@+id/status_indicator"
        android:layout_width="12dp"
        android:layout_height="12dp"
        android:layout_marginEnd="2dp"
        android:layout_marginBottom="2dp"
        android:background="@drawable/shape_online_status"
        app:layout_constraintBottom_toBottomOf="@id/iv_avatar"
        app:layout_constraintEnd_toEndOf="@id/iv_avatar" />

    <!-- 用户名和通话类型图标的水平容器 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/username_call_type_container"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="15dp"
        android:layout_marginEnd="3dp"
        android:orientation="horizontal"
        app:layout_constraintBottom_toTopOf="@id/tv_call_type"
        app:layout_constraintEnd_toStartOf="@id/tv_timestamp"
        app:layout_constraintStart_toEndOf="@id/iv_avatar"
        app:layout_constraintTop_toTopOf="@id/iv_avatar">

        <!-- 用户名 -->
        <TextView
            android:id="@+id/tv_username"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="1"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constrainedWidth="true"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toStartOf="@id/iv_call_type"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:textColor="@color/black"
            android:textSize="15sp"
            android:textStyle="bold"
            tools:text="aaaa" />

        <!-- 通话类型图标 -->
        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/iv_call_type"
            android:layout_width="22dp"
            android:layout_height="22dp"
            android:layout_marginStart="5dp"
            android:scaleType="fitCenter"
            android:layout_marginEnd="10dp"
            app:layout_constraintStart_toEndOf="@id/tv_username"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            tools:src="@drawable/call_connected" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- 通话类型和时长文本 -->
    <TextView
        android:id="@+id/tv_call_type"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/call_text_gray"
        android:textSize="14sp"
        android:layout_marginEnd="3dp"
        app:layout_constraintBottom_toBottomOf="@id/iv_avatar"
        app:layout_constraintEnd_toStartOf="@id/tv_timestamp"
        app:layout_constraintStart_toStartOf="@id/username_call_type_container"
        app:layout_constraintTop_toBottomOf="@id/username_call_type_container"
        tools:text="Outgoing call: 20s" />

    <!-- 视频通话标识 -->
    <com.score.callmetest.ui.widget.AlphaImageView
        android:id="@+id/iv_video_indicator"
        android:layout_width="38dp"
        android:layout_height="38dp"
        android:layout_marginEnd="18dp"
        android:scaleType="fitCenter"
        android:src="@drawable/video_gray"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- 时间戳 -->
    <TextView
        android:id="@+id/tv_timestamp"
        android:layout_width="64dp"
        android:layout_height="19dp"
        android:layout_marginEnd="15dp"
        android:textColor="@color/call_text_gray"
        android:textSize="12sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/iv_video_indicator"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="2023.02.05" />

</androidx.constraintlayout.widget.ConstraintLayout>