<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- 弹出菜单动画样式 -->
    <style name="PopupAnimation">
        <item name="android:windowEnterAnimation">@anim/popup_enter</item>
        <item name="android:windowExitAnimation">@anim/popup_exit</item>
    </style>
    <!-- switch禁用 Ripple 效果 -->
    <style name="NoRippleSwitch" parent="Widget.AppCompat.CompoundButton.Switch">
        <item name="android:background">@null</item>
        <item name="android:foreground">@null</item>
    </style>

    <style name="CardView" parent="android:Widget">
        <item name="cardCornerRadius">2dp</item>
        <item name="cardElevation">2dp</item>
        <item name="cardMaxElevation">2dp</item>
        <item name="cardBackgroundColor">@color/cardview_light_background</item>
    </style>
</resources>