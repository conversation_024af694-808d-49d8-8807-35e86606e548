package com.score.callmetest.ui.widget

import android.app.Dialog
import android.content.DialogInterface
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isVisible
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.score.callmetest.R
import com.score.callmetest.databinding.DialogCoinRechargeBinding
import com.score.callmetest.entity.RechargeSource
import com.score.callmetest.manager.DualChannelEventManager
import com.score.callmetest.manager.FlashChatManager
import com.score.callmetest.manager.StrategyManager
import com.score.callmetest.manager.UserInfoManager
import com.score.callmetest.ui.chat.ChatActivity
import com.score.callmetest.util.CustomUtils
import com.score.callmetest.util.click
import timber.log.Timber

class CoinRechargeDialog(val source: RechargeSource,var bcInvitationId: String? = null) : BottomSheetDialogFragment() {
    private var _binding: DialogCoinRechargeBinding? = null
    private val binding get() = _binding!!

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View? {
        _binding = DialogCoinRechargeBinding.inflate(inflater, container, false)
        val coinBalance = UserInfoManager.myUserInfo?.availableCoins ?: 0

        binding.tvCoinBalance.text = " " + coinBalance.toString()

        if (StrategyManager.isReviewPkg()) {
            binding.coinService.visibility = View.GONE
        }

        binding.coinService.click {
            ChatActivity.start(context, CustomUtils.provideCustomService())
        }

        binding.rechargeSubView.bcInvitationId = bcInvitationId
        if(!bcInvitationId.isNullOrEmpty()){
            binding.rechargeSubView.refresh{
                binding.rechargeSubView.isVisible = true
            }
        }else {
            binding.rechargeSubView.isVisible = true
        }
        binding.rechargeSubView.entry = source

        // 监听金币余额
        DualChannelEventManager.observeAvailableCoins(this) { availableCoinsMessage ->
            // 更新余额
            val availableCoins = availableCoinsMessage.coins?:0
            UserInfoManager.myUserInfo?.availableCoins = availableCoins
            binding.tvCoinBalance.text = " $availableCoins"
        }

        return binding.root
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
        FlashChatManager.startShowDialogTimer()
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        FlashChatManager.stopShowDialogTimer()
        val dialog = super.onCreateDialog(savedInstanceState)
        dialog.setOnShowListener {
            val bottomSheet = (dialog as? BottomSheetDialog)?.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet)
            bottomSheet?.setBackgroundResource(android.R.color.transparent)
        }
        return dialog
    }
}