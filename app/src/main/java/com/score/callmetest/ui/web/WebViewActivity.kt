package com.score.callmetest.ui.web

import android.annotation.SuppressLint
import android.os.Build
import android.util.Log
import android.view.View
import android.view.ViewGroup
import android.webkit.JavascriptInterface
import android.webkit.WebView
import android.webkit.WebViewClient
import androidx.activity.addCallback
import androidx.lifecycle.lifecycleScope
import com.google.gson.Gson
import com.score.callmetest.BuildConfig
import com.score.callmetest.R
import com.score.callmetest.databinding.ActivityWebViewBinding
import com.score.callmetest.entity.H5LogEventEntity
import com.score.callmetest.entity.RechargeSource
import com.score.callmetest.manager.GlobalManager
import com.score.callmetest.manager.RechargeManager
import com.score.callmetest.manager.StrategyManager
import com.score.callmetest.manager.UserInfoManager
import com.score.callmetest.ui.base.BaseActivity
import com.score.callmetest.ui.base.EmptyViewModel
import com.score.callmetest.ui.chat.ChatActivity
import com.score.callmetest.ui.widget.CoinRechargeDialog
import com.score.callmetest.ui.widget.InsufficientBalanceDialog
import com.score.callmetest.util.CustomUtils
import com.score.callmetest.utils.WebViewMemoryLeakUtils
import kotlinx.coroutines.launch
import timber.log.Timber

/**
 * WebView Activity
 *
 * 内存泄漏防护措施：
 * 1. 在onDestroy中清理WebView相关资源
 * 2. 移除JavaScript接口
 * 3. 停止WebView加载并清理
 * 4. 从父容器中移除WebView
 * 5. 销毁WebView实例
 */
class WebViewActivity : BaseActivity<ActivityWebViewBinding, EmptyViewModel>() {

    companion object {
        private const val NAME = "JSBridgeService"
        const val FROM = "from"
    }

    // 使用弱引用避免内存泄漏
    private var webView: WebView? = null
    private var customWebClient: CustomWebClient? = null

    private var mFrom: String? = null
    private val mGson = Gson()

    private var mIsNeedFinish = false

    override fun getViewBinding(): ActivityWebViewBinding {
        return ActivityWebViewBinding.inflate(layoutInflater)
    }

    override fun getViewModelClass() = EmptyViewModel::class.java

    override fun initView() {
        GlobalManager.addViewStatusBarTopMargin(this, binding.webView)
        // 初始化WebView引用
        webView = binding.webView

        onBackPressedDispatcher.addCallback(this){
            if (webView?.canGoBack() == true && !mIsNeedFinish) {
                webView?.goBack()
            } else {
                beforeFinish()
            }
        }

        setupWebView()
//        setupToolbar()
    }

    @SuppressLint("SetJavaScriptEnabled")
    private fun setupWebView() {
        val url = intent.getStringExtra("url") ?: "https://example.com"
        val title = intent.getStringExtra("title") ?: getString(R.string.app_name)
        mFrom = intent.getStringExtra(FROM)

        binding.toolbar.title = title

        webView?.apply {
            // 使用工具类配置WebView以优化内存使用
            WebViewMemoryLeakUtils.configureWebViewForMemoryOptimization(this, enableCache = false)

            // 添加JavaScript接口
            addJavascriptInterface(this@WebViewActivity, NAME)

            // 设置WebViewClient
            customWebClient = CustomWebClient(this@WebViewActivity)
            webViewClient = customWebClient!!

            // 加载URL
            loadUrl(url)
        }
    }

    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        supportActionBar?.setDisplayShowHomeEnabled(true)

        binding.toolbar.setNavigationOnClickListener {
            onBackPressedDispatcher.onBackPressed()
        }
    }

    private fun beforeFinish() {
        if (mFrom == "recharge") {
            try {
                // 调整轮询策略
                // dsc--注释--可能暂时不用，勿删
//                RechargeManager.adjustPollingStrategyOnPaymentReturn()
            } catch (e: Exception) {
                Timber.e(e, "调整轮询策略时出现异常")
            }
        }
        finish()
    }

    // <editor-folder desc="JSBridgeService">

    //关闭当前webView
    @JavascriptInterface
    fun newTppClose() {
        //需要实现  关闭当前应用内WebView
        Timber.d("jscall newTppClose...")
        // 在主线程中执行finish操作
        runOnUiThread {
            beforeFinish()
        }
    }
    
    //webview事件回调
    /**
     *  {"event":"page_load_start","code":1755136489000,"orderId":"","ext":"h5_payment_process"}
     *  {"event":"page_load_finish","code":1755136489000,"orderId":"","ext":"h5_payment_process","price":0}
     *  {"event":"page_load_finish","code":1755136490000,"orderId":"44562547fd81410e9240b3027feaa0c4","ext":"h5_payment_error","price":0}
     *  {"event":"success","code":"a4c34a0a-1c3e-4d13-955e-9ec163464631","orderId":"8ff88814ad854d17a3f14195b019ea12","ext":"sucess"}
     *  {"event":"payment_success","code":"263002","orderId":"8ff88814ad854d17a3f14195b019ea12","price":4.99,"ext":"h5_payment_success"}
     *  {"event":"success","code":"263002","orderId":"8ff88814ad854d17a3f14195b019ea12","price":4.99,"ext":"sucess"}
     *  {"event":"close_page","code":"263002","orderId":"8ff88814ad854d17a3f14195b019ea12","price":4.99,"ext":"h5_payment_success"}
     */
    @JavascriptInterface
    fun newTppLogEvent(json: String?) {
        Timber.d("jscall newTppLogEvent...$json")
        try {
            val event = mGson.fromJson(json, H5LogEventEntity::class.java)
            when(event.event){
                "page_load_finish" -> {
                    if(event.ext == "h5_payment_error"){
                        // 加载失败--返回可以直接finish
                        mIsNeedFinish = true
                    }
                }
                "payment_success" -> {
                    // 支付成功--返回可以直接finish
                    mIsNeedFinish = true
                }
            }
        } catch (e: Exception) {
            Timber.e(e)
        }
    }

    //打开官方客服私聊页
    @JavascriptInterface
    fun openVipService() {
        Timber.d("jscall openVipService...")
        // 先查缓存，再网络查询
        // 使用lifecycleScope确保在Activity生命周期内执行
        lifecycleScope.launch {
            runOnUiThread {
                ChatActivity.start(this@WebViewActivity, CustomUtils.provideCustomService())
                finish()
            }
        }
    }
    
   //提供支持api
    @JavascriptInterface
    fun getSupportApi(): String {
       Timber.d("jscall getSupportApi...")
        val api = arrayListOf("newTppClose", "newTppLogEvent", "openVipService", "recharge", "rechargeSource")
        return Gson().toJson(api)
    }
    
    //调起充值页面
    @JavascriptInterface
    fun recharge() {
        Timber.d("jscall recharge...")
        runOnUiThread {
            CoinRechargeDialog(RechargeSource.JACKPOT_RECHARGE).show(supportFragmentManager, "coin_recharge")
        }
    }
    
    //获取充值来源
    @JavascriptInterface
    fun rechargeSource(json: String?) {
        Timber.d("jscall rechargeSource...$json")
    }

    //</editor-folder>

    override fun onPause() {
        super.onPause()
        // 使用工具类暂停WebView以节省资源
        WebViewMemoryLeakUtils.pauseWebView(webView)
    }

    override fun onResume() {
        super.onResume()
        // 使用工具类恢复WebView
        WebViewMemoryLeakUtils.resumeWebView(webView)
    }

    override fun onDestroy() {
        Timber.d("WebViewActivity onDestroy - 开始清理WebView资源")

        try {
            // 清理CustomWebClient
            customWebClient?.cleanup()
            customWebClient = null

            // 使用工具类清理WebView资源，防止内存泄漏
            WebViewMemoryLeakUtils.cleanupWebView(webView, NAME)

            // 清空引用
            webView = null

            Timber.d("WebViewActivity onDestroy - WebView资源清理完成")

        } catch (e: Exception) {
            Timber.e(e, "WebViewActivity onDestroy - 清理WebView资源时出现异常")
        } finally {
            // 确保调用父类的onDestroy
            super.onDestroy()
        }
    }

}