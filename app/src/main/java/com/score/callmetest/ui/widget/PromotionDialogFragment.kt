package com.score.callmetest.ui.widget

import android.content.DialogInterface
import android.graphics.Color
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.graphics.drawable.toDrawable
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.activityViewModels
import com.opensource.svgaplayer.SVGAParser
import com.score.callmetest.R
import com.score.callmetest.ui.main.PromotionViewModel
import com.score.callmetest.util.CustomUtils
import com.score.callmetest.util.GlideUtils
import com.score.callmetest.util.click
import timber.log.Timber

/**
 * 促销弹窗辅助类，方便在任何地方调用显示促销弹窗
 * 目前有两个xml：
 * 1、dialog_promotion.xml - 新用户促销弹窗，包含svga_discount动画
 * 2、dialog_promotion1.xml - 活动促销弹窗，包含tv_day和tv_add_amount
 *
 * 动画文件：
 * - 按钮动画：btn_promotion.svga
 * - 折扣标签动画：discount_tags.svga（仅dialog_promotion.xml使用）
 *
 * 倒计时使用：tv_hour（时）、tv_min（分）、tv_seconds（秒）
 * 价格显示：tv_price（现价）、tv_old_price（原价）
 */
/**
 * 显示促销弹窗
 *
 * @param fragmentManager FragmentManager
 * @param amount 金额文本
 * @param addAmount 额外金额文本（带+号）
 * @param description 描述文本
 * @param price 当前价格
 * @param layoutResId 布局资源ID
 * @param buttonSvgaName 按钮SVGA动画名称
 * @param buttonEffectSvgaName 按钮特效SVGA动画名称
 * @param remainingCount 剩余数量
 * @param treasureBoxImageUrl 宝箱图片URL，如果为空则使用默认图片
 * @param onButtonClickListener 按钮点击回调
 * @param goodsInfoType 商品类型，用于从CountdownManager获取倒计时
 * @param goodsInfoCode 商品code，用于从CountdownManager获取倒计时
 */
class PromotionDialogFragment : DialogFragment() {

    private var amount: String = ""
    private var addAmount: String = ""
    private var description: String = ""
    private var price: String = ""
    private var oldPrice: String = ""
    private var buttonSvgaName: String = "btn_promotion.svga"
    private var buttonEffectSvgaName: String = "discount_tags.svga"
    private var remainingCount: Int = 0
    private var onButtonClickListener: (() -> Unit)? = null
    private var layoutResId: Int = R.layout.dialog_promotion
    private var treasureBoxImageUrl: String? = null

    // CountdownManager相关属性
    private var goodsInfoType: String? = null
    private var goodsInfoCode: String? = null

    // Shared ViewModel
    private val promotionViewModel: PromotionViewModel by activityViewModels()

    companion object {
        fun newInstance(
            layoutResId: Int = R.layout.dialog_promotion,
            amount: String,
            description: String,
            price: String = "",
            originPrice: String = "",
            buttonSvgaName: String = "btn_promotion.svga",
            buttonEffectSvgaName: String = "discount_tags.svga",
            onButtonClickListener: (() -> Unit)? = null,
            addAmount: String = "",
            remainingCount: Int = 0,
            treasureBoxImageUrl: String? = null,
            goodsInfoType: String,
            goodsInfoCode: String
        ): PromotionDialogFragment {
            return PromotionDialogFragment().apply {
                this.layoutResId = layoutResId
                this.amount = amount
                this.addAmount = addAmount
                this.description = description
                this.price = price
                this.oldPrice = originPrice
                this.buttonSvgaName = buttonSvgaName
                this.buttonEffectSvgaName = buttonEffectSvgaName
                this.onButtonClickListener = onButtonClickListener
                this.remainingCount = remainingCount
                this.treasureBoxImageUrl = treasureBoxImageUrl
                this.goodsInfoType = goodsInfoType
                this.goodsInfoCode = goodsInfoCode
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        // 禁止点击外部和返回键关闭
        isCancelable = false
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        dialog?.requestWindowFeature(Window.FEATURE_NO_TITLE)
        dialog?.window?.setBackgroundDrawable(Color.TRANSPARENT.toDrawable())
        return inflater.inflate(layoutResId, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        // 设置弹窗大小
        dialog?.window?.setLayout(
            ViewGroup.LayoutParams.WRAP_CONTENT,
            ViewGroup.LayoutParams.WRAP_CONTENT
        )

        // 设置蒙层透明度
        dialog?.window?.setDimAmount(0.6f)

        // 初始化视图
        setupViews(view)

        // 加载SVGA动画
        loadSvgaAnimations(view)

        // 观察共享的 ViewModel
        setupViewModelObserver(view)
    }

    private fun setupViews(view: View) {
        // 设置宝箱图片 - 支持网络图片加载
        val ivTreasureBox = view.findViewById<ImageView>(R.id.iv_treasure_box)
        ivTreasureBox?.let {
            //todo  促销活动，图片下发
            if (!treasureBoxImageUrl.isNullOrEmpty()) {
                // 使用网络图片
                GlideUtils.load(
                    context = requireContext(),
                    url = treasureBoxImageUrl,
                    imageView = it,
                    placeholder = R.drawable.promotion_action_src,
                    error = R.drawable.promotion_action_src
                )
            }
        }

        // 设置金额文本
        val tvAmount = view.findViewById<TextView>(R.id.tv_amount)
        tvAmount?.text = amount

        // 设置额外金额文本
        val tvAddAmount = view.findViewById<TextView>(R.id.tv_add_amount)
        tvAddAmount?.text = addAmount

        // 设置描述文本
  /*      val tvDescription = view.findViewById<TextView>(R.id.tv_title)
        if (description.isEmpty()) {
            tvDescription.visibility = View.GONE
        } else {
            tvDescription.visibility = View.VISIBLE
            tvDescription?.text = description
        }*/

        // 设置价格信息
        setupPriceInfo(view)

        val price_layout = view.findViewById<LinearLayout>(R.id.price_layout)
        price_layout.click {
            onButtonClickListener?.invoke()
            dismiss()
        }

        // 设置活动天数 - 仅在 dialog_promotion1.xml 中存在
        val tvDay = view.findViewById<TextView>(R.id.tv_day)
        tvDay?.let {
            if (remainingCount > 0) {
                it.text = "Only $remainingCount left"
                it.visibility = View.VISIBLE
            } else {
                it.visibility = View.GONE
            }
        }

        // 设置关闭按钮点击事件
        val ivClose = view.findViewById<ImageView>(R.id.iv_close)
        ivClose?.click {
            dismiss()
        }
    }

    private fun setupPriceInfo(view: View) {
        // 设置价格文本 - 现价
        val tvPrice = view.findViewById<TextView>(R.id.tv_price)
        tvPrice?.text = price

        // 设置原价文本 - 原价
        val tvOldPrice = view.findViewById<TextView>(R.id.tv_old_price)
        // 设置旧价格信息
        setupOldPrice(
            priceTextView = tvOldPrice
        )
    }

    /**
     * 设置旧价格的显示和下划线
     */
    private fun setupOldPrice(priceTextView: TextView?) {
        if (priceTextView == null) return

        if (oldPrice.isNotEmpty() && CustomUtils.comparePrice(oldPrice, price) > 0) {
            priceTextView.text = oldPrice
            priceTextView.paint.isStrikeThruText = true
            priceTextView.visibility = View.VISIBLE
        } else {
            priceTextView.text = ""
            priceTextView.paint.isStrikeThruText = false
            priceTextView.visibility = View.GONE
        }
    }

    private fun loadSvgaAnimations(view: View) {
        val parser = SVGAParser(requireContext())

        // 加载按钮动画 - 使用 btn_promotion.svga
        val svgaButton = view.findViewById<AlphaSVGAImageView>(R.id.svga_button)
        svgaButton.click {
            onButtonClickListener?.invoke()
            dismiss()
        }
        CustomUtils.playSvga(svgaButton, "btn_promotion.svga")

        // 加载折扣标签动画 - 仅在 dialog_promotion.xml 中存在
        val svgaDiscount = view.findViewById<AlphaSVGAImageView>(R.id.svga_discount)
        svgaDiscount?.let {
            CustomUtils.playSvga(it, "discount_tags.svga", loops = 1)
        }
    }

    private fun setupViewModelObserver(view: View) {
        val tvHour = view.findViewById<TextView>(R.id.tv_hour)
        val tvMin = view.findViewById<TextView>(R.id.tv_min)
        val tvSeconds = view.findViewById<TextView>(R.id.tv_seconds)

        if (tvHour == null || tvMin == null || tvSeconds == null) return

        promotionViewModel.countdownText.observe(viewLifecycleOwner) { timeString ->
            // timeString 的格式是 HH:MM:SS 或 MM:SS
            val parts = timeString.split(":")
            if (parts.size == 3) {
                tvHour.text = parts[0]
                tvMin.text = parts[1]
                tvSeconds.text = parts[2]
            } else if (parts.size == 2) {
                tvHour.text = "00"
                tvMin.text = parts[0]
                tvSeconds.text = parts[1]
            }
        }

        promotionViewModel.isCountdownFinished.observe(viewLifecycleOwner) {
            if (it) {
                tvHour.text = "00"
                tvMin.text = "00"
                tvSeconds.text = "00"
                // （可选）倒计时结束后可以关闭对话框
                // dismiss()
            }
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        // ViewModel 观察者的清理由 Lifecycle 组件自动处理。
        Timber.tag("PromotionDialog").d("onDestroyView 已调用。")
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        // 通知父Fragment弹窗已关闭
        parentFragmentManager.setFragmentResult("PromotionDialog_dismissed", Bundle())
    }



}