package com.score.callmetest.ui.preview

import android.content.Context
import android.graphics.Matrix
import android.graphics.PointF
import android.util.AttributeSet
import android.view.GestureDetector
import android.view.MotionEvent
import android.view.ScaleGestureDetector
import androidx.appcompat.widget.AppCompatImageView
import kotlin.math.max
import kotlin.math.min

class ZoomImageView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null
) : AppCompatImageView(context, attrs) {
    private val matrix = Matrix()
    private var mode = NONE
    private var last = PointF()
    private var start = PointF()
    private var minScale = 1f
    private var maxScale = 4f
    private var m: FloatArray = FloatArray(9)
    private var viewWidth = 0
    private var viewHeight = 0
    private var saveScale = 1f
    private var origWidth = 0f
    private var origHeight = 0f
    private var oldMeasuredWidth = 0
    private var oldMeasuredHeight = 0

    private val scaleDetector = ScaleGestureDetector(context, object : ScaleGestureDetector.SimpleOnScaleGestureListener() {
        override fun onScale(detector: ScaleGestureDetector): Boolean {
            var scale = detector.scaleFactor
            val origScale = saveScale
            saveScale *= scale
            if (saveScale > maxScale) {
                saveScale = maxScale
                scale = maxScale / origScale
            } else if (saveScale < minScale) {
                saveScale = minScale
                scale = minScale / origScale
            }
            matrix.postScale(scale, scale, detector.focusX, detector.focusY)
            fixTrans()
            imageMatrix = matrix
            return true
        }
    })

    private val gestureDetector = GestureDetector(context, object : GestureDetector.SimpleOnGestureListener() {
        override fun onDoubleTap(e: MotionEvent): Boolean {
            if (saveScale > minScale) {
                resetZoom()
            } else {
                val scale = maxScale / saveScale
                matrix.postScale(scale, scale, e.x, e.y)
                saveScale = maxScale
                fixTrans()
                imageMatrix = matrix
            }
            return true
        }
    })

    init {
        super.setClickable(true)
        scaleType = ScaleType.MATRIX
        imageMatrix = matrix
        setOnTouchListener { _, event ->
            if (parent != null) {
                if (saveScale > 1f || event.pointerCount > 1) {
                    parent.requestDisallowInterceptTouchEvent(true)
                } else {
                    parent.requestDisallowInterceptTouchEvent(false)
                }
            }
            scaleDetector.onTouchEvent(event)
            gestureDetector.onTouchEvent(event)
            val curr = PointF(event.x, event.y)
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    last.set(curr)
                    start.set(last)
                    mode = DRAG
                }
                MotionEvent.ACTION_MOVE -> if (mode == DRAG) {
                    val dx = curr.x - last.x
                    val dy = curr.y - last.y
                    matrix.postTranslate(dx, dy)
                    fixTrans()
                    last.set(curr.x, curr.y)
                    imageMatrix = matrix
                }
                MotionEvent.ACTION_UP, MotionEvent.ACTION_POINTER_UP -> mode = NONE
            }
            true
        }
    }

    override fun setImageResource(resId: Int) {
        super.setImageResource(resId)
        resetZoom()
    }

    override fun setImageBitmap(bm: android.graphics.Bitmap?) {
        super.setImageBitmap(bm)
        resetZoom()
    }

    override fun setImageDrawable(drawable: android.graphics.drawable.Drawable?) {
        super.setImageDrawable(drawable)
        resetZoom()
    }

    private fun resetZoom() {
        if (drawable == null) return
        val bmWidth = drawable.intrinsicWidth
        val bmHeight = drawable.intrinsicHeight
        val scale: Float = min(viewWidth.toFloat() / bmWidth, viewHeight.toFloat() / bmHeight)
        matrix.reset()
        matrix.setScale(scale, scale)
        val redundantYSpace = (viewHeight.toFloat() - scale * bmHeight) / 2f
        val redundantXSpace = (viewWidth.toFloat() - scale * bmWidth) / 2f
        matrix.postTranslate(redundantXSpace, redundantYSpace)
        saveScale = 1f
        imageMatrix = matrix
    }

    private fun fixTrans() {
        matrix.getValues(m)
        val transX = m[Matrix.MTRANS_X]
        val transY = m[Matrix.MTRANS_Y]
        val fixTransX = getFixTrans(transX, viewWidth.toFloat(), origWidth * saveScale)
        val fixTransY = getFixTrans(transY, viewHeight.toFloat(), origHeight * saveScale)
        if (fixTransX != 0f || fixTransY != 0f) {
            matrix.postTranslate(fixTransX, fixTransY)
        }
        imageMatrix = matrix
    }

    private fun getFixTrans(trans: Float, viewSize: Float, contentSize: Float): Float {
        return when {
            contentSize <= viewSize -> (viewSize - contentSize) / 2 - trans
            trans > 0 -> -trans
            trans < viewSize - contentSize -> viewSize - contentSize - trans
            else -> 0f
        }
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        viewWidth = MeasureSpec.getSize(widthMeasureSpec)
        viewHeight = MeasureSpec.getSize(heightMeasureSpec)
        if (oldMeasuredWidth == viewWidth && oldMeasuredHeight == viewHeight || viewWidth == 0 || viewHeight == 0)
            return
        oldMeasuredWidth = viewWidth
        oldMeasuredHeight = viewHeight
        if (drawable == null) return
        val bmWidth = drawable.intrinsicWidth
        val bmHeight = drawable.intrinsicHeight
        val scale: Float = min(viewWidth.toFloat() / bmWidth, viewHeight.toFloat() / bmHeight)
        matrix.reset()
        matrix.setScale(scale, scale)
        val redundantYSpace = (viewHeight.toFloat() - scale * bmHeight) / 2f
        val redundantXSpace = (viewWidth.toFloat() - scale * bmWidth) / 2f
        matrix.postTranslate(redundantXSpace, redundantYSpace)
        origWidth = viewWidth - 2 * redundantXSpace
        origHeight = viewHeight - 2 * redundantYSpace
        saveScale = 1f
        imageMatrix = matrix
    }

    companion object {
        private const val NONE = 0
        private const val DRAG = 1
    }
} 