package com.score.callmetest.ui.login

import android.os.Build
import android.provider.Settings
import android.util.Base64
import android.util.Log
import androidx.lifecycle.viewModelScope
import com.score.callmetest.BuildConfig
import com.score.callmetest.CallmeApplication
import com.score.callmetest.Constant
import com.score.callmetest.R
import com.score.callmetest.manager.AppConfigManager
import com.score.callmetest.manager.StrategyManager
import com.score.callmetest.network.LoginData
import com.score.callmetest.network.LoginRequest
import com.score.callmetest.network.RetrofitUtils
import com.score.callmetest.network.RiskInfo
import com.score.callmetest.network.StrategyConfig
import com.score.callmetest.ui.base.BaseViewModel
import com.score.callmetest.util.AESUtils
import com.score.callmetest.util.DeviceUtils
import com.score.callmetest.util.GPSUtils
import com.score.callmetest.util.InputMethodUtils
import com.score.callmetest.util.ProxyUtils
import com.score.callmetest.util.SIMUtils
import com.score.callmetest.util.SharePreferenceUtil
import com.score.callmetest.util.ThreadUtils
import com.score.callmetest.util.TimeZoneUtils

import kotlinx.coroutines.launch
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import retrofit2.HttpException
import timber.log.Timber
import java.io.IOException

class LoginViewModel : BaseViewModel() {
    // 首次/非首次注册逻辑
    val isFirstLogin = SharePreferenceUtil.getBoolean(Constant.KEY_IS_FIRST_LOGIN, true)
    val lastLoginType = SharePreferenceUtil.getString(Constant.KEY_LAST_LOGIN_TYPE, null)

    fun saveLoginData(token: String, isFirstLogin: Boolean, loginType: String) {
        SharePreferenceUtil.putString(Constant.TOKEN_KEY, token)
        SharePreferenceUtil.putString(Constant.KEY_LAST_LOGIN_TYPE, loginType)
    }

    fun login(
        oauthType: Int = 4,
        token: String,
        onSuccess: (LoginData) -> Unit,
        onError: (String) -> Unit
    ) {
        // 先获取配置
        AppConfigManager.getAppConfig(
            onSuccess = { configData ->
                AppConfigManager.saveAppConfig(configData)
                doLogin(oauthType, token, onSuccess, onError)
            },
            onError = { errorMsg ->
                Timber.e(errorMsg)
                val errorMsg = if(errorMsg == "-1") CallmeApplication.context.getString(R.string.login_error)
                else errorMsg
                onError(errorMsg)
            }
        )
    }

} 