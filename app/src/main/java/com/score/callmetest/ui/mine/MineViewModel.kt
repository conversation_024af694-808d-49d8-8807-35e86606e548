package com.score.callmetest.ui.mine

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.score.callmetest.network.NetworkResult
import com.score.callmetest.network.RelationsCounter
import com.score.callmetest.network.RetrofitUtils
import com.score.callmetest.network.relationsCounterRequest
import com.score.callmetest.ui.base.BaseViewModel
import kotlinx.coroutines.launch

class MineViewModel : BaseViewModel() {

    private val dataRepository = RetrofitUtils.dataRepository

    private val _relationsCounter = MutableLiveData<RelationsCounter>()
    val relationsCounter: LiveData<RelationsCounter> = _relationsCounter

    private val _loading = MutableLiveData<Boolean>()
    val loading: LiveData<Boolean> = _loading

    private val _error = MutableLiveData<String>()
    val error: LiveData<String> = _error

    /**
     * 获取关系数量统计
     */
    fun getRelationsCounter() {
        viewModelScope.launch {
            _loading.value = true
            try {
                val timestamp = System.currentTimeMillis()
                val request = relationsCounterRequest(timestamp)
                when (val result = dataRepository.getRelationsCounter(request)) {
                    is NetworkResult.Success -> {
                        result.data?.let { counter ->
                            _relationsCounter.value = counter
                        }
                    }
                    is NetworkResult.Error -> {
                        _error.value = result.message
                    }
                }
            } catch (e: Exception) {
                _error.value = "获取关系数量失败: ${e.message}"
            } finally {
                _loading.value = false
            }
        }
    }
}