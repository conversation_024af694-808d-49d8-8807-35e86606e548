package com.score.callmetest.ui.mine.mycard

import android.graphics.Typeface
import android.graphics.drawable.GradientDrawable
import android.view.View
import android.view.ViewTreeObserver
import android.widget.TextView
import androidx.core.graphics.toColorInt
import androidx.fragment.app.Fragment
import androidx.viewpager2.adapter.FragmentStateAdapter
import androidx.viewpager2.widget.ViewPager2
import com.google.android.material.tabs.TabLayout
import com.score.callmetest.R
import com.score.callmetest.databinding.ActivityMycardBinding
import com.score.callmetest.manager.GlobalManager
import com.score.callmetest.ui.base.BaseActivity
import com.score.callmetest.util.ClickUtils
import com.score.callmetest.util.DisplayUtils
import com.score.callmetest.util.DrawableUtils
import timber.log.Timber

/**
 * 我的卡片页面的独立Activity
 * 直接管理TabLayout和ViewPager2，不再使用中间的MyCardFragment容器
 */
class MyCardActivity : BaseActivity<ActivityMycardBinding, MyCardViewModel>() {

    private lateinit var mPagerAdapter: MyCardPagerAdapter

    override fun getViewBinding(): ActivityMycardBinding {
        return ActivityMycardBinding.inflate(layoutInflater)
    }

    override fun getViewModelClass() = MyCardViewModel::class.java

    override fun initView() {
        try {
            // 设置背景渐变
            binding.mycardTopRoot.background = DrawableUtils.createGradientDrawable(
                colors = intArrayOf("#FFFFFF".toColorInt(), "#9F48FF".toColorInt()),
                orientation = GradientDrawable.Orientation.BOTTOM_TOP,
                radius = DisplayUtils.dp2pxInternalFloat(12f)
            )

            setupTabLayout()
            setupViewPager()

            // 设置默认显示第一个页面
            binding.viewPager.post {
                binding.viewPager.setCurrentItem(0, false)
                binding.tabFollow.getTabAt(0)?.select()
            }
        } catch (e: Exception) {
            Timber.tag("MyCardActivity").w("Error in initView: $e")
        }
    }

    private fun setupTabLayout() {
        with(binding.tabFollow) {
            tabRippleColor = null
            // 添加Match Card标签（目前只有一个，后续可以添加更多）
            addTab(newTab().apply {
                customView = createCustomTabView("Match Card", selected = true)
            })
            // TODO: 后续可以在这里添加更多Tab
            // addTab(newTab().apply {
            //     customView = createCustomTabView("Other Card", selected = false)
            // })
        }
    }

    private fun setupViewPager() {
        // 初始化ViewPager适配器
        mPagerAdapter = MyCardPagerAdapter(this)

        // 配置ViewPager
        binding.viewPager.apply {
            adapter = mPagerAdapter
            offscreenPageLimit = 1 // 目前只有一个页面，后续可以调整
        }
        GlobalManager.setNeverOverScroll(binding.viewPager)
    }

    override fun initListener() {
        // 返回按钮点击事件
        ClickUtils.setOnIsolatedClickListener(binding.btnReturn) {
            onBackPressedDispatcher.onBackPressed()
        }

        // 监听Tab切换
        binding.tabFollow.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab) {
                updateTabStyle(tab.customView, true)
                binding.viewPager.currentItem = tab.position
            }

            override fun onTabUnselected(tab: TabLayout.Tab) {
                updateTabStyle(tab.customView, false)
            }

            override fun onTabReselected(tab: TabLayout.Tab) {
                // 不需要处理
            }
        })

        // 监听ViewPager页面切换
        binding.viewPager.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                binding.tabFollow.getTabAt(position)?.select()
            }
        })
    }

    /**
     * 创建自定义Tab视图
     * @param text Tab文本
     * @param customView 现有的自定义视图（如果有）
     * @param selected 是否选中
     * @return 自定义Tab视图
     */
    private fun createCustomTabView(text: String? = null, customView: View? = null, selected: Boolean = false): View {
        val view = customView ?: layoutInflater.inflate(R.layout.tab_custom, null)
        updateTabStyle(view, selected, text)
        return view
    }

    /**
     * 更新Tab样式
     * @param view Tab视图
     * @param selected 是否选中
     * @param text Tab文本（可选）
     */
    private fun updateTabStyle(view: View?, selected: Boolean, text: String? = null) {
        if (view == null) return

        val textView = view.findViewById<TextView>(R.id.tab_text)
        val indicator = view.findViewById<View>(R.id.tab_indicator)

        // 设置文本（如果提供）
        text?.let { textView?.text = it }

        if (selected) {
            // 选中状态：15sp，加粗，显示指示器
            textView?.textSize = 15f
            textView?.setTypeface(Typeface.create("sans-serif", Typeface.BOLD))
            indicator?.visibility = View.VISIBLE
        } else {
            // 未选中状态：15sp，正常，隐藏指示器
            textView?.textSize = 15f
            textView?.setTypeface(Typeface.create("sans-serif", Typeface.NORMAL))
            indicator?.visibility = View.GONE
        }
    }

    /**
     * ViewPager适配器
     */
    private inner class MyCardPagerAdapter(activity: MyCardActivity) : FragmentStateAdapter(activity) {
        override fun getItemCount(): Int = 1 // 目前只有一个页面，后续可以增加

        override fun createFragment(position: Int): Fragment {
            return when (position) {
                0 -> MatchCardFragment()
                // TODO: 后续可以在这里添加更多Fragment
                // 1 -> OtherCardFragment()
                else -> throw IllegalArgumentException("Invalid position: $position")
            }
        }
    }
}