package com.score.callmetest.ui.widget

import android.annotation.SuppressLint
import android.app.Dialog
import android.content.Context
import android.graphics.Color
import android.graphics.Typeface
import android.graphics.drawable.Drawable
import android.graphics.drawable.GradientDrawable
import android.os.Bundle
import android.text.Spanned
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.annotation.DrawableRes
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.core.graphics.toColorInt
import androidx.core.view.doOnPreDraw
import com.google.common.collect.BiMap
import com.score.callmetest.R
import com.score.callmetest.databinding.DialogCustomBinding
import com.score.callmetest.databinding.DialogFlashChatBinding
import com.score.callmetest.entity.RechargeSource
import com.score.callmetest.manager.FlashChatManager
import com.score.callmetest.manager.GlobalManager
import com.score.callmetest.manager.UserInfoManager
import com.score.callmetest.ui.match.MatchActivity
import com.score.callmetest.util.ActivityUtils
import com.score.callmetest.util.CustomUtils
import com.score.callmetest.util.DisplayUtils
import com.score.callmetest.util.DrawableUtils
import com.score.callmetest.util.click

class FlashChatDialog(
    val activity: AppCompatActivity,
    val isFree: Boolean,
    val unitPrice: Int = 0,
) : Dialog(activity, androidx.appcompat.R.style.Theme_AppCompat_Dialog) {
    private var binding: DialogFlashChatBinding = DialogFlashChatBinding.inflate(LayoutInflater.from(context))

    @SuppressLint("SetTextI18n")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)

        // 设置全屏/居中
        window?.setLayout(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.WRAP_CONTENT
        )
        // 隐藏状态栏
        window?.decorView?.systemUiVisibility = (
                View.SYSTEM_UI_FLAG_FULLSCREEN or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                )
        window?.setBackgroundDrawableResource(R.color.transparent)
        // 点击空白区域可取消
//        setCanceledOnTouchOutside(false)

        binding.dialogBg.setBackgroundResource(R.drawable.dialog_bg_green)

        // 设置内容左右37dp边距
        val paddingPx = (37 * context.resources.displayMetrics.density).toInt()
        binding.root.setPadding(
            paddingPx,
            binding.root.paddingTop,
            paddingPx,
            binding.root.paddingBottom
        )

        CustomUtils.playSvga(binding.avatarsSvga, "match_avatars.svga")

        binding.title.setTypeface(Typeface.create("sans-serif", Typeface.BOLD));
        binding.content.setTypeface(Typeface.create("sans-serif", Typeface.NORMAL));

        CustomUtils.playSvga(binding.videoSvga, "info_video.svga")
        binding.btnConfirm.setTypeface(Typeface.create("sans-serif", Typeface.BOLD));

        binding.close.click {
            dismiss()
        }

        if (isFree) {
            binding.dialogBg.setBackgroundResource(R.drawable.dialog_bg_green)
            val time = "${FlashChatManager.matchFreeTimes}"
            val content =
                "You have $time free times to use!"
            val spannable = android.text.SpannableString(content)
            val startIndex = content.indexOf(time.toString())
            val endIndex = startIndex + time.toString().length
            spannable.setSpan(
                android.text.style.ForegroundColorSpan("#FF6400".toColorInt()),
                startIndex,
                endIndex,
                android.text.Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
            )
            binding.content.text = spannable
            binding.tvTip.visibility = View.GONE
            binding.btnConfirm.text = "Free Match"

            binding.btnConfirmLayout.doOnPreDraw {
                binding.btnConfirmLayout.background = DrawableUtils.createGradientDrawable(
                    colors = intArrayOf("#4FFF60".toColorInt(), "#00B8D3".toColorInt()),
                    orientation = GradientDrawable.Orientation.TOP_BOTTOM,
                    radius = binding.btnConfirmLayout.height / 2f
                )
            }

            binding.btnConfirmLayout.click {
                ActivityUtils.getTopActivity()?.let {
                    try {
                        FlashChatManager.gotoMatchPage(it as AppCompatActivity, isFree = true)
                    } catch (e : Exception) {
                        e.printStackTrace()
                    }
                }
                dismiss()
            }
        } else {
            binding.dialogBg.setBackgroundResource(R.drawable.login_dialog_bg)
            binding.content.text = "Start call to meet some interesting girls randomly"

            binding.btnConfirm.text = "Random Match"
            val spannable = android.text.SpannableString("(  ${unitPrice}/20s)")
            val drawable = ContextCompat.getDrawable(context, R.drawable.coin)
            drawable?.setBounds(0, 0, DisplayUtils.dp2px(14f), DisplayUtils.dp2px(14f))
            val imageSpan = drawable?.let { android.text.style.ImageSpan(it, android.text.style.ImageSpan.ALIGN_BOTTOM) }
            if (imageSpan != null) {
                spannable.setSpan(imageSpan, 1, 2, android.text.Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
            }
            binding.tvTip.visibility = View.VISIBLE
            binding.tvTip.text = spannable

            binding.btnConfirmLayout.doOnPreDraw {
                binding.btnConfirmLayout.background = DrawableUtils.createGradientDrawable(
                    colors = intArrayOf("#FFDC20".toColorInt(), "#FF462F".toColorInt()),
                    orientation = GradientDrawable.Orientation.TOP_BOTTOM,
                    radius = binding.btnConfirmLayout.height / 2f
                )
            }

            UserInfoManager.myUserInfo?.availableCoins?.let {
                if (it > unitPrice) {
                    binding.btnConfirmLayout.click {
                        ActivityUtils.getTopActivity()?.let {
                            try {
                                FlashChatManager.gotoMatchPage(it as AppCompatActivity, isFree = false)
                            } catch (e : Exception) {
                                e.printStackTrace()
                            }
                        }
                    }
                } else {
                    binding.btnConfirmLayout.click {
                        CoinRechargeDialog(RechargeSource.FLASH).show(activity.supportFragmentManager, "coin_recharge")
                    }
                }
            }
        }
    }
}