# 消息弹窗功能说明

## 功能概述

收到新融云message事件时，会在应用顶部显示一个弹窗，用户可以点击弹窗快速跳转到聊天页面。

## 主要组件

### 1. MessageIncomingManager
- **位置**: `app/src/main/java/com/score/callmetest/ui/message/MessageIncomingManager.kt`
- **功能**: 管理消息弹窗的显示和隐藏逻辑
- **特性**:
  - 直接监听融云消息事件（使用RongCloudManager.addOnReceiveMessageListener）
  - 支持悬浮窗和DialogFragment两种显示方案
  - 新消息覆盖旧消息内容，无需重新弹窗
  - 支持上滑手势收起弹窗
  - 自动3秒后消失
  - 线程安全处理
  - 使用Timber记录日志

### 2. MessageIncomingPopupView
- **位置**: `app/src/main/java/com/score/callmetest/ui/message/MessageIncomingPopupView.kt`
- **功能**: 消息弹窗的UI视图
- **UI元素**:
  - 头像（圆形）
  - 昵称
  - 消息内容（最多2行）
  - 右侧箭头图标
  - 关闭按钮

### 3. MessageIncomingDialogFragment
- **位置**: `app/src/main/java/com/score/callmetest/ui/message/MessageIncomingDialogFragment.kt`
- **功能**: 无悬浮窗权限时使用的DialogFragment方案

### 4. 布局文件
- **位置**: `app/src/main/res/layout/layout_message_incoming_popup.xml`
- **特性**:
  - 白色背景，圆角设计
  - 顶部显示，左右边距16dp
  - 响应式布局，适配不同屏幕

## 使用方式

### 自动初始化
在`BaseActivity`中已经自动初始化，无需手动调用：

```kotlin
// 在BaseActivity.doBaseLogic()中
MessageIncomingManager.initialize()
```

管理器使用`RongCloudManager.addOnReceiveMessageListener`直接监听融云消息，只需要初始化一次。

### 显示条件
弹窗会在以下条件下显示：

#### 实时消息弹窗
1. 收到新的融云消息（支持多种消息类型）
2. 不是当前用户发送的消息
3. 应用在前台
4. 来电弹窗未显示（来电弹窗优先级更高）
5. 不在屏蔽页面（VideoCallActivity、CoinStoreActivity）
6. MsgListFragment未可见
7. 如果在聊天页面，只有当消息不是来自当前聊天对象时才显示

#### APP前台切换弹窗
1. APP从后台切换到前台
2. 存在后台接收的未展示消息且消息时间不超过30分钟
3. 满足上述实时消息弹窗的所有屏蔽条件
4. 每条消息最多只展示一次

### 消息更新机制
- 如果弹窗已经在显示，新消息会直接更新弹窗内容
- 不会重复弹出新的弹窗，避免干扰用户体验

### 消息展示状态管理
#### 1. 唯一性保证
- 每条消息生成唯一ID：`${senderUserId}_${contentHash}_${sentTime}`
- 使用`shownMessageIds`集合记录已展示的消息
- 每条消息最多只展示一次弹窗

#### 2. 后台消息收集
- 应用在后台时，收到的消息存储在`backgroundReceivedMessages`列表
- 前台切换时，只处理后台收集的未展示消息
- 自动清理超过30分钟的后台消息

#### 3. 前台切换逻辑
- APP从后台切换到前台时触发检查
- 获取最新的后台接收消息
- 检查消息是否已展示过
- 使用统一的条件判断逻辑检查是否应该显示
- 满足条件时显示弹窗并标记为已展示
- 清空后台消息列表

#### 4. 统一的条件判断
- 提取了`shouldShowMessagePopup()`方法统一处理显示条件
- 避免了`onReceiveMessage`和`onAppBecameForeground`中的重复逻辑
- 支持区分实时消息和后台消息的不同处理

#### 5. 消息列表集成
- 统一在MessageIncomingManager中处理消息接收
- 自动转换融云消息为MessageListEntity
- 自动更新或创建数据库记录
- 通过回调机制通知MsgListViewModel更新UI

#### 6. 聊天消息集成
- 统一在MessageIncomingManager中处理聊天消息
- 自动过滤当前聊天对象的消息
- 支持的消息类型：TEXT、IMAGE、VOICE、GIFT、LINK、FILE
- 通过回调机制通知ChatViewModel更新聊天界面

### 支持的消息类型
| 消息类型 | 融云类型 | 显示内容 | 说明 |
|---------|---------|---------|------|
| TEXT | TextMessage | 消息文本内容 | 直接显示文本内容 |
| IMAGE | ImageMessage | [Image] | 显示本地化的图片标识 |
| VOICE | HQVoiceMessage | [Voice] | 显示本地化的语音标识 |
| LINK | HyperLinkMsg | [Link] | 显示本地化的链接标识 |
| GIFT | SingleJsonMsg | [Gift] | 显示本地化的礼物标识 |
| FILE | FileMessage | [File] | 显示本地化的文件标识 |
| VIDEO | - | [Video] | 预留视频消息类型 |
| CARD | - | [Recharge Card] | 预留充值卡片类型 |
| SYSTEM | - | - | 预留系统消息类型 |

### 用户交互
- **点击弹窗内容**: 跳转到对应的聊天页面（使用系统的click/longClick机制自动区分）
- **点击关闭按钮**: 关闭弹窗
- **上滑手势**: 收起弹窗，弹窗会跟随手指滑动，向上拖拽超过100px自动收起
- **拖拽时**: 暂停自动消失倒计时，松手后重新开始倒计时
- **Touch事件穿透**: 弹窗不会阻挡底层页面的滑动操作
- **状态栏透明**: 弹窗显示时状态栏保持透明，不会闪烁或变黑
- **3秒后自动消失**: 无操作时自动隐藏

## 权限处理

### 有悬浮窗权限
使用`WindowManager`在系统级别显示弹窗，可以覆盖在其他应用之上。

### 无悬浮窗权限
使用`DialogFragment`在应用内显示弹窗，只在应用前台时可见。

## 技术特性

### 线程安全
- 使用`MainScope`处理协程
- UI操作都在主线程执行
- 使用`ThreadUtils`确保线程安全

### 内存管理
- 使用`WeakReference`避免内存泄漏
- 及时清理资源和监听器

### 日志记录
- 使用Timber记录详细日志
- 便于调试和问题排查

## 自定义配置

### 修改自动消失时间
```kotlin
// 在MessageIncomingManager中修改
private const val AUTO_DISMISS_DELAY = 3000L // 3秒
```

### 点击检测机制
现在使用Android系统的click/longClick机制，无需手动配置时间阈值。系统会自动区分：
- 快速点击 → 触发click事件
- 长按或拖拽 → 触发longClick事件（被我们消费掉，不执行任何操作）

### 屏蔽规则详解

#### 1. 页面级屏蔽
以下页面会自动屏蔽消息弹窗：
- **VideoCallActivity**: 视频通话页面
- **CoinStoreActivity**: 充值页面
- **MsgListFragment**: 消息列表页面（resume状态时）

#### 2. 来电弹窗优先级
- 来电弹窗显示时，会立即隐藏消息弹窗
- 来电弹窗存在时，不会显示新的消息弹窗
- 通过`CallIncomingManager.isShowing()`检查来电弹窗状态

#### 3. 聊天页面智能屏蔽
- 在聊天页面时，只屏蔽当前聊天对象的消息
- 其他用户的消息仍会显示弹窗
- 使用ChatActivity的静态方法获取当前聊天用户ID，避免反射

### 扩展新消息类型
要添加新的消息类型支持，需要在以下位置进行修改：

1. **MessageType枚举** (`ChatMessageEntity.kt`):
```kotlin
enum class MessageType {
    // ... 现有类型
    NEW_TYPE,  // 添加新类型
}
```

2. **MessageIncomingManager.getMessageTypeAndContent()方法**:
```kotlin
is NewMessageClass -> {
    val displayText = CallmeApplication.context.getString(R.string.msg_list_type_new)
    MessageType.NEW_TYPE to displayText
}
```

3. **添加对应的字符串资源** (`strings.xml`):
```xml
<string name="msg_list_type_new">[New Type]</string>
```

### 消息列表集成架构

#### 1. 统一消息处理流程
```
融云消息 → MessageIncomingManager → 数据库操作 → 回调通知 → MsgListViewModel → UI更新
```

#### 2. 数据库操作
- **查询**: 检查用户是否已存在消息记录
- **更新**: 更新已存在用户的消息信息（最后消息、未读数等）
- **插入**: 为新用户创建消息记录

#### 3. 回调机制
```kotlin
// 设置消息列表回调
MessageIncomingManager.setMessageListCallback { messageListEntity ->
    // 处理消息列表更新
}

// 设置聊天消息回调
MessageIncomingManager.setChatMessageCallback { message ->
    // 处理聊天消息更新
}

// 清理回调
MessageIncomingManager.setMessageListCallback(null)
MessageIncomingManager.setChatMessageCallback(null)
```

### 聊天页面用户ID管理

#### 1. ChatActivity静态方法
```kotlin
companion object {
    @Volatile
    private var currentChatUserId: String? = null

    fun getCurrentChatUserId(): String? = currentChatUserId
    internal fun setCurrentChatUserId(userId: String?) {
        currentChatUserId = userId
    }
}
```

#### 2. 生命周期管理
```kotlin
// onCreate中设置
setCurrentChatUserId(mTargetUserInfo?.userId)

// onDestroy中清理
setCurrentChatUserId(null)
```

#### 3. MessageIncomingManager中使用
```kotlin
private fun getCurrentChatUserId(chatActivity: ChatActivity): String? {
    return ChatActivity.getCurrentChatUserId()
}
```

### 扩展屏蔽规则
要添加新的页面屏蔽规则：

1. **Activity级屏蔽** - 在MessageIncomingManager中添加：
```kotlin
when(topActivity){
    is VideoCallActivity,
    is CoinStoreActivity,
    is NewActivity -> {  // 添加新的Activity
        Timber.tag(TAG).d("In blocked activity: ${topActivity.javaClass.simpleName}")
        return
    }
}
```

2. **Fragment级屏蔽** - 参考MsgListFragment的实现：
```kotlin
// 在Fragment的onResume中
MessageIncomingManager.setFragmentResumed(true)

// 在Fragment的onPause中
MessageIncomingManager.setFragmentResumed(false)
```

### 修改弹窗样式
编辑布局文件：`app/src/main/res/layout/layout_message_incoming_popup.xml`

### 修改背景样式
编辑drawable文件：`app/src/main/res/drawable/shape_message_popup_bg.xml`

## 注意事项

1. **聊天页面智能显示**: 在聊天页面时，只有当消息不是来自当前聊天对象时才显示弹窗
2. **消息更新机制**: 新消息会覆盖旧消息内容，不会重复弹窗
3. **权限适配**: 自动适配有无悬浮窗权限的情况
4. **手势支持**: 支持上滑手势快速收起弹窗，弹窗会跟随手指滑动
5. **Touch事件穿透**: 使用FLAG_NOT_TOUCH_MODAL确保底层页面可以正常滑动
6. **拖拽时倒计时控制**: 拖拽时暂停自动消失倒计时，松手后重新开始
7. **状态栏处理**: 使用FLAG_LAYOUT_IN_SCREEN和FLAG_LAYOUT_NO_LIMITS确保状态栏透明且不闪烁
8. **点击检测**: 使用Android系统的click/longClick机制，设置空的longClick监听器让系统自动区分点击和拖拽
9. **页面屏蔽机制**: 在特定页面（VideoCallActivity、CoinStoreActivity、MsgListFragment可见时）自动屏蔽弹窗
10. **来电弹窗优先级**: 来电弹窗显示时会自动隐藏消息弹窗，来电弹窗存在时不显示消息弹窗
11. **APP前台切换弹窗**: 从后台切换到前台时，显示30分钟内的后台接收且未展示的消息
12. **消息展示状态管理**: 每条消息最多只展示一次，避免重复弹窗
13. **后台消息收集**: 应用在后台时收集消息，前台时统一处理
14. **消息列表集成**: 统一处理消息接收，自动更新MessageListEntity并存储到数据库
15. **聊天消息集成**: 统一处理聊天消息，通过回调通知ChatViewModel更新聊天界面
16. **回调机制**: 通过回调方式通知MsgListViewModel和ChatViewModel，避免重复监听
16. **Fragment可见性检测**: 综合isResumed、userVisibleHint、isHidden三个状态精确检测Fragment真实可见性
17. **生命周期管理**: 在用户登出时自动清理资源，避免内存泄漏
18. **支持多种消息类型**: 支持TEXT、IMAGE、VOICE、LINK、GIFT、FILE等消息类型，可扩展
