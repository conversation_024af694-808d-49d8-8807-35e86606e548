package com.score.callmetest.ui.mycoin

import android.graphics.Color
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import androidx.core.graphics.toColorInt
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.GridLayoutManager
import com.score.callmetest.R
import com.score.callmetest.databinding.FragmentMyCoinBinding
import com.score.callmetest.entity.RechargeSource
import com.score.callmetest.manager.BannerManager
import com.score.callmetest.manager.DualChannelEventManager
import com.score.callmetest.manager.GlobalManager
import com.score.callmetest.manager.GoodsManager
import com.score.callmetest.manager.RechargeManager
import com.score.callmetest.manager.UserInfoManager
import com.score.callmetest.network.GoodsInfo
import com.score.callmetest.ui.base.BaseFragment
import com.score.callmetest.ui.widget.GridSpacingItemDecoration
import com.score.callmetest.ui.main.RechargeOption
import com.score.callmetest.ui.main.RechargeOptionAdapter
import com.score.callmetest.ui.widget.Helper.PagerHelper
import com.score.callmetest.util.DisplayUtils
import com.score.callmetest.util.DrawableUtils
import com.score.callmetest.util.ToastUtils
import kotlinx.coroutines.launch
import timber.log.Timber

class MyCoinFragment : BaseFragment<FragmentMyCoinBinding, MyCoinViewModel>() {

    private var rechargeAdapter: RechargeOptionAdapter? = null

    override fun getViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?
    ): FragmentMyCoinBinding {
        return FragmentMyCoinBinding.inflate(inflater, container, false)
    }

    override fun getViewModelClass() = MyCoinViewModel::class.java

    override fun initView() {
        GlobalManager.addViewStatusBarTopMargin(requireActivity(), binding.llTopBar)
        setupBanner()
        setupRechargeOptions()
        updateCoinBalance()
    }

    override fun initData() {
        loadRechargeOptions()
    }

    override fun initListener() {
        super.initListener()
        setupObservers()
    }

    /**
     * 设置 Banner 轮播图
     */
    private fun setupBanner() {
        BannerManager.getBannerInfo(
            scope = lifecycleScope,
            callback = { bannerList ->
                Timber.d("MyCoinFragment banner data received: ${bannerList?.size} items")
                if (!bannerList.isNullOrEmpty()) {
                    Timber.d("MyCoinFragment setting up banner with ${bannerList.size} items")
                    // 使用 PagerHelper 设置轮播图
                    PagerHelper.setupBannerPager(
                        viewPager = binding.bannerViewPager,
                        bannerList = bannerList,
                        enableLoop = true,
                        enableAutoScroll = true,
                        autoScrollInterval = 3000L,
                        onItemClick = { realIndex ->
                            if (realIndex < bannerList.size) {
                                BannerManager.handleBannerClick(
                                    context = requireContext(),
                                    bannerInfoResponse = bannerList[realIndex]
                                )
                            }
                        },
                        onPageSelected = { realIndex ->
                            Timber.d("MyCoinFragment onPageSelected: realIndex=$realIndex")
                            updateDots(bannerList.size, realIndex)
                        }
                    )
                    // 延迟初始化小圆点，确保 ViewPager 已经设置完成
                    binding.bannerViewPager.post {
                        updateDots(bannerList.size, 0)
                    }
                } else {
                    Timber.d("MyCoinFragment banner list is empty")
                    binding.lineBanner.visibility = View.GONE
                }
            }
        )
    }

    /**
     * 更新轮播图小圆点
     */
    private fun updateDots(totalCount: Int, currentIndex: Int) {
        Timber.d("MyCoinFragment updateDots: totalCount=$totalCount, currentIndex=$currentIndex")

        try {
            binding.dotLayout.removeAllViews()

            // 确保参数有效
            if (totalCount <= 0 || currentIndex < 0) {
                Timber.w("MyCoinFragment invalid parameters: totalCount=$totalCount, currentIndex=$currentIndex")
                binding.dotLayout.visibility = View.GONE
                return
            }

            // 只有多于1张图片时才显示小圆点
            if (totalCount <= 1) {
                binding.dotLayout.visibility = View.GONE
                Timber.d("MyCoinFragment dots hidden: totalCount <= 1")
                return
            }

            binding.dotLayout.visibility = View.VISIBLE
            Timber.d("MyCoinFragment dots visible: creating $totalCount dots")

            for (i in 0 until totalCount) {
                val dotSize = DisplayUtils.dp2px(5f)
                val dot = ImageView(requireContext())


                val params = ViewGroup.MarginLayoutParams(dotSize, dotSize).apply {
                    if (i > 0) {
                        marginStart = DisplayUtils.dp2px(3f)
                    }
                }
                dot.layoutParams = params

                val isSelected = i == currentIndex
                val color = if (isSelected) Color.WHITE else "#88FFFFFF".toColorInt()

                dot.setImageDrawable(
                    DrawableUtils.createRoundRectDrawable(
                        radius = dotSize / 2f,
                        color = color
                    )
                )

                binding.dotLayout.addView(dot)
                Timber.d("MyCoinFragment added dot $i, selected=$isSelected, color=${Integer.toHexString(color)}")
            }

            Timber.d("MyCoinFragment dots update completed: ${binding.dotLayout.childCount} dots added")
        } catch (e: Exception) {
            Timber.e(e, "MyCoinFragment error updating dots")
            binding.dotLayout.visibility = View.GONE
        }
    }

    /**
     * 设置充值套餐列表
     */
    private fun setupRechargeOptions() {
        binding.rvRechargeOptions.layoutManager = GridLayoutManager(requireContext(), 2)
        // 添加卡片间距，13dp
        val spacing = DisplayUtils.dp2px(13f)
        binding.rvRechargeOptions.addItemDecoration(GridSpacingItemDecoration(2, spacing, false))
    }

    /**
     * 加载充值套餐数据
     */
    private fun loadRechargeOptions() {
        val cachedGoods = GoodsManager.getCachedAllGoods()
        if (cachedGoods.isNotEmpty()) {
            updateRechargeOptionsUI(cachedGoods)
        } else {
            lifecycleScope.launch {
                try {
                    val goodsList = GoodsManager.getAllGoods()
                    updateRechargeOptionsUI(goodsList)
                } catch (e: Exception) {
                    ToastUtils.showShortToast(getString(R.string.net_error_and_try_again))
                }
            }
        }
    }

    /**
     * 更新充值套餐UI
     */
    private fun updateRechargeOptionsUI(goodsList: List<GoodsInfo>) {
        val rechargeOptions = goodsList
            .sortedWith(compareByDescending<GoodsInfo> { it.isPromotion == true }.thenBy {
                it.price ?: Double.MAX_VALUE
            })
            .map { goods ->
                // 使用本地化价格逻辑，与其他地方保持一致
                val (priceSymbol, price) = GoodsManager.getLocaleGoodsPrice(goods)
                val (oldPriceSymbol, oldPrice) = GoodsManager.getLocaleGoodsPrice(goods, true)
                RechargeOption(
                    iconRes = GoodsManager.getIconByGoodsId(goods.code),
                    coinAmount = (goods.exchangeCoin ?: 0) ,
                    price = price?.let { "$priceSymbol$it" } ?: "",
                    oldPrice = oldPrice?.let { "$oldPriceSymbol$it" } ?: "",
                    priceSymbol = priceSymbol,
                    name = goods.name,
                    tags = goods.tags,
                    extraCoinPercent = goods.extraCoinPercent,
                    bonus = if ((goods.extraCoinPercent ?: 0) > 0) "+${goods.extraCoinPercent}%" else null,
                    goodsCode = goods.code,
                    type = goods.type,
                    remainMilliseconds = goods.remainMilliseconds ?: goods.surplusMillisecond,
                    invitationId = goods.invitationId
                )
            }

        val distinctOptions = rechargeOptions
            .filter { it.type == "0" || ((it.remainMilliseconds ?: 0L) > 0L) }

        rechargeAdapter = RechargeOptionAdapter.createWithoutLimit(
            options = distinctOptions,
            onClick = { option -> handleRechargeOptionClick(option) },
            onCountdownEnd = { loadRechargeOptions() }
        )
        binding.rvRechargeOptions.adapter = rechargeAdapter
    }

    /**
     * 处理充值套餐点击
     */
    private fun handleRechargeOptionClick(option: RechargeOption) {
        option.goodsCode?.let { goodsCode ->
            RechargeManager.startRecharge(
                activity = requireActivity(),
                goodsCode = goodsCode,
                entry = RechargeSource.SUBSCRIBE_DETAIL
            )
        }
    }

    /**
     * 更新金币余额显示
     */
    private fun updateCoinBalance() {
        val coinCount = UserInfoManager.myUserInfo?.availableCoins ?: 0
        binding.tvCoinBalance.text = coinCount.toString()
    }

    /**
     * 设置观察者
     */
    private fun setupObservers() {
        // 监听金币余额变化
        DualChannelEventManager.observeAvailableCoins(viewLifecycleOwner) { availableCoinsMessage ->
            val availableCoins = availableCoinsMessage.coins ?: 0
            UserInfoManager.myUserInfo?.availableCoins = availableCoins
            binding.tvCoinBalance.text = availableCoins.toString()
        }
    }

    override fun onDestroyView() {
        // 清理适配器监听器
        rechargeAdapter?.clearAllListeners()
        rechargeAdapter = null
        PagerHelper.cleanup()
        super.onDestroyView()
    }
}