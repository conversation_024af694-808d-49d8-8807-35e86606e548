package com.score.callmetest.ui.videocall.ongoing

import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.viewModelScope
import com.google.gson.Gson
import com.score.callmetest.manager.GiftManager
import com.score.callmetest.network.AddFriendRequest
import com.score.callmetest.network.ComplainInsertRecordRequest
import com.score.callmetest.network.GiftInfo
import com.score.callmetest.network.GiveGiftRequest
import com.score.callmetest.network.RetrofitUtils
import com.score.callmetest.ui.base.BaseViewModel
import com.score.callmetest.im.RongCloudManager
import com.score.callmetest.im.callback.ImSendMsgCallback
import com.score.callmetest.manager.FollowManager
import com.score.callmetest.manager.OnGiftAskMessage
import com.score.callmetest.manager.UserInfoManager
import com.score.callmetest.network.MysteriousInfo
import com.score.callmetest.network.NetworkResult
import com.score.callmetest.network.UserInfo
import com.score.callmetest.util.ThreadUtils
import io.rong.imlib.model.Message
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.jsonObject
import kotlinx.serialization.json.jsonPrimitive
import timber.log.Timber

class CallOngoingViewModel : BaseViewModel() {
    var channelName: String? = null
    // 存储为我的id
    var fromUserId: String? = null
    // 存储对方id
    var toUserId: String? = null


    // 主播信息
    var userInfo: UserInfo? = null

    suspend fun reportUser(reason: String = "不当行为"): Boolean {
        val req = ComplainInsertRecordRequest(
            broadcasterId = toUserId,
            channelName = channelName,
            reason = reason
        )
        val resp = RetrofitUtils.dataRepository.reportUser(req)
        return resp is NetworkResult.Success && resp.data == true
    }

    suspend fun addFriend(): Boolean {
        return toUserId?.let { FollowManager.followUserSync(it) } ?: false
    }

    /**
     * 已经去除这个功能--
     *
     * 其次，这个举报的传参有问题，后续如果需要添加这个功能，要注意参数
     *
     */
    fun reportUserWithCallback(onResult: (Boolean, String?) -> Unit) {
        viewModelScope.launch {
            try {
                val success = reportUser()
                onResult(success, if (success) null else "举报失败")
            } catch (e: Exception) {
                onResult(false, "举报失败: ${e.message}")
            }
        }
    }

    fun addFriendWithCallback(onResult: (Boolean, String?) -> Unit) {
        viewModelScope.launch {
            try {
                val success = addFriend()
                onResult(success, if (success) null else "关注失败")
            } catch (e: Exception) {
                onResult(false, "关注失败: ${e.message}")
            }
        }
    }

    // 已移除后置摄像头相关逻辑，全部由 RearCameraManager 负责

    // 礼物索取相关回调
    private var onGiftRequestShow: ((GiftRequestInfo) -> Unit)? = null
    private var onGiftRequestHide: (() -> Unit)? = null
    private var giftRequestCountdownJob: Job? = null

    // 礼物赠送相关回调
    private var onGiftSendSuccess: ((GiftInfo) -> Unit)? = null
    private var onGiftSendError: ((String) -> Unit)? = null

    // 礼物发送横幅相关回调
    private var onGiftSendBannerShow: ((GiftSendInfo) -> Unit)? = null
    private var onGiftSendBannerHide: (() -> Unit)? = null
    private var giftSendBannerCountdownJob: Job? = null

    // 当前显示的礼物发送信息（用于累积相同礼物）
    private var currentGiftSendInfo: GiftSendInfo? = null

    /**
     * 设置礼物索取事件监听器
     * @param onShow 显示礼物索取横幅的回调
     * @param onHide 隐藏礼物索取横幅的回调
     */
    fun setGiftRequestListener(
        onShow: (GiftRequestInfo) -> Unit,
        onHide: () -> Unit
    ) {
        this.onGiftRequestShow = onShow
        this.onGiftRequestHide = onHide
    }

    /**
     * 设置礼物赠送事件监听器
     * @param onSuccess 礼物赠送成功的回调
     * @param onError 礼物赠送失败的回调
     */
    fun setGiftSendListener(
        onSuccess: (GiftInfo) -> Unit,
        onError: (String) -> Unit
    ) {
        this.onGiftSendSuccess = onSuccess
        this.onGiftSendError = onError
    }

    /**
     * 设置礼物发送横幅事件监听器
     * @param onShow 显示礼物发送横幅的回调
     * @param onHide 隐藏礼物发送横幅的回调
     */
    fun setGiftSendBannerListener(
        onShow: (GiftSendInfo) -> Unit,
        onHide: () -> Unit
    ) {
        this.onGiftSendBannerShow = onShow
        this.onGiftSendBannerHide = onHide
    }

    /**
     * 处理礼物索取消息
     * @param giftAskMessage 礼物索取消息
     */
    fun handleGiftAskMessage(giftAskMessage: OnGiftAskMessage) {
        viewModelScope.launch {
            try {
                val content = giftAskMessage.content
                if (content.isNullOrBlank()) {
                    Timber.d("礼物索取消息内容为空")
                    return@launch
                }

                // 解析礼物信息
                val giftRequestInfo = parseGiftRequestContent(content, giftAskMessage.fromUserId)
                if (giftRequestInfo != null) {
                    // 显示礼物索取横幅
                    showGiftRequestBanner(giftRequestInfo)
                } else {
                    Timber.w("解析礼物索取消息失败: $content")
                }

            } catch (e: Exception) {
                Timber.e(e, "处理礼物索取消息失败")
            }
        }
    }

    /**
     * 解析礼物索取消息内容
     * @param content 消息内容（JSON格式）
     * @param fromUserId 发送者ID
     * @return 解析后的礼物索取信息
     */
    private fun parseGiftRequestContent(content: String, fromUserId: String?): GiftRequestInfo? {
        return try {
            // 尝试解析JSON内容
            val gson = Gson()
            val giftInfo = gson.fromJson(content, GiftInfo::class.java)
            if (giftInfo == null) {
                return null
            }

            GiftRequestInfo(
                giftInfo = giftInfo,
                fromUserId = fromUserId,
                fromUserName = userInfo?.nickname ?: "Unknown User",
                timestamp = System.currentTimeMillis()
            )

        } catch (e: Exception) {
            Timber.e(e, "解析礼物索取内容失败: $content")
            null
        }
    }

    /**
     * 显示礼物索取横幅并启动10秒倒计时
     */
    private fun showGiftRequestBanner(giftRequestInfo: GiftRequestInfo) {
        // 取消之前的倒计时
        giftRequestCountdownJob?.cancel()

        // 通知UI显示横幅
        viewModelScope.launch(Dispatchers.Main) {
            onGiftRequestShow?.invoke(giftRequestInfo)
        }

        // 启动10秒倒计时
        giftRequestCountdownJob = viewModelScope.launch {
            try {
                delay(10_000) // 10秒
                // 倒计时结束，隐藏横幅
                viewModelScope.launch(Dispatchers.Main) {
                    onGiftRequestHide?.invoke()
                }
                Timber.d("礼物索取横幅10秒倒计时结束，自动隐藏")
            } catch (e: Exception) {
                Timber.e(e, "礼物索取倒计时异常")
            }
        }

        Timber.d("显示礼物索取横幅: ${giftRequestInfo.giftInfo.giftDesc}")
    }

    /**
     * 手动隐藏礼物索取横幅
     */
    fun hideGiftRequestBanner() {
        giftRequestCountdownJob?.cancel()
        viewModelScope.launch(Dispatchers.Main) {
            onGiftRequestHide?.invoke()
        }
        Timber.d("手动隐藏礼物索取横幅")
    }

    /**
     * 送礼物
     * 完全参考ChatViewModel中的sendGift方法实现
     * @param giftInfo 礼物信息
     * @param from 礼物来源--参考 [GiveGiftRequest.sceneSource]
     * @param channel
     */
    fun sendGift(giftInfo: GiftInfo?,from: String,channel: String?) {
        Timber.d("receive send gift cmd---$giftInfo")
        if (giftInfo == null) return

        // 先确定targetUser是否还在
        val targetUserId = userInfo?.userId ?: return

        // 请求后台发送礼物
        viewModelScope.launch {
            try {
                val response = withContext(Dispatchers.IO) {
                    val request = GiveGiftRequest(channelName = channel,giftCode = giftInfo.code, num = 1, recipientUserId = targetUserId,
                        sceneSource = from, giftSource = 0)
                    RetrofitUtils.dataRepository.giveUserGifts(request)
                }

                Timber.d("gift-sent-$response")
                viewModelScope.launch(Dispatchers.Main) {
                    if (response is NetworkResult.Success) {
                        onGiftSendSuccess?.invoke(giftInfo)
                    } else {
                        onGiftSendError?.invoke("接口请求发送失败: $response")
                    }
                }

            } catch (e: Exception) {
                Timber.e(e, "礼物赠送异常")
                viewModelScope.launch(Dispatchers.Main) {
                    onGiftSendError?.invoke("礼物赠送异常: ${e.message}")
                }
            }
        }
    }

    /**
     * 显示礼物发送横幅并启动5秒倒计时
     */
    fun showGiftSendBanner(giftInfo: GiftInfo) {
        // 取消之前的倒计时
        giftSendBannerCountdownJob?.cancel()

        // 检查是否是相同的礼物
        val newGiftSendInfo = if (currentGiftSendInfo != null &&
            currentGiftSendInfo!!.giftInfo.code == giftInfo.code) {
            // 相同礼物，累加数量
            val newCount = currentGiftSendInfo!!.count + 1
            currentGiftSendInfo!!.copy(
                count = newCount,
                timestamp = System.currentTimeMillis()
            )
        } else {
            // 不同礼物或首次显示，创建新的礼物发送信息
            GiftSendInfo(
                giftInfo = giftInfo,
                senderName = UserInfoManager.myUserInfo?.nickname ?: "YOU", // 当前用户发送
                count = 1,
                timestamp = System.currentTimeMillis()
            )
        }

        // 更新当前礼物信息
        currentGiftSendInfo = newGiftSendInfo

        // 通知UI显示横幅
        viewModelScope.launch(Dispatchers.Main) {
            onGiftSendBannerShow?.invoke(newGiftSendInfo)
        }

        // 启动5秒倒计时
        giftSendBannerCountdownJob = viewModelScope.launch {
            try {
                delay(5_000) // 5秒
                // 倒计时结束，隐藏横幅并清空当前礼物信息
                viewModelScope.launch(Dispatchers.Main) {
                    onGiftSendBannerHide?.invoke()
                    currentGiftSendInfo = null
                }
                Timber.d("礼物发送横幅5秒倒计时结束，自动隐藏")
            } catch (e: Exception) {
                Timber.e(e, "礼物发送横幅倒计时异常")
            }
        }

        Timber.d("显示礼物发送横幅: ${giftInfo.giftDesc}, 数量: ${newGiftSendInfo.count}")
    }

    /**
     * 手动隐藏礼物发送横幅
     */
    fun hideGiftSendBanner() {
        giftSendBannerCountdownJob?.cancel()
        viewModelScope.launch(Dispatchers.Main) {
            onGiftSendBannerHide?.invoke()
            currentGiftSendInfo = null
        }
        Timber.d("手动隐藏礼物发送横幅")
    }

    override fun onCleared() {
        super.onCleared()
        // 清理倒计时任务和回调
        giftRequestCountdownJob?.cancel()
        giftSendBannerCountdownJob?.cancel()
        onGiftRequestShow = null
        onGiftRequestHide = null
        onGiftSendSuccess = null
        onGiftSendError = null
        onGiftSendBannerShow = null
        onGiftSendBannerHide = null
        currentGiftSendInfo = null
    }
}

/**
 * 礼物索取信息
 */
data class GiftRequestInfo(
    val giftInfo: GiftInfo,
    val fromUserId: String?,
    val fromUserName: String,
    val timestamp: Long
)

/**
 * 礼物发送信息
 */
data class GiftSendInfo(
    val giftInfo: GiftInfo,
    val senderName: String,
    val count: Int = 1,
    val timestamp: Long
)