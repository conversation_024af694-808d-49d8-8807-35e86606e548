package com.score.callmetest.ui.widget

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import androidx.appcompat.widget.AppCompatTextView

class AlphaTextView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : AppCompatTextView(context, attrs, defStyleAttr) {

    private var normalAlpha = 1.0f
    private var pressedAlpha = 0.5f
    private var disabledAlpha = 0.2f

    /**
     * 这个设置为false，即不会有点击和touch事件，但不会改动enable
     */
    var isTouchable = true

    init {
        isClickable = true
        isFocusable = true
        alpha = normalAlpha
    }

    override fun setEnabled(enabled: Boolean) {
        super.setEnabled(enabled)
        alpha = if (enabled) normalAlpha else disabledAlpha
    }

    override fun onTouchEvent(event: MotionEvent?): Boolean {
        if (!isEnabled || !isTouchable) return super.onTouchEvent(event)
        when (event?.action) {
            MotionEvent.ACTION_DOWN -> alpha = pressedAlpha
            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> alpha = normalAlpha
        }
        return super.onTouchEvent(event)
    }
} 