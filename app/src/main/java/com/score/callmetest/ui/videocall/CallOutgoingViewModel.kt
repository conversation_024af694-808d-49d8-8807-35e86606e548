package com.score.callmetest.ui.videocall

import androidx.lifecycle.viewModelScope
import com.score.callmetest.manager.CallSource
import com.score.callmetest.manager.CallType
import com.score.callmetest.manager.HangUpReason
import com.score.callmetest.manager.VideoCallManager
import com.score.callmetest.network.CreateChannelResponse
import com.score.callmetest.ui.base.BaseViewModel

class CallOutgoingViewModel : BaseViewModel() {
    var channelName: String? = null
    var oppositeUserId: String? = null
    var channelResponse: CreateChannelResponse? = null

    fun createChannel(
        toUserId: String,
        callType: CallType,
        callSource: CallSource,
        onSuccess: (CreateChannelResponse?) -> Unit = {},
        onError: (String) -> Unit = {}
    ) {
        VideoCallManager.createChannel(
            scope = viewModelScope,
            toUserId = toUserId,
            callType = callType,
            callSource = callSource,
            onSuccess = { data ->
                channelResponse = data
                channelName = channelResponse?.channelName
                oppositeUserId = channelResponse?.toUserId
                onSuccess.invoke(data)
            },
            onError = onError
        )
    }

    fun hangup(
        channelName: String?,
        hangUpReason: HangUpReason = HangUpReason.NORMAL,
        remark: String?,
        onSuccess: () -> Unit = {},
        onError: (String) -> Unit = {}
    ) {
        if (!channelName.isNullOrEmpty() && !oppositeUserId.isNullOrEmpty() && channelName == this.channelName) {
            VideoCallManager.hangUp(
                scope = viewModelScope,
                channelName = channelName,
                hangUpReason = hangUpReason,
                oppositeUserId = oppositeUserId!!,
                remark = remark,
                onSuccess = onSuccess,
                onError = onError
            )
        }
    }


} 