package com.score.callmetest.ui.videocall

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.score.callmetest.R
import com.score.callmetest.util.click

class ImpressionTagAdapter(
    private val onTagClick: (String, Int, Boolean) -> Unit // add isSelected param
) : RecyclerView.Adapter<ImpressionTagAdapter.TagViewHolder>() {
    private var tags: List<String> = emptyList()
    private val selectedPositions = mutableSetOf<Int>()

    fun submitList(newTags: List<String>) {
        tags = newTags
        selectedPositions.clear()
        notifyDataSetChanged()
    }

    fun toggleSelected(position: Int) {
        if (selectedPositions.contains(position)) {
            selectedPositions.remove(position)
        } else {
            selectedPositions.add(position)
        }
        notifyItemChanged(position)
    }

    fun getSelectedTags(): List<String> = selectedPositions.map { tags[it] }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): TagViewHolder {
        val view = LayoutInflater.from(parent.context).inflate(R.layout.item_impression_tag, parent, false)
        return TagViewHolder(view)
    }

    override fun onBindViewHolder(holder: TagViewHolder, position: Int) {
        val tag = tags[position]
        holder.text.text = tag
        holder.text.isSelected = selectedPositions.contains(position)
        holder.text.click {
            toggleSelected(position)
            onTagClick(tag, position, selectedPositions.contains(position))
        }
    }

    override fun getItemCount(): Int = tags.size

    class TagViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        val text: TextView = view.findViewById(R.id.tagText)
    }
} 