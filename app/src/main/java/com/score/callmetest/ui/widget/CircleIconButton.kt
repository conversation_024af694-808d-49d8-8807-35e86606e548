package com.score.callmetest.ui.widget

import android.content.Context
import android.graphics.*
import android.util.AttributeSet
import android.view.MotionEvent
import androidx.core.content.ContextCompat
import com.score.callmetest.R
import androidx.core.content.withStyledAttributes
import androidx.core.graphics.withClip

class CircleIconButton @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : AlphaImageView(context, attrs, defStyleAttr) {

    private var normalAlpha = 1.0f
    private var pressedAlpha = 0.5f
    private var disabledAlpha = 0.2f
    private val clipPath = Path()
    private var viewSize = 0

    // 描边相关属性
    private val strokePaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        style = Paint.Style.STROKE
    }
    private var strokeWidth = 0f
    private var strokeColor = Color.TRANSPARENT
    private var strokeRadius = 0f

    init {
        isClickable = true
        isFocusable = true
        background = ContextCompat.getDrawable(context, R.drawable.round_button_bg)
        alpha = normalAlpha

        // 可通过xml自定义icon、背景色和描边属性
        attrs?.let {
            context.withStyledAttributes(it, R.styleable.RoundIconButton) {
                val icon = getDrawable(R.styleable.RoundIconButton_iconSrc)
                val bgColor = getColor(
                    R.styleable.RoundIconButton_bgColor,
                    ContextCompat.getColor(context, android.R.color.white)
                )

                // 读取描边属性
                strokeWidth = getDimension(R.styleable.RoundIconButton_strokeWidth, 0f)
                strokeColor = getColor(
                    R.styleable.RoundIconButton_strokeColor,
                    ContextCompat.getColor(context, android.R.color.transparent)
                )

                setImageDrawable(icon)
                background?.setTint(bgColor)

                // 配置描边画笔
                strokePaint.strokeWidth = strokeWidth
                strokePaint.color = strokeColor
            }
        }
    }

    override fun setEnabled(enabled: Boolean) {
        super.setEnabled(enabled)
        alpha = if (enabled) normalAlpha else disabledAlpha
        // 更新描边透明度
        strokePaint.alpha = if (enabled) 255 else (disabledAlpha * 255).toInt()
        invalidate()
    }

    override fun onTouchEvent(event: MotionEvent?): Boolean {
        when (event?.action) {
            MotionEvent.ACTION_DOWN -> alpha = pressedAlpha
            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> alpha = normalAlpha
        }
        return super.onTouchEvent(event)
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        viewSize = w.coerceAtMost(h)
        val radius = viewSize / 2f
        // 计算描边半径（减去描边宽度的一半，使描边居中显示）
        strokeRadius = radius - strokeWidth / 2

        clipPath.reset()
        clipPath.addCircle(w / 2f, h / 2f, radius, Path.Direction.CW)
        clipPath.close()
    }

    override fun onDraw(canvas: Canvas) {
        canvas.withClip(clipPath) {
            super.onDraw(this)
        }

        // 绘制描边（如果宽度大于0）
        if (strokeWidth > 0) {
            canvas.drawCircle(
                width / 2f,
                height / 2f,
                strokeRadius,
                strokePaint
            )
        }
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        // 保证宽高一致，取最小值，始终为正圆
        val size = widthMeasureSpec.coerceAtMost(heightMeasureSpec)
        super.onMeasure(size, size)
    }

    // 提供设置描边属性的方法
    fun setStrokeWidth(width: Float) {
        strokeWidth = width
        strokePaint.strokeWidth = width
        invalidate()
    }

    fun setStrokeColor(color: Int) {
        strokeColor = color
        strokePaint.color = color
        invalidate()
    }
}