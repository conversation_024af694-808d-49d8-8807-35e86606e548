package com.score.callmetest.ui.mine.follow

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.score.callmetest.R
import com.score.callmetest.manager.FollowManager
import com.score.callmetest.manager.UserInfoManager
import com.score.callmetest.network.FollowModel
import com.score.callmetest.network.UserInfo
import com.score.callmetest.ui.base.BaseViewModel
import com.score.callmetest.ui.mine.follow.BottomState
import com.score.callmetest.util.EventBus


class FollowViewModel : BaseViewModel() {
    // 关注列表数据（type=3）
    private val _followingList = MutableLiveData<List<FollowModel>>()
    val followingList: LiveData<List<FollowModel>> = _followingList

    // 粉丝列表数据（type=2）
    private val _followersList = MutableLiveData<List<FollowModel>>()
    val followersList: LiveData<List<FollowModel>> = _followersList

    // 加载状态
    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading

    // 错误信息
    private val _errorMessage = MutableLiveData<String>()
    val errorMessage: LiveData<String> = _errorMessage

    // 跳转到聊天页面的用户信息
    private val _goChatUser = MutableLiveData<UserInfo>()
    val goChatUser: LiveData<UserInfo> = _goChatUser

    // 关注列表底部状态
    private val _followingBottomState = MutableLiveData<BottomState>()
    val followingBottomState: LiveData<BottomState> = _followingBottomState

    // 粉丝列表底部状态
    private val _followersBottomState = MutableLiveData<BottomState>()
    val followersBottomState: LiveData<BottomState> = _followersBottomState

    // Toast消息
    private val _toastMessage = MutableLiveData<Int>()
    val toastMessage: LiveData<Int> = _toastMessage

    // 分页状态管理
    private var followingCurrentPage = 1
    private var followersCurrentPage = 1
    private var isFollowingLoadingMore = false
    private var isFollowersLoadingMore = false
    private var hasMoreFollowing = true
    private var hasMoreFollowers = true
    private val pageSize = 15

    // 当前列表数据
    private val currentFollowingList = mutableListOf<FollowModel>()
    private val currentFollowersList = mutableListOf<FollowModel>()

    /**
     * 加载关注列表（type=3）
     * @param isRefresh 是否是刷新操作，true表示刷新，false表示加载更多
     */
    fun loadFollowingList(isRefresh: Boolean = true) {
        if (isRefresh) {
            // 刷新操作，重置分页状态和底部状态
            followingCurrentPage = 1
            hasMoreFollowing = true
            currentFollowingList.clear()
            _followingBottomState.postValue(BottomState.HIDDEN)
        } else {
            // 加载更多操作，检查是否正在加载或没有更多数据
            if (isFollowingLoadingMore || !hasMoreFollowing) {
                return
            }
            followingCurrentPage++
            // 开始加载更多时显示加载动画
            _followingBottomState.postValue(BottomState.LOADING)
        }

        isFollowingLoadingMore = true
        FollowManager.loadFollowingList(pageSize, followingCurrentPage, object : FollowManager.FollowPageCallback {
            override fun onSuccess(list: List<FollowModel>) {
                isFollowingLoadingMore = false

                if (isRefresh) {
                    currentFollowingList.clear()
                }
                currentFollowingList.addAll(list)

                // 判断是否还有更多数据
                hasMoreFollowing = list.size >= pageSize

                _followingList.postValue(currentFollowingList.toList())

                // 根据数据情况设置底部状态
                updateBottomState(isRefresh, list.size, currentFollowingList, _followingBottomState)
            }

            override fun onError(errorMsg: String) {
                isFollowingLoadingMore = false
                if (!isRefresh) {
                    // 加载更多失败时，回退页码
                    followingCurrentPage--

                    // 显示Toast并设置为完成状态
                    _toastMessage.postValue(R.string.there_is_no_more_data)
                    _followingBottomState.postValue(BottomState.FINISHED)
                } else {
                    _followingBottomState.postValue(BottomState.HIDDEN)
                }
                _errorMessage.postValue(errorMsg)
            }

            override fun onLoading(isLoading: Boolean) {
                _isLoading.postValue(isLoading)
            }
        })
    }

    /**
     * 通用的底部状态更新方法
     */
    private fun updateBottomState(
        isRefresh: Boolean,
        dataSize: Int,
        currentList: List<FollowModel>,
        bottomStateLiveData: MutableLiveData<BottomState>
    ) {
        if (isRefresh) {
            // 第一次请求
            when {
                currentList.isEmpty() -> {
                    // 数据为空，隐藏底部
                    bottomStateLiveData.postValue(BottomState.HIDDEN)
                }
                dataSize < pageSize -> {
                    // 数据少于15条，显示静态底部
                    bottomStateLiveData.postValue(BottomState.FINISHED)
                }
                dataSize == pageSize -> {
                    // 数据等于15条，隐藏底部等待用户滚动
                    bottomStateLiveData.postValue(BottomState.HIDDEN)
                }
            }
        } else {
            // 后续请求
            when {
                dataSize == 0 -> {
                    // 没有更多数据，显示Toast和静态底部
                    _toastMessage.postValue(R.string.there_is_no_more_data)
                    bottomStateLiveData.postValue(BottomState.FINISHED)
                }
                dataSize < pageSize -> {
                    // 数据少于15条，显示静态底部
                    bottomStateLiveData.postValue(BottomState.FINISHED)
                }
                dataSize == pageSize -> {
                    // 数据等于15条，隐藏底部继续等待
                    bottomStateLiveData.postValue(BottomState.HIDDEN)
                }
            }
        }
    }

    /**
     * 加载粉丝列表（type=2）
     * @param isRefresh 是否是刷新操作，true表示刷新，false表示加载更多
     */
    fun loadFollowersList(isRefresh: Boolean = true) {
        if (isRefresh) {
            // 刷新操作，重置分页状态和底部状态
            followersCurrentPage = 1
            hasMoreFollowers = true
            currentFollowersList.clear()
            _followersBottomState.postValue(BottomState.HIDDEN)
        } else {
            // 加载更多操作，检查是否正在加载或没有更多数据
            if (isFollowersLoadingMore || !hasMoreFollowers) {
                return
            }
            followersCurrentPage++
            // 开始加载更多时显示加载动画
            _followersBottomState.postValue(BottomState.LOADING)
        }

        isFollowersLoadingMore = true
        FollowManager.loadFollowersList(pageSize, followersCurrentPage, object : FollowManager.FollowPageCallback {
            override fun onSuccess(list: List<FollowModel>) {
                isFollowersLoadingMore = false

                if (isRefresh) {
                    currentFollowersList.clear()
                }
                currentFollowersList.addAll(list)

                // 判断是否还有更多数据
                hasMoreFollowers = list.size >= pageSize

                _followersList.postValue(currentFollowersList.toList())

                // 当是刷新操作时，通知MineFragment更新关系计数
                if (isRefresh) {
                    EventBus.post(FollowManager.FollowListRefreshEvent(type = 2))
                }

                // 根据数据情况设置底部状态
                updateBottomState(isRefresh, list.size, currentFollowersList, _followersBottomState)
            }

            override fun onError(errorMsg: String) {
                isFollowersLoadingMore = false
                if (!isRefresh) {
                    // 加载更多失败时，回退页码
                    followersCurrentPage--
                    // 显示Toast并设置为完成状态
                    _toastMessage.postValue(R.string.there_is_no_more_data)
                    _followersBottomState.postValue(BottomState.FINISHED)
                } else {
                    _followersBottomState.postValue(BottomState.HIDDEN)
                }
                _errorMessage.postValue(errorMsg)
            }

            override fun onLoading(isLoading: Boolean) {
                _isLoading.postValue(isLoading)
            }
        })
    }



    /**
     * 关注用户（添加好友）
     */
    fun followUser(userId: String, callback: (Boolean) -> Unit) {
        FollowManager.followUser(viewModelScope,userId, object : FollowManager.FollowActionCallback {
            override fun onSuccess() {
                callback(true)
            }

            override fun onError(errorMsg: String) {
                _errorMessage.postValue(errorMsg)
                callback(false)
            }
        })
    }

    /**
     * 取关用户（删除好友）
     */
    fun unfollowUser(userId: String, callback: (Boolean) -> Unit) {
        FollowManager.unfollowUser(viewModelScope,userId, object : FollowManager.FollowActionCallback {
            override fun onSuccess() {
                callback(true)
            }

            override fun onError(errorMsg: String) {
                _errorMessage.postValue(errorMsg)
                callback(false)
            }
        })
    }

    /**
     * 点击item查询userinfo后跳转到聊天
     */
    fun gotoChat(userId: String) {
        // 先查缓存，再网络查询
        UserInfoManager.getUserInfo(userId,scope = viewModelScope) { getUserInfo ->
            getUserInfo?.let { nonNullUser ->
                _goChatUser.value = nonNullUser
            }
        }
    }

    /**
     * 检查关注列表是否还有更多数据
     */
    fun hasMoreFollowingData(): Boolean = hasMoreFollowing

    /**
     * 检查粉丝列表是否还有更多数据
     */
    fun hasMoreFollowersData(): Boolean = hasMoreFollowers

    /**
     * 检查关注列表是否正在加载更多
     */
    fun isFollowingLoadingMore(): Boolean = isFollowingLoadingMore

    /**
     * 检查粉丝列表是否正在加载更多
     */
    fun isFollowersLoadingMore(): Boolean = isFollowersLoadingMore
}