package com.score.callmetest.ui.widget.Helper

import android.content.Context
import android.content.Intent
import android.os.Handler
import android.os.Looper
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import androidx.core.net.toUri
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.widget.ViewPager2
import com.score.callmetest.R
import com.score.callmetest.manager.GlobalManager
import com.score.callmetest.network.BannerInfoResponse
import com.score.callmetest.ui.preview.MultiImagePreviewActivity
import com.score.callmetest.util.DisplayUtils
import com.score.callmetest.util.GlideUtils
import com.score.callmetest.util.ThreadUtils
import com.score.callmetest.util.click
import timber.log.Timber

object PagerHelper {
    private val autoScrollHandler = Handler(Looper.getMainLooper())
    private var autoScrollRunnable: Runnable? = null
    private var isAutoScrollEnabled = false
    private var isUserScrolling = false

    fun setupBannerPager(
        viewPager: ViewPager2,
        bannerList: List<BannerInfoResponse>,
        enableLoop: Boolean = true,
        enableAutoScroll: Boolean = true,
        autoScrollInterval: Long = 3000L,
        onItemClick: ((realIndex: Int) -> Unit)? = null,
        onPageSelected: ((realIndex: Int) -> Unit)? = null,
    ) {
        val doubleUrls = ArrayList<GlideUtils.DoubleUrl>().apply {
            bannerList.map {
                add(GlideUtils.DoubleUrl(it.pic.toString(), it.pic.toString()))
            }
        }

        setupPhotoPager(
            viewPager = viewPager,
            doubleUrls = doubleUrls,
            enableLoop = enableLoop,
            enableAutoScroll = enableAutoScroll,
            autoScrollInterval = autoScrollInterval,
            scaleType = ImageView.ScaleType.FIT_XY,
            onImageClick = { realIndex, urls ->
                onItemClick?.invoke(realIndex)
            },
            onPageSelected = onPageSelected,
        )
    }

    /**
     * 设置图片轮播，支持单图/多图循环，点击可预览，支持自动轮播
     * @param viewPager ViewPager2控件
     * @param imageUrls 原始图片url列表
     * @param context 上下文
     * @param enableLoop 是否启用循环（多图时默认true）
     * @param enableAutoScroll 是否启用自动轮播（多图时默认true）
     * @param autoScrollInterval 自动轮播间隔，默认5000ms
     * @param onImageClick 可选，图片点击回调（默认跳转大图预览）
     */
    fun setupPhotoPager(
        viewPager: ViewPager2,
        doubleUrls: List<GlideUtils.DoubleUrl>,
        enableLoop: Boolean = true,
        enableAutoScroll: Boolean = true,
        autoScrollInterval: Long = 3000L,
        scaleType: ImageView.ScaleType? = ImageView.ScaleType.CENTER_CROP,
        onImageClick: ((realIndex: Int, urls: List<GlideUtils.DoubleUrl>) -> Unit)? = null,
        onPageSelected: ((realIndex: Int) -> Unit)? = null,
    ) {
        // 对图片URL进行去重处理
        val uniqueImageUrls = doubleUrls.distinct()

        if (uniqueImageUrls.isEmpty()) return

        // 清理之前的资源
        cleanup()

        GlobalManager.setNeverOverScroll(viewPager)

        viewPager.offscreenPageLimit = 4

        if (uniqueImageUrls.size > 1 && enableLoop) {
            // 无限循环
//            val loopImageUrls = mutableListOf<String>()
            val loopImageDoubleUrls = mutableListOf<GlideUtils.DoubleUrl>()
            loopImageDoubleUrls.add(uniqueImageUrls.last())
            loopImageDoubleUrls.addAll(uniqueImageUrls)
            loopImageDoubleUrls.add(uniqueImageUrls.first())

            val adapter = object : RecyclerView.Adapter<RecyclerView.ViewHolder>() {
                override fun onCreateViewHolder(
                    parent: ViewGroup,
                    viewType: Int
                ): RecyclerView.ViewHolder {
                    val imageView = ImageView(parent.context)
                    imageView.layoutParams = ViewGroup.LayoutParams(
                        ViewGroup.LayoutParams.MATCH_PARENT,
                        ViewGroup.LayoutParams.MATCH_PARENT
                    )
                    imageView.scaleType = scaleType
                    imageView.click {
                        val realIndex =
                            (viewPager.currentItem - 1 + uniqueImageUrls.size) % uniqueImageUrls.size
                        if (onImageClick != null) {
                            onImageClick(realIndex, doubleUrls)
                        } else {
                            val intent = Intent(parent.context, MultiImagePreviewActivity::class.java)
                            intent.putParcelableArrayListExtra(
                                "imageUris", ArrayList(doubleUrls.map { it.primary.toString().toUri() })
                            )
                            intent.putExtra("startIndex", realIndex)
                            parent.context.startActivity(intent)
                        }
                    }
                    return object : RecyclerView.ViewHolder(imageView) {}
                }

                override fun getItemCount() = loopImageDoubleUrls.size
                override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
                    val imageView = holder.itemView as ImageView
                    GlideUtils.load(
                        imageView = imageView,
                        doubleUrl = loopImageDoubleUrls[position],
                        placeholder = R.drawable.placeholder,
                        onResourceReady = {
                            ThreadUtils.runOnMainDelayed(200) {
                                if (viewPager != null) {
                                    viewPager.visibility = View.VISIBLE
                                }
                            }
                        },
                    )
                }
            }
            viewPager.adapter = adapter
            viewPager.setCurrentItem(1, false)

            // 创建页面变化回调
            // 注册回调
            viewPager.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
                override fun onPageScrollStateChanged(state: Int) {
                    if (state == ViewPager2.SCROLL_STATE_IDLE) {
                        val itemCount = loopImageDoubleUrls.size
                        val cur = viewPager.currentItem
                        when (cur) {
                            0 -> viewPager.setCurrentItem(itemCount - 2, false)
                            itemCount - 1 -> viewPager.setCurrentItem(1, false)
                        }
                        isUserScrolling = false
                        if (enableAutoScroll) {
                            startAutoScroll(viewPager, autoScrollInterval)
                        }
                    } else if (state == ViewPager2.SCROLL_STATE_DRAGGING) {
                        isUserScrolling = true
                        stopAutoScroll()
                    }
                }

                override fun onPageSelected(position: Int) {
                    val realIndex =
                        (viewPager.currentItem - 1 + uniqueImageUrls.size) % uniqueImageUrls.size
                    onPageSelected?.invoke(realIndex)
                    super.onPageSelected(position)
                }
            })

            // 启动自动轮播
            if (enableAutoScroll) {
                startAutoScroll(viewPager, autoScrollInterval)
            }
        } else {
            // 单图
            val adapter = object : RecyclerView.Adapter<RecyclerView.ViewHolder>() {
                override fun onCreateViewHolder(
                    parent: ViewGroup,
                    viewType: Int
                ): RecyclerView.ViewHolder {
                    val imageView = ImageView(parent.context)
                    imageView.layoutParams = ViewGroup.LayoutParams(
                        ViewGroup.LayoutParams.MATCH_PARENT,
                        ViewGroup.LayoutParams.MATCH_PARENT
                    )
                    imageView.scaleType = ImageView.ScaleType.CENTER_CROP
                    imageView.click {
                        if (onImageClick != null) {
                            onImageClick(0, doubleUrls)
                        } else {
                            val intent = Intent(parent.context, MultiImagePreviewActivity::class.java)
                            intent.putParcelableArrayListExtra(
                                "imageUris", ArrayList(doubleUrls.map { it.primary.toString().toUri() })
                            )
                            intent.putExtra("startIndex", 0)
                            parent.context.startActivity(intent)
                        }
                    }
                    return object : RecyclerView.ViewHolder(imageView) {}
                }

                override fun getItemCount() = uniqueImageUrls.size
                override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
                    val imageView = holder.itemView as ImageView
                    GlideUtils.load(
                        imageView = imageView,
                        doubleUrl = uniqueImageUrls[position],
                        placeholder = R.drawable.placeholder,
                        onResourceReady = {
                            viewPager.visibility = View.VISIBLE
                        }
                    )
                }
            }
            viewPager.adapter = adapter
            viewPager.setCurrentItem(0, false)
        }
    }

    /**
     * 启动自动轮播
     */
    private fun startAutoScroll(viewPager: ViewPager2, interval: Long) {
        if (isAutoScrollEnabled) return
        isAutoScrollEnabled = true

        autoScrollRunnable = object : Runnable {
            override fun run() {
                if (!isUserScrolling) {
                    val cur = viewPager.currentItem
                    viewPager.setCurrentItem(cur + 1, true)
                }
                autoScrollHandler.postDelayed(this, interval)
            }
        }

        autoScrollRunnable?.let { runnable ->
            autoScrollHandler.postDelayed(runnable, interval)
        }
    }

    /**
     * 停止自动轮播
     */
    private fun stopAutoScroll() {
        isAutoScrollEnabled = false
        autoScrollRunnable?.let {
            autoScrollHandler.removeCallbacks(it)
        }
        autoScrollRunnable = null
    }

    /**
     * 清理所有资源，在Activity/Fragment销毁时调用
     */
    fun cleanup() {
        stopAutoScroll()
        isUserScrolling = false
    }
} 