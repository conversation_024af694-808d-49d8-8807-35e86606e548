package com.score.callmetest.ui.widget

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import androidx.appcompat.widget.AppCompatImageView

open class AlphaImageView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : AppCompatImageView(context, attrs, defStyleAttr) {

    private var normalAlpha = 1.0f
    private var pressedAlpha = 0.5f


    /**
     * 这个设置为false，即不会有点击和touch事件，但不会改动enable
     */
    var isTouchable = true

    init {
        isClickable = true
        isFocusable = true
        alpha = normalAlpha
    }

    override fun onTouchEvent(event: MotionEvent?): Bo<PERSON>an {
        if (!isTouchable) return super.onTouchEvent(event)
        when (event?.action) {
            MotionEvent.ACTION_DOWN -> alpha = pressedAlpha
            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> alpha = normalAlpha
        }
        return super.onTouchEvent(event)
    }
} 