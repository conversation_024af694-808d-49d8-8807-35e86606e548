package com.score.callmetest.ui.home

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.score.callmetest.manager.StrategyManager


class HomeViewModel : ViewModel() {

    // 从 broadcasterWallTagList 获取标签数据
    val broadcasterWallTabDataLists = StrategyManager.getBroadcasterWallTagList()
    
    // 当前选中的国家代码，null表示"All"
    private val _selectedCountryCode = MutableLiveData<String?>("ALL")
    val selectedCountryCode: MutableLiveData<String?> = _selectedCountryCode
    
    fun setSelectedCountry(countryCode: String?) {
        _selectedCountryCode.value = countryCode
    }

    fun getSelectedCountryCode(): String? {
        return _selectedCountryCode.value
    }

    data class WallScrollEvent(val state: Int)

    /**
     * 国家筛选变化事件
     */
    data class CountryFilterChangeEvent(val countryCode: String?)
}