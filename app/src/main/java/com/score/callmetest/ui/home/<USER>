package com.score.callmetest.ui.home

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.graphics.toColorInt
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.score.callmetest.databinding.FragmentWallBinding
import com.score.callmetest.entity.CustomEvents
import com.score.callmetest.manager.BannerManager
import com.score.callmetest.manager.FollowManager
import com.score.callmetest.manager.UserInfoManager
import com.score.callmetest.network.BroadcasterModel
import com.score.callmetest.ui.base.BaseFragment
import com.score.callmetest.ui.home.adapter.BroadcasterAdapter
import com.score.callmetest.ui.widget.BlockEvent
import com.score.callmetest.util.EventBus

class WallFragment : BaseFragment<FragmentWallBinding, WallViewModel>() {
    private lateinit var broadcasterAdapter: BroadcasterAdapter
    private var isFragmentVisible = false
    private var tab1Name: String = ""
    private var tab2Name: String = ""
    private var emptyViewInflated: View? = null

    companion object {
        private const val ARG_TAB1_ID = "tab1_id"
        private const val ARG_TAB2_ID = "tab2_id"
        private const val ARG_REGION = "region"

        fun newInstance(tab1Name: String, tab2Name: String, region: String?): WallFragment {
            return WallFragment().apply {
                arguments = Bundle().apply {
                    putString(ARG_TAB1_ID, tab1Name)
                    putString(ARG_TAB2_ID, tab2Name)
                    putString(ARG_REGION, region)
                }
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            tab1Name = it.getString(ARG_TAB1_ID, "Popular") ?: "Popular"
            tab2Name = it.getString(ARG_TAB2_ID, "All") ?: "All"
        }
    }

    override fun getViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?
    ): FragmentWallBinding {
        return FragmentWallBinding.inflate(inflater, container, false)
    }

    override fun getViewModelClass() = WallViewModel::class.java

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): android.view.View? {
        binding = getViewBinding(inflater, container)
        // 通过自定义工厂传参创建 ViewModel
        viewModel = WallViewModel(tab1Name, tab2Name)
        // region参数在这里设置
        val region = arguments?.getString(ARG_REGION)
        viewModel.updateRegion(region)

        initView()
        initListener()
        initData()
        return binding.root
    }

    override fun initView() {
        broadcasterAdapter = BroadcasterAdapter()
        val layoutManager = GridLayoutManager(requireContext(), 2) // 假设每行2列

        layoutManager.spanSizeLookup = object : GridLayoutManager.SpanSizeLookup() {
            /** 这个函数的意思是占据的空间大小。
             *  banner显示一行，相当于占据2个位置（layoutManager.spanCount == 2）
             */
            override fun getSpanSize(position: Int): Int {
                // 在获取 item view type 之前，确保 position 是有效的
                if (position < 0 || position >= broadcasterAdapter.itemCount) {
                    return layoutManager.spanCount
                }
                val size = when (broadcasterAdapter.getItemViewType(position)) {
                    BroadcasterAdapter.VIEW_TYPE_BANNER -> layoutManager.spanCount // Banner 占据整行
                    BroadcasterAdapter.VIEW_TYPE_BROADCASTER -> 1
                    else -> 2 // 默认情况
                }
                return size
            }
        }

        binding.recyclerView.layoutManager = layoutManager
        binding.recyclerView.adapter = broadcasterAdapter
        binding.swipeRefreshLayout.setColorSchemeColors("#FFFF3DAD".toColorInt())

        setupObservers()
    }

    private var hasBanner = false
    private fun setupObservers() {
        viewModel.onDataChanged =
            { broadcasterModels, hasMore -> // broadcasterModels 是 List<BroadcasterModel>
                val listItems = mutableListOf<BroadcasterAdapter.ListItem>()
                if (viewModel.isFirstPage() && isFragmentVisible) {
                    // 避免首次进来没数据
                    broadcasterModels.map { it.userId }.toTypedArray().forEachIndexed({ index, userId ->
                        if(index < 8){
                            // 首次只加载8个就够了
                            UserInfoManager.addUserToRefreshQueue(userId)
                        }else {
                            return@forEachIndexed
                        }
                    })
                    refreshVisibleBroadcasters()
                }
                BannerManager.getBannerInfo(
                    scope = lifecycleScope,
                    callback = {
                        if (tab1Name == "Popular" && tab2Name == "All") {
                            if (!it.isNullOrEmpty()) {
                                hasBanner = true
                                listItems.add(BroadcasterAdapter.ListItem.BannerItem(it))
                            }
                        }

                        // 2. 添加主播数据项
                        broadcasterModels.forEach { model ->
                            listItems.add(BroadcasterAdapter.ListItem.BroadcasterItem(model))
                        }

                        // 只有在首次加载或刷新时，才根据 broadcasterModels.isEmpty() 判断空状态
                        if (viewModel.isFirstPage()) {
                            updateEmptyState(broadcasterModels.isEmpty())
                        }

                        broadcasterAdapter.submitListHasMore(listItems, hasMore)
                        binding.swipeRefreshLayout.isRefreshing = false
                    }
                )
            }

        viewModel.onError = {
            binding.swipeRefreshLayout.isRefreshing = false
            updateEmptyState(broadcasterAdapter.itemCount <= 1) // 1 表示只有 banner
        }

        // 监听状态变化
        EventBus.observe(
            scope = lifecycleScope,
            eventType = CustomEvents.NewStatusEvent::class.java
        ) { event ->
            val newStatusMap = event.statusMap
            val visibleBroadcasters = getVisibleBroadcasters().map {
                newStatusMap[it.userId]?.let { status ->
                    it.copy(status = status)
                }?: it
            }
            broadcasterAdapter.updateBroadcasterStatus(visibleBroadcasters)
        }
    }

    override fun initListener() {
        super.initListener()

        // 监听滑动加载更多和滚动停止刷新
        binding.recyclerView.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                super.onScrollStateChanged(recyclerView, newState)
                val isScrolling = newState != RecyclerView.SCROLL_STATE_IDLE
                viewModel.onScrollStateChanged(isScrolling) {
                    if (isFragmentVisible) {
                        refreshVisibleBroadcasters()
                    }
                }
                EventBus.post(HomeViewModel.WallScrollEvent(newState))
            }

            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)
                // 只在向下滚动时检查加载更多
                if (dy > 0) {
                    val lm = recyclerView.layoutManager as GridLayoutManager
                    val visibleItemCount = lm.childCount
                    val totalItemCount = lm.itemCount
                    val firstVisibleItemPosition = lm.findFirstVisibleItemPosition()
                    // 判断是否需要加载更多
                    if (!viewModel.isLoading() && viewModel.hasMoreData()) {
                        if ((visibleItemCount + firstVisibleItemPosition) >= totalItemCount && firstVisibleItemPosition >= 0) {
                            broadcasterAdapter.updateFooterState(0) // 0: 加载中
                            viewModel.loadMore()
                        }
                    }
                }
            }
        })

        // 下拉刷新监听
        binding.swipeRefreshLayout.setOnRefreshListener {
            viewModel.refresh()
        }

        if (tab1Name == "Followed") {
            // 监听关注和取消关注事件，当变化时重新加载列表
            EventBus.observe(this, FollowManager.FollowListRefreshEvent::class.java) { event ->
                if (event.type == 1) {
                    // 当关注数量变化时，重新加载关注列表
                    viewModel.refresh()
                }

            }
        }

        EventBus.observe(this, BlockEvent::class.java) { event ->
            // 当有用户被拉黑时，从列表中移除被拉黑的用户
            if(event.isBlocked) {
                viewModel.removeBlockedUser(event.userId)
            }
        }

        // 监听国家筛选变化事件
        EventBus.observe(this, HomeViewModel.CountryFilterChangeEvent::class.java) { event ->
            // 只有Popular标签下的All子标签才响应国家筛选
            if (tab1Name == "Popular" && tab2Name == "All") {
                // 显示加载动画
                binding.swipeRefreshLayout.isRefreshing = true
                viewModel.updateRegion(event.countryCode)
            }
        }
    }

    override fun initData() {
        super.initData()

        // 首次加载数据
        binding.swipeRefreshLayout.isRefreshing = true
        viewModel.refresh()
    }

    // getVisibleBroadcasters 方法需要更新
    private fun getVisibleBroadcasters(): List<BroadcasterModel> {
        val layoutManager =
            binding.recyclerView.layoutManager as? GridLayoutManager ?: return emptyList()
        val firstVisible = layoutManager.findFirstVisibleItemPosition()
        val lastVisible = layoutManager.findLastVisibleItemPosition()

        // 确保索引有效
        if (firstVisible < 0 || lastVisible < 0 || firstVisible > lastVisible) return emptyList()


        val visibleBroadcasters = mutableListOf<BroadcasterModel>()
        // 遍历可见范围内的项
        for (i in firstVisible..lastVisible) {
            // 再次检查索引是否在当前列表范围内，因为数据可能在异步更新
            if (i < broadcasterAdapter.currentList.size) { // 边界检查
                val item = broadcasterAdapter.currentList[i]
                if (item is BroadcasterAdapter.ListItem.BroadcasterItem) {
                    visibleBroadcasters.add(item.broadcaster)
                }
            }
        }
        return visibleBroadcasters
    }

    private fun refreshVisibleBroadcasters() {
        val visibleBroadcasters = getVisibleBroadcasters()
        UserInfoManager.addUserToRefreshQueue(*visibleBroadcasters.map { it.userId }.toTypedArray())
        UserInfoManager.refreshUserStatus(lifecycleScope,true)
    }

    /**
     * 更新空状态显示
     * @param isEmpty 数据是否为空
     */
    // updateEmptyState 方法也需要考虑 Banner
    private fun updateEmptyState(isBroadcasterListEmpty: Boolean) {
        // 假设 emptyViewInflated 是你的空视图的 ViewStub 或实际 View
        // binding.emptyView 是 ViewStub
        if (isBroadcasterListEmpty) {
            // 如果主播列表为空，但我们总是有 Banner，所以列表本身不完全为空
            // 你需要决定这种情况下是否仍显示 "空列表" 状态
            // 这里假设如果只有 Banner，也算作一种 "空" (没有实际内容)
            if (broadcasterAdapter.itemCount <= 1
                || (hasBanner && broadcasterAdapter.itemCount <= 2)) { // 只有 Banner Item 和 footer
                if (emptyViewInflated == null && binding.emptyView.parent != null) { // 确保 ViewStub 存在且未被 inflate
                    emptyViewInflated = binding.emptyView.inflate()
                }
                if (emptyViewInflated == null && binding.emptyView.parent != null) {
                    try {
                        emptyViewInflated = binding.emptyView.inflate() // Inflate ViewStub
                    } catch (e: Exception) {
                        // ViewStub 可能已经被 inflate 或者从父视图移除
//                        Log.e("WallFragment", "Error inflating empty view from ViewStub", e)
                        // 可以尝试直接查找 R.id.emptyViewInflated，如果 ViewStub 的 inflatedId 设置正确
                        emptyViewInflated = view?.findViewById(binding.emptyView.inflatedId)
                    }
                }
                emptyViewInflated?.visibility = View.VISIBLE
                binding.recyclerView.visibility = View.VISIBLE
            } else {
                // 这种情况理论上不应该发生：主播列表空，但 adapter item count > 1
                emptyViewInflated?.visibility = View.GONE
                binding.recyclerView.visibility = View.VISIBLE
            }
        } else {
            emptyViewInflated?.visibility = View.GONE
            binding.recyclerView.visibility = View.VISIBLE
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun onResume() {
        super.onResume()
        isFragmentVisible = true
        // 当 Fragment 重新可见时，可以考虑刷新可见主播的状态或重启状态轮询
        // 例如，如果 ViewModel 中有基于 Fragment 可见性的状态更新逻辑
        if (viewModel.isLoading() || broadcasterAdapter.itemCount > 1) { // 避免在完全空列表时启动
            if (isFragmentVisible) {
                refreshVisibleBroadcasters()
            }
        }
    }

    override fun onPause() {
        super.onPause()
        isFragmentVisible = false
    }

    override fun onDestroyView() {
        viewModel.clear()
        super.onDestroyView()
    }
}