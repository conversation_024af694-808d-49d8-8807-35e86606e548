package com.score.callmetest.ui.profile

import android.net.Uri
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import androidx.recyclerview.widget.RecyclerView
import com.score.callmetest.R
import com.score.callmetest.util.GlideUtils
import com.score.callmetest.util.click

class PhotoAdapter(
    private val photoList: List<Uri>,
    private val onDeleteClick: (Int) -> Unit,
    private val onAddClick: () -> Unit
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    companion object {
        private const val TYPE_PHOTO = 0
        private const val TYPE_ADD_BUTTON = 1
    }

    private var showAddButton = true
    private var onPhotoClick: ((Uri, Int) -> Unit)? = null

    fun updateAddButtonVisibility(show: Boolean) {
        showAddButton = show
        notifyDataSetChanged()
    }

    fun setOnPhotoClickListener(listener: (Uri, Int) -> Unit) {
        onPhotoClick = listener
    }

    override fun getItemViewType(position: Int): Int {
        return if (position < photoList.size) TYPE_PHOTO else TYPE_ADD_BUTTON
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            TYPE_PHOTO -> {
                val view = LayoutInflater.from(parent.context)
                    .inflate(R.layout.item_photo, parent, false)
                PhotoViewHolder(view)
            }
            TYPE_ADD_BUTTON -> {
                val view = LayoutInflater.from(parent.context)
                    .inflate(R.layout.item_add_photo, parent, false)
                AddButtonViewHolder(view)
            }
            else -> throw IllegalArgumentException("Invalid view type")
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (holder) {
            is PhotoViewHolder -> {
                val photoUri = photoList[position]
                holder.bind(photoUri, position)
            }
            is AddButtonViewHolder -> {
                holder.bind()
            }
        }
    }

    override fun getItemCount(): Int {
        return photoList.size + if (showAddButton) 1 else 0
    }

    inner class PhotoViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val photoImageView: ImageView = itemView.findViewById(R.id.ivPhoto)
        private val deleteButton: ImageView = itemView.findViewById(R.id.btnDelete)

        fun bind(photoUri: Uri, position: Int) {
            GlideUtils.load(
                context = itemView.context,
                url = photoUri,
                imageView = photoImageView
            )

            deleteButton.click {
                onDeleteClick(position)
            }
            photoImageView.click {
                onPhotoClick?.invoke(photoUri, position)
            }
        }
    }

    inner class AddButtonViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val addButton: ImageView = itemView.findViewById(R.id.btnAddPhoto)

        fun bind() {
            addButton.click {
                onAddClick()
            }
        }
    }
} 