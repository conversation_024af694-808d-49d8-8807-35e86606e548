package com.score.callmetest.ui.widget.decoration

import android.graphics.Rect
import android.view.View
import androidx.recyclerview.widget.RecyclerView

/**
 * 自定义RecyclerView间距空白divide---横向的
 */
open class SpaceHorItemDecoration (private val space: Int) : RecyclerView.ItemDecoration() {
    override fun getItemOffsets(
        outRect: Rect,
        view: View,
        parent: RecyclerView,
        state: RecyclerView.State
    ) {
        // 对每个项添加右侧间距，最后一个项不添加
        val position = parent.getChildAdapterPosition(view)
        if (position != parent.adapter?.itemCount?.minus(1)) {
            outRect.right = space
        }
    }
}

/**
 * 自定义RecyclerView间距空白divide---垂直的
 * 用于垂直列表，为每个item添加底部间距
 */
open class SpaceVerticalItemDecoration(private val space: Int) : RecyclerView.ItemDecoration() {
    override fun getItemOffsets(
        outRect: Rect,
        view: View,
        parent: RecyclerView,
        state: RecyclerView.State
    ) {
        // 对每个项添加底部间距，最后一个项不添加
        val position = parent.getChildAdapterPosition(view)
        if (position != parent.adapter?.itemCount?.minus(1)) {
            outRect.bottom = space
        }
    }
}