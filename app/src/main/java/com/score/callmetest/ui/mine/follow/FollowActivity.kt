package com.score.callmetest.ui.mine.follow

import android.graphics.Typeface
import android.view.View
import android.view.ViewTreeObserver
import android.widget.TextView
import androidx.core.content.res.ResourcesCompat
import androidx.fragment.app.Fragment
import androidx.viewpager2.adapter.FragmentStateAdapter
import androidx.viewpager2.widget.ViewPager2
import com.google.android.material.tabs.TabLayout
import com.score.callmetest.R
import com.score.callmetest.databinding.ActivityFollowBinding
import com.score.callmetest.manager.GlobalManager
import com.score.callmetest.ui.base.BaseActivity
import com.score.callmetest.ui.message.MessageFollowingFragment
import com.score.callmetest.util.ClickUtils
import com.score.callmetest.util.DrawableUtils
import timber.log.Timber

/**
 * 关注/粉丝页面的独立Activity
 * 直接管理TabLayout和ViewPager2，不再使用中间的FollowFragment容器
 */
class FollowActivity : BaseActivity<ActivityFollowBinding, FollowViewModel>() {

    private lateinit var mPagerAdapter: FollowPagerAdapter

    // 初始标签页位置，默认为0（Following），1为Followers
    private var initialTabPosition = 0

    override fun getViewBinding(): ActivityFollowBinding {
        return ActivityFollowBinding.inflate(layoutInflater)
    }

    override fun getViewModelClass() = FollowViewModel::class.java

    override fun initView() {
        // 获取传入的初始标签页位置
        initialTabPosition = intent.getIntExtra("initial_tab", 0)

        GlobalManager.addViewStatusBarTopMargin(this, binding.tabFollow)

        try {
            setupTabLayout()
            setupViewPager()

            // 根据初始标签页位置设置当前页
            binding.viewPager.post {
                binding.viewPager.setCurrentItem(initialTabPosition, false)
                // 确保标签也被选中
                binding.tabFollow.getTabAt(initialTabPosition)?.select()
            }
        } catch (e: Exception) {
            Timber.tag("FollowActivity").w("Error in initView: $e")
        }
    }

    private fun setupTabLayout() {
        with(binding.tabFollow) {
            tabRippleColor = null
            // 添加Following标签
            addTab(newTab().apply {
                customView = createCustomTabView("Following", selected = initialTabPosition == 0)
            })

            // 添加Followers标签
            addTab(newTab().apply {
                customView = createCustomTabView("Followers", selected = initialTabPosition == 1)
            })
        }
    }

    private fun setupViewPager() {
        // 初始化ViewPager适配器
        mPagerAdapter = FollowPagerAdapter(this)

        // 配置ViewPager
        binding.viewPager.apply {
            adapter = mPagerAdapter
            offscreenPageLimit = 2 // 预加载页面数量
        }
        GlobalManager.setNeverOverScroll(binding.viewPager)
    }

    override fun initListener() {
        // 返回按钮点击事件
        ClickUtils.setOnIsolatedClickListener(binding.btnReturn) {
            onBackPressedDispatcher.onBackPressed()
        }

        // 监听Tab切换
        binding.tabFollow.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab) {
                updateTabStyle(tab.customView, true)
                binding.viewPager.currentItem = tab.position
            }

            override fun onTabUnselected(tab: TabLayout.Tab) {
                updateTabStyle(tab.customView, false)
            }

            override fun onTabReselected(tab: TabLayout.Tab) {
                // 不需要处理
            }
        })

        // 监听ViewPager页面切换
        binding.viewPager.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                binding.tabFollow.getTabAt(position)?.select()
            }
        })
    }

    /**
     * 创建自定义Tab视图
     * @param text Tab文本
     * @param customView 现有的自定义视图（如果有）
     * @param selected 是否选中
     * @return 自定义Tab视图
     */
    private fun createCustomTabView(text: String? = null, customView: View? = null, selected: Boolean = false): View {
        val view = customView ?: layoutInflater.inflate(R.layout.tab_custom, null)
        updateTabStyle(view, selected, text)
        return view
    }

    /**
     * 更新Tab样式
     * @param view Tab视图
     * @param selected 是否选中
     * @param text Tab文本（可选）
     */
    private fun updateTabStyle(view: View?, selected: Boolean, text: String? = null) {
        if (view == null) return

        val textView = view.findViewById<TextView>(R.id.tab_text)
        val indicator = view.findViewById<View>(R.id.tab_indicator)

        // 设置文本（如果提供）
        if (!text.isNullOrEmpty()) {
            textView.text = text
        }

        if (selected) {
            textView?.textSize = 22f
            textView?.paint?.isFakeBoldText = false
            textView?.setTypeface(ResourcesCompat.getFont(this,  R.font.roboto_bold))
            indicator?.visibility = View.VISIBLE

            // 使用ViewTreeObserver确保布局稳定
            textView?.viewTreeObserver?.addOnGlobalLayoutListener(object :
                ViewTreeObserver.OnGlobalLayoutListener {
                override fun onGlobalLayout() {
                    textView.viewTreeObserver.removeOnGlobalLayoutListener(this)
                    if (textView.width > 0 && indicator != null) {
                        indicator.layoutParams.width = textView.width / 2
                        indicator.layoutParams.height = textView.height / 2
                        indicator.background = DrawableUtils.createRoundRectDrawable(
                            resources.getColor(R.color.tab_indicator_color),
                            textView.height / 4f
                        )
                        indicator.requestLayout()
                    }
                }
            })
        } else {
            textView?.textSize = 15f
            textView?.paint?.isFakeBoldText = false
            textView?.setTypeface(Typeface.create("sans-serif", Typeface.NORMAL))
            indicator?.visibility = View.GONE
        }
    }

    /**
     * ViewPager适配器
     */
    private inner class FollowPagerAdapter(activity: FollowActivity) : FragmentStateAdapter(activity) {
        override fun getItemCount(): Int = binding.tabFollow.tabCount

        override fun createFragment(position: Int): Fragment {
            return when (position) {
                0 -> MessageFollowingFragment()  // 使用MessageFollowingFragment，FollowingFragment保留用于测试
                1 -> FollowersFragment()
                // 其他类型主动抛出异常
                else -> throw IllegalArgumentException("Invalid position: $position")
            }
        }
    }
}
