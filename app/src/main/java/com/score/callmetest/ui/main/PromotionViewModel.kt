package com.score.callmetest.ui.main

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.score.callmetest.manager.CountdownManager
import com.score.callmetest.util.ThreadUtils
import com.score.callmetest.util.TimeUtils
import timber.log.Timber

/**
 * 用于管理促销活动倒计时的 ViewModel。
 * 此 ViewModel 作为倒计时状态的唯一可信来源，
 * 在 MainActivity 和 PromotionDialogFragment 之间共享。
 */
class PromotionViewModel : ViewModel() {

    // 向UI层暴露的 LiveData
    private val _countdownText = MutableLiveData<String>()
    val countdownText: LiveData<String> = _countdownText

    private val _isCountdownFinished = MutableLiveData<Boolean>(false)
    val isCountdownFinished: LiveData<Boolean> = _isCountdownFinished

    // 内部状态
    private var listenerId: String? = null
    private var countdownType: String? = null
    private var countdownCode: String? = null

    /**
     * 如果当前 ViewModel 尚未运行倒计时，则初始化并启动它。
     * @param type 倒计时事件的类型。
     * @param code 倒计时事件的唯一标识码。
     * @param remainMilliseconds 剩余时间的毫秒数。
     */
    fun startCountdown(type: String, code: String, remainMilliseconds: Long) {
        // 如果同一个事件的倒计时已在此 ViewModel 中运行，则阻止重复初始化
        if (listenerId != null && countdownType == type && countdownCode == code) {
            Timber.d("Countdown for $code/$type already started in this ViewModel.")
            return
        }

        cleanupListener()

        this.countdownType = type
        this.countdownCode = code

        CountdownManager.initOrUpdateCountdown(type, code, remainMilliseconds)

        // 检查倒计时是否已经过期
        if (CountdownManager.isExpired(type, code)) {
            handleCountdownFinish()
            return
        }

        // 开始监听倒计时
        listenerId = CountdownManager.startCountdown(
            type = type,
            code = code,
            onTick = { remainingMillis ->
                ThreadUtils.runOnMain {
                    _countdownText.value = TimeUtils.formatSecondsToTime(remainingMillis / 1000)
                    _isCountdownFinished.value = false
                }
            },
            onFinish = {
                ThreadUtils.runOnMain {
                    handleCountdownFinish()
                }
            }
        )

        Timber.d("PromotionViewModel started listening to countdown $code/$type with listenerId: $listenerId")
    }

    private fun handleCountdownFinish() {
        _countdownText.value = "00:00"
        _isCountdownFinished.value = true
        cleanupListener() // 结束后立即清理
        Timber.d("Countdown finished for $countdownCode/$countdownType")
    }

    /**
     * 从 CountdownManager 中移除监听器。
     */
    private fun cleanupListener() {
        if (listenerId != null && countdownType != null && countdownCode != null) {
            Timber.d("Cleaning up listener $listenerId for $countdownCode/$countdownType")
            CountdownManager.removeListener(countdownType, countdownCode, listenerId!!)
            listenerId = null
            countdownType = null
            countdownCode = null
        } else {
            Timber.d("Cleanup called but no active listener to remove.")
        }
    }

    /**
     * 当 ViewModel 即将被销毁时，此方法会被调用。
     * 这是清理资源（例如移除倒计时监听器）的绝佳位置。
     */
    override fun onCleared() {
        super.onCleared()
        Timber.d("PromotionViewModel onCleared. Performing final cleanup.")
        cleanupListener()
    }
}

