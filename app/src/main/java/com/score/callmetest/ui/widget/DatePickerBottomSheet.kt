package com.score.callmetest.ui.widget

import android.app.Dialog
import android.graphics.Color
import android.os.Build
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.NumberPicker
import android.widget.TextView
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.score.callmetest.R
import com.score.callmetest.util.DisplayUtils
import com.score.callmetest.util.DrawableUtils
import com.score.callmetest.util.click
import java.util.Calendar

/**
 * 生日选择器
 */
class DatePickerBottomSheet(
    private val maxYear: Int,
    private val onDateSelected: (year: Int, month: Int, day: Int) -> Unit,
    private val birthday: String? = null
) : BottomSheetDialogFragment() {

    private lateinit var yearPicker: NumberPicker
    private lateinit var monthPicker: NumberPicker
    private lateinit var dayPicker: NumberPicker

    private val calendar = Calendar.getInstance()
    private var selectedYear: Int
    private var selectedMonth: Int
    private var selectedDay: Int

    init {
        if (!birthday.isNullOrEmpty()) {
            try {
                val dateFormat = java.text.SimpleDateFormat("yyyy-MM-dd", java.util.Locale.getDefault())
                val date = dateFormat.parse(birthday)
                if (date != null) {
                    calendar.time = date
                }
            } catch (_: Exception) {}
        }
        selectedYear = calendar.get(Calendar.YEAR).coerceAtMost(maxYear)
        selectedMonth = calendar.get(Calendar.MONTH)
        selectedDay = calendar.get(Calendar.DAY_OF_MONTH)
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = super.onCreateDialog(savedInstanceState)
        // 禁用默认背景
        dialog.setOnShowListener {
            val bottomSheet = dialog.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet)
            bottomSheet?.setBackgroundResource(android.R.color.transparent)
        }
        return dialog
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        val view = inflater.inflate(R.layout.bottom_sheet_date_picker, container, false)

        // 设置底部弹窗内容区域圆角背景（内容区域）
        DrawableUtils.createRoundRectDrawable(
            Color.WHITE,
            floatArrayOf(
                DisplayUtils.dp2pxInternalFloat(20f), DisplayUtils.dp2pxInternalFloat(20f), // 左上
                DisplayUtils.dp2pxInternalFloat(20f), DisplayUtils.dp2pxInternalFloat(20f), // 右上
                0f, 0f, // 右下
                0f, 0f  // 左下
            )
        ).also { view.background = it }


        monthPicker = view.findViewById(R.id.month_picker)
        dayPicker = view.findViewById(R.id.day_picker)
        yearPicker = view.findViewById(R.id.year_picker)
        val btnCancel = view.findViewById<TextView>(R.id.btn_cancel)
        val btnConfirm = view.findViewById<TextView>(R.id.btn_confirm)


        // 年份
        yearPicker.minValue = 1900
        yearPicker.maxValue = maxYear
        yearPicker.value = selectedYear

        // 月份
        monthPicker.minValue = 0
        monthPicker.maxValue = 11
        monthPicker.value = selectedMonth
        monthPicker.displayedValues = arrayOf(
            "January", "February", "March", "April", "May", "June",
            "July", "August", "September", "October", "November", "December"
        )

        // 日期
        updateDaysInMonth()
        dayPicker.value = selectedDay

        // 监听
        yearPicker.setOnValueChangedListener { _, _, newVal ->
            selectedYear = newVal
            updateDaysInMonth()
        }
        monthPicker.setOnValueChangedListener { _, _, newVal ->
            selectedMonth = newVal
            updateDaysInMonth()
        }
        dayPicker.setOnValueChangedListener { _, _, newVal ->
            selectedDay = newVal
        }

        btnCancel.click { dismiss() }
        btnConfirm.click {
            onDateSelected(selectedYear, selectedMonth + 1, selectedDay)
            dismiss()
        }
        return view
    }

    private fun updateDaysInMonth() {
        calendar.set(Calendar.YEAR, selectedYear)
        calendar.set(Calendar.MONTH, selectedMonth)
        val daysInMonth = calendar.getActualMaximum(Calendar.DAY_OF_MONTH)

        dayPicker.minValue = 1
        dayPicker.maxValue = daysInMonth


        if (selectedDay > daysInMonth) {
            selectedDay = daysInMonth
            dayPicker.value = selectedDay
        }
    }
}