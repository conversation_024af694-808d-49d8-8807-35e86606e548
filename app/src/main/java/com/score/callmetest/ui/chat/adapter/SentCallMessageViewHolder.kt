package com.score.callmetest.ui.chat.adapter

import android.view.View
import androidx.recyclerview.widget.RecyclerView
import com.score.callmetest.CallType
import com.score.callmetest.Constant
import com.score.callmetest.R
import com.score.callmetest.databinding.ItemChatMessageCallRightBinding
import com.score.callmetest.entity.ChatMessageEntity
import com.score.callmetest.entity.MessageStatus
import com.score.callmetest.util.ClickUtils
import com.score.callmetest.util.GlideUtils
import com.score.callmetest.util.TimeUtils
import timber.log.Timber

/**
 * 发送通话记录ViewHolder
 */
internal class SentCallMessageViewHolder(
    private val binding: ItemChatMessageCallRightBinding,
    private val mChatMessageListeners: ChatAdapterListeners
) : RecyclerView.ViewHolder(binding.root), MessageHolder {

    private var mCurrentMessage: ChatMessageEntity? = null

    init {
        // 点击事件
        ClickUtils.setOnGlobalDebounceClickListener(binding.callContentParent) {
            mCurrentMessage?.let { message ->
                mChatMessageListeners.mOnCallRecordClickListener?.invoke(message)
            }
        }

        // 头像点击
        ClickUtils.setOnGlobalDebounceClickListener(binding.ivAvatar) {
            mCurrentMessage?.let { message ->
                mChatMessageListeners.mOnAvatarClickListener?.invoke(message,binding.ivAvatar)
            }
        }
    }

    override fun bind(message: ChatMessageEntity) {
        mCurrentMessage = message
        
        // 设置通话状态文本和图标
        setupCallContent(message)

        // 加载头像
        GlideUtils.load(
            view = binding.ivAvatar,
            url = message.senderAvatar,
            placeholder = R.drawable.placeholder,
            error = R.drawable.placeholder,
            isCircle = true
        )
    }

    /**
     * 设置通话内容显示
     */
    private fun setupCallContent(message: ChatMessageEntity) {
        val callType = message.contentType ?: return

        when (callType) {
            CallType.INCOMING_CALL, CallType.OUTGOING_CALL -> {
                // 接通的通话，显示通话时长
                val duration = message.mediaDuration // 通话时长（秒）
                val formattedDuration = TimeUtils.formatCallDuration(duration)
                binding.tvCallStatus.text = binding.tvCallStatus.context.getString(R.string.call_duration,formattedDuration)
                binding.ivCallIcon.setImageResource(R.drawable.call_chat_connected)
            }
            CallType.CANCELLED_CALL -> {
                binding.tvCallStatus.text = binding.tvCallStatus.context.getString(R.string.call_cancelled)
                binding.ivCallIcon.setImageResource(R.drawable.call_chat_cancel)
            }
            CallType.UNANSWERED_CALL -> {
                binding.tvCallStatus.text = binding.tvCallStatus.context.getString(R.string.call_unanswered)
                binding.ivCallIcon.setImageResource(R.drawable.call_chat_cancel)
            }
            CallType.REJECTED_CALL -> {
                binding.tvCallStatus.text = binding.tvCallStatus.context.getString(R.string.call_rejected)
                binding.ivCallIcon.setImageResource(R.drawable.call_chat_cancel)
            }
            CallType.MISSED_CALL -> {
                binding.tvCallStatus.text = binding.tvCallStatus.context.getString(R.string.call_missed)
                binding.ivCallIcon.setImageResource(R.drawable.call_chat_cancel)
            }
            else -> {
                binding.tvCallStatus.text = binding.tvCallStatus.context.getString(R.string.call_cancelled)
                binding.ivCallIcon.setImageResource(R.drawable.call_chat_cancel)
                Timber.w("Unknown call type: $callType")
            }
        }
    }
}
