package com.score.callmetest.ui.widget

import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import com.score.callmetest.R
import com.score.callmetest.databinding.DialogRechargeResultBinding
import com.score.callmetest.util.click

class RechargeResultDialog(
    context: Context,
    private val isSuccess: Boolean,
    private val orderNo: String
) : Dialog(context) {

    private lateinit var binding: DialogRechargeResultBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = DialogRechargeResultBinding.inflate(LayoutInflater.from(context))
        setContentView(binding.root)

        setupUI()
        setupButtons()
    }

    private fun setupUI() {
        if (isSuccess) {
            // 充值成功
            binding.ivResultIcon.setImageResource(R.drawable.coin)
            binding.tvTitle.text = "Recharge Success"
            binding.tvMessage.text = "Your coins have been added to your account successfully!"
        } else {
            // 充值失败
            binding.ivResultIcon.setImageResource(R.drawable.ic_close)
            binding.tvTitle.text = "Recharge Failed"
            binding.tvMessage.text = "Sorry, your recharge was not successful. Please try again."
        }

        binding.tvOrderNo.text = "Order: $orderNo"
    }

    private fun setupButtons() {
        binding.btnOk.click {
            dismiss()
        }
    }
} 