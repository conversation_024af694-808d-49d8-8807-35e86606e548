package com.score.callmetest.ui.chat.adapter

import com.score.callmetest.entity.ChatMessageEntity
import com.score.callmetest.entity.MessageStatus

interface MessageHolder {

    /**
     * 绑定消息数据
     */
    fun bind(message: ChatMessageEntity)

    /**
     * 更新发送状态--接收信息不需要继承
     * @param [status] 发送状态
     */
    fun updateStatus(status: MessageStatus) {}

    /**
     * 更新实体
     * @param [messageEntity] 消息实体
     */
    fun updateEntity(messageEntity: ChatMessageEntity) {}

}