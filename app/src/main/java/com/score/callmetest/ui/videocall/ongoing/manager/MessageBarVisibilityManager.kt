package com.score.callmetest.ui.videocall.ongoing.manager

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ObjectAnimator
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.View
import androidx.recyclerview.widget.RecyclerView
import com.score.callmetest.ui.widget.GradientRecyclerView

/**
 * 消息栏可见性管理器
 * 负责管理消息栏的显示和隐藏逻辑
 * 
 * 隐藏规则：
 * 1. 收到/发送新消息则显示消息栏，若5s内没有新消息则隐藏消息栏
 * 2. 点击屏幕可显示消息栏，再次点击可隐藏消息栏
 * 3. 每次显示消息栏时间为5s
 * 4. 点击输入框拉起输入法时，显示消息栏
 * 5. RecyclerView上下滑动过程中显示消息栏，停止滑动后5s隐藏
 * 6. 隐藏动画：淡化效果
 */
class MessageBarVisibilityManager(
    private val messageRecyclerView: GradientRecyclerView
) {
    
    companion object {
        private const val TAG = "MessageBarVisibility"
        private const val AUTO_HIDE_DELAY = 5000L // 5秒自动隐藏
        private const val ANIMATION_DURATION = 300L // 动画时长
    }
    
    private val handler = Handler(Looper.getMainLooper())
    private var hideRunnable: Runnable? = null
    private var isVisible = false
    private var isAnimating = false
    private var currentAnimator: ObjectAnimator? = null
    
    // 滚动监听器
    private val scrollListener = object : RecyclerView.OnScrollListener() {
        override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
            super.onScrollStateChanged(recyclerView, newState)
            
            when (newState) {
                RecyclerView.SCROLL_STATE_DRAGGING,
                RecyclerView.SCROLL_STATE_SETTLING -> {
                    // 开始滚动，显示消息栏
                    showMessageBar()
                }
                RecyclerView.SCROLL_STATE_IDLE -> {
                    // 停止滚动，5秒后隐藏
                    scheduleAutoHide()
                }
            }
        }
    }
    
    init {
        // 添加滚动监听器
        messageRecyclerView.addScrollListener(scrollListener)
        
        // 初始状态为隐藏
        messageRecyclerView.visibility = View.INVISIBLE
        isVisible = false
    }
    
    /**
     * 显示消息栏
     * @param autoHide 是否自动隐藏，默认为true
     */
    fun showMessageBar(autoHide: Boolean = true) {
        if (isAnimating) return
        
        Log.d(TAG, "显示消息栏")
        
        // 取消之前的隐藏任务
        cancelAutoHide()
        
        if (!isVisible) {
            isVisible = true
            isAnimating = true
            
            // 设置初始状态
            messageRecyclerView.visibility = View.VISIBLE
            messageRecyclerView.alpha = 0f
            
            // 淡入动画
            currentAnimator = ObjectAnimator.ofFloat(messageRecyclerView, "alpha", 0f, 1f).apply {
                duration = ANIMATION_DURATION
                addListener(object : AnimatorListenerAdapter() {
                    override fun onAnimationEnd(animation: Animator) {
                        isAnimating = false
                        currentAnimator = null
                        
                        // 如果需要自动隐藏，则安排隐藏任务
                        if (autoHide) {
                            scheduleAutoHide()
                        }
                    }
                })
                start()
            }
        } else if (autoHide) {
            // 已经显示，重新安排隐藏时间
            scheduleAutoHide()
        }
    }
    
    /**
     * 隐藏消息栏
     */
    fun hideMessageBar() {
        if (isAnimating || !isVisible) return
        
        Log.d(TAG, "隐藏消息栏")
        
        // 取消自动隐藏任务
        cancelAutoHide()
        
        isVisible = false
        isAnimating = true
        
        // 淡出动画
        currentAnimator = ObjectAnimator.ofFloat(messageRecyclerView, "alpha", 1f, 0f).apply {
            duration = ANIMATION_DURATION
            addListener(object : AnimatorListenerAdapter() {
                override fun onAnimationEnd(animation: Animator) {
                    messageRecyclerView.visibility = View.INVISIBLE
                    isAnimating = false
                    currentAnimator = null
                }
            })
            start()
        }
    }
    
    /**
     * 切换消息栏可见性
     */
    fun toggleMessageBar() {
        if (isVisible) {
            hideMessageBar()
        } else {
            showMessageBar()
        }
    }
    
    /**
     * 当收到新消息时调用
     */
    fun onNewMessage() {
        Log.d(TAG, "收到新消息，显示消息栏")
        showMessageBar()
    }
    
    /**
     * 当发送消息时调用
     */
    fun onMessageSent() {
        Log.d(TAG, "发送消息，显示消息栏")
        showMessageBar()
    }
    
    /**
     * 当输入框获得焦点时调用
     */
    fun onInputFocused() {
        Log.d(TAG, "输入框获得焦点，显示消息栏")
        showMessageBar(autoHide = false) // 输入时不自动隐藏
    }
    
    /**
     * 当输入框失去焦点时调用
     */
    fun onInputBlurred() {
        Log.d(TAG, "输入框失去焦点，安排自动隐藏消息栏")
        scheduleAutoHide()
    }
    
    /**
     * 安排自动隐藏任务
     */
    private fun scheduleAutoHide() {
        cancelAutoHide()
        hideRunnable = Runnable {
            hideMessageBar()
        }
        handler.postDelayed(hideRunnable!!, AUTO_HIDE_DELAY)
    }
    
    /**
     * 取消自动隐藏任务
     */
    private fun cancelAutoHide() {
        hideRunnable?.let {
            handler.removeCallbacks(it)
            hideRunnable = null
        }
    }
    
    /**
     * 检查消息栏是否可见
     */
    fun isMessageBarVisible(): Boolean {
        return isVisible
    }
    
    /**
     * 强制显示消息栏（不自动隐藏）
     */
    fun forceShow() {
        showMessageBar(autoHide = false)
    }
    
    /**
     * 强制隐藏消息栏
     */
    fun forceHide() {
        cancelAutoHide()
        hideMessageBar()
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        Log.d(TAG, "清理消息栏可见性管理器")
        
        // 取消动画
        currentAnimator?.cancel()
        currentAnimator = null
        
        // 取消自动隐藏任务
        cancelAutoHide()
        
        // 移除滚动监听器
        messageRecyclerView.removeScrollListener(scrollListener)
        
        isVisible = false
        isAnimating = false
    }
}
