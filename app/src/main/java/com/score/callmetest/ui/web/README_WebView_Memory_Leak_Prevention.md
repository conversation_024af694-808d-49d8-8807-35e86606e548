# WebView 内存泄漏防护指南

## 概述

WebView 是 Android 开发中常见的内存泄漏源头。本文档介绍了在 `WebViewActivity` 中实施的内存泄漏防护措施，以及如何在其他地方复用这些最佳实践。

## 内存泄漏原因

WebView 容易造成内存泄漏的主要原因：

1. **JavaScript 接口持有 Activity 引用**：通过 `addJavascriptInterface` 添加的接口会持有 Activity 的强引用
2. **WebViewClient 持有 Context 引用**：自定义的 WebViewClient 通常持有 Activity 引用
3. **WebView 内部线程**：WebView 内部的线程可能持有 WebView 实例的引用
4. **缓存和历史记录**：WebView 的缓存、历史记录等可能持有大量对象引用
5. **JavaScript 计时器**：未清理的 JavaScript 计时器会阻止垃圾回收

## 防护措施

### 1. WebViewActivity 实现的防护措施

#### 生命周期管理
```kotlin
override fun onPause() {
    super.onPause()
    // 暂停 WebView 以节省资源
    WebViewMemoryLeakUtils.pauseWebView(webView)
}

override fun onResume() {
    super.onResume()
    // 恢复 WebView
    WebViewMemoryLeakUtils.resumeWebView(webView)
}

override fun onDestroy() {
    // 清理 CustomWebClient
    customWebClient?.cleanup()
    customWebClient = null
    
    // 使用工具类清理 WebView 资源
    WebViewMemoryLeakUtils.cleanupWebView(webView, NAME)
    
    // 清空引用
    webView = null
    
    super.onDestroy()
}
```

#### WebView 配置优化
```kotlin
// 使用工具类配置 WebView 以优化内存使用
WebViewMemoryLeakUtils.configureWebViewForMemoryOptimization(webView, enableCache = false)
```

#### JavaScript 接口线程安全
```kotlin
@JavascriptInterface
fun newTppClose() {
    // 在主线程中执行 UI 操作
    runOnUiThread {
        finish()
    }
}

@JavascriptInterface
fun openVipService() {
    // 使用 lifecycleScope 确保在 Activity 生命周期内执行
    lifecycleScope.launch {
        try {
            // 异步操作
        } catch (e: Exception) {
            Timber.e(e, "openVipService failed")
        }
    }
}
```

### 2. CustomWebClient 防护措施

#### 弱引用持有 Activity
```kotlin
class CustomWebClient(context: Activity) : WebViewClient() {
    // 使用弱引用避免内存泄漏
    private val contextRef = java.lang.ref.WeakReference(context)
    
    private fun shouldOverrideUrlLoadingInner(view: WebView?, url: String?): Boolean {
        // 获取 Activity 引用，如果为 null 说明 Activity 已被回收
        val context = contextRef.get() ?: return false
        // ... 其他逻辑
    }
    
    fun cleanup() {
        contextRef.clear()
    }
}
```

### 3. WebViewMemoryLeakUtils 工具类

提供统一的 WebView 清理方法：

```kotlin
// 清理 WebView 资源
WebViewMemoryLeakUtils.cleanupWebView(webView, "JSBridgeService")

// 配置 WebView 内存优化
WebViewMemoryLeakUtils.configureWebViewForMemoryOptimization(webView, enableCache = false)

// 暂停和恢复 WebView
WebViewMemoryLeakUtils.pauseWebView(webView)
WebViewMemoryLeakUtils.resumeWebView(webView)
```

## 最佳实践

### 1. 在 Activity/Fragment 中使用 WebView

```kotlin
class MyWebViewActivity : BaseActivity<ActivityWebViewBinding, EmptyViewModel>() {
    private var webView: WebView? = null
    private var customWebClient: CustomWebClient? = null
    
    override fun initView() {
        webView = binding.webView
        setupWebView()
    }
    
    private fun setupWebView() {
        webView?.apply {
            // 使用工具类配置
            WebViewMemoryLeakUtils.configureWebViewForMemoryOptimization(this)
            
            // 设置 WebViewClient
            customWebClient = CustomWebClient(this@MyWebViewActivity)
            webViewClient = customWebClient
            
            // 添加 JavaScript 接口
            addJavascriptInterface(this@MyWebViewActivity, "JSInterface")
        }
    }
    
    override fun onPause() {
        super.onPause()
        WebViewMemoryLeakUtils.pauseWebView(webView)
    }
    
    override fun onResume() {
        super.onResume()
        WebViewMemoryLeakUtils.resumeWebView(webView)
    }
    
    override fun onDestroy() {
        customWebClient?.cleanup()
        customWebClient = null
        
        WebViewMemoryLeakUtils.cleanupWebView(webView, "JSInterface")
        webView = null
        
        super.onDestroy()
    }
}
```

### 2. JavaScript 接口实现

```kotlin
@JavascriptInterface
fun someMethod() {
    // 确保在主线程执行 UI 操作
    runOnUiThread {
        // UI 操作
    }
}

@JavascriptInterface
fun asyncMethod() {
    // 使用 lifecycleScope 进行异步操作
    lifecycleScope.launch {
        try {
            // 异步操作
        } catch (e: Exception) {
            Timber.e(e, "Async operation failed")
        }
    }
}
```

## 测试验证

使用 `WebViewActivityTest` 验证内存泄漏防护是否有效：

```kotlin
@Test
fun testWebViewMemoryLeakPrevention() {
    val activity = activityRule.launchActivity(null)
    
    // 验证 WebView 已初始化
    assert(activity.binding.webView != null)
    
    // 模拟 Activity 销毁
    activity.finish()
    
    // 等待销毁完成
    Thread.sleep(1000)
    
    // 验证资源已清理
    Timber.d("WebView memory leak prevention test completed")
}
```

## 注意事项

1. **及时清理**：确保在 `onDestroy` 中清理所有 WebView 相关资源
2. **线程安全**：JavaScript 接口方法要注意线程安全，UI 操作必须在主线程执行
3. **生命周期感知**：使用 `lifecycleScope` 进行异步操作，避免在 Activity 销毁后继续执行
4. **弱引用**：对于需要持有 Activity 引用的类，使用弱引用避免内存泄漏
5. **工具类复用**：使用 `WebViewMemoryLeakUtils` 工具类确保清理逻辑的一致性

## 总结

通过实施上述防护措施，可以有效避免 WebView 相关的内存泄漏问题。关键是要在合适的生命周期方法中进行资源清理，使用弱引用持有 Activity 引用，以及确保异步操作的生命周期安全。
