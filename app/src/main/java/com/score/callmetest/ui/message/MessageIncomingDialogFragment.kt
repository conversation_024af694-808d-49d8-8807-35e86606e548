package com.score.callmetest.ui.message

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.DialogFragment
import com.score.callmetest.Constant
import timber.log.Timber

/**
 * 消息弹窗DialogFragment
 * 用于在无悬浮窗权限时显示消息弹窗
 * 
 * <AUTHOR>
 * @date 2025/07/30
 */
class MessageIncomingDialogFragment : DialogFragment() {
    
    companion object {
        private const val TAG = "MsgIncomingDF"
        
        /**
         * 创建新实例
         * 
         * @param avatarUrl 头像URL
         * @param nickname 昵称
         * @param content 消息内容
         * @return MessageIncomingDialogFragment实例
         */
        fun newInstance(
            avatarUrl: String?,
            nickname: String?,
            content: String?
        ): MessageIncomingDialogFragment {
            val fragment = MessageIncomingDialogFragment()
            val args = Bundle()
            args.putString(Constant.AVATAR_URL, avatarUrl)
            args.putString(Constant.NICKNAME, nickname)
            args.putString(Constant.MSG_CONTENT, content)
            fragment.arguments = args
            return fragment
        }
    }
    
    /**
     * 操作监听器接口
     */
    interface OnActionListener {
        /**
         * 消息点击事件
         */
        fun onMessageClick()

        /**
         * 上滑手势事件
         */
        fun onSwipeUp()

        /**
         * 拖拽开始事件
         */
        fun onDragStart() {}

        /**
         * 拖拽结束事件
         */
        fun onDragEnd() {}
    }
    
    private var actionListener: OnActionListener? = null
    private var popupView: MessageIncomingPopupView? = null

    /**
     * 设置操作监听器
     */
    fun setOnActionListener(listener: OnActionListener) {
        this.actionListener = listener
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        // 设置透明背景样式
        setStyle(STYLE_NO_FRAME, android.R.style.Theme_Translucent_NoTitleBar)
        Timber.tag(TAG).d("MessageIncomingDialogFragment created")
    }
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        // 设置不可取消
        isCancelable = false
        
        // 创建弹窗视图
        val view = MessageIncomingPopupView(requireContext())
        popupView = view

        // 从参数中获取数据
        val avatarUrl = arguments?.getString(Constant.AVATAR_URL)
        val nickname = arguments?.getString(Constant.NICKNAME)
        val content = arguments?.getString(Constant.MSG_CONTENT)

        // 绑定数据和事件
        view.bind(
            avatarUrl = avatarUrl,
            nicknameStr = nickname,
            contentStr = content,
            onMessageClick = {
                Timber.tag(TAG).d("Message clicked in DialogFragment")
                actionListener?.onMessageClick()
            },
            onSwipeUp = {
                Timber.tag(TAG).d("Swipe up in DialogFragment")
                actionListener?.onSwipeUp()
                dismissAllowingStateLoss()
            },
            onDragStart = {
                Timber.tag(TAG).d("Drag start in DialogFragment")
                actionListener?.onDragStart()
            },
            onDragEnd = {
                Timber.tag(TAG).d("Drag end in DialogFragment")
                actionListener?.onDragEnd()
            }
        )

        return view
    }
    
    override fun onStart() {
        super.onStart()
        
        // 设置对话框属性
        dialog?.window?.let { window ->
            // 设置全屏
            window.setLayout(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
            )
            
            // 设置透明背景
            window.setBackgroundDrawableResource(android.R.color.transparent)
            
            // 设置顶部显示
            window.setGravity(android.view.Gravity.TOP)
            
            // 清除默认的暗化背景
            window.clearFlags(android.view.WindowManager.LayoutParams.FLAG_DIM_BEHIND)
            
            // 设置不阻塞触摸事件
            window.addFlags(
                android.view.WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL or
                android.view.WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN or
                android.view.WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS
            )

            // 设置状态栏透明，不隐藏状态栏
            window.decorView.systemUiVisibility =
                View.SYSTEM_UI_FLAG_LAYOUT_STABLE or
                View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN

        }
        
        Timber.tag(TAG).d("MessageIncomingDialogFragment started")
    }
    
    override fun onDestroy() {
        super.onDestroy()
        popupView = null
        Timber.tag(TAG).d("MessageIncomingDialogFragment destroyed")
    }

    /**
     * 更新消息内容
     *
     * @param avatarUrl 头像URL
     * @param nickname 昵称
     * @param content 消息内容
     */
    fun updateContent(
        avatarUrl: String?,
        nickname: String?,
        content: String?
    ) {
        Timber.tag(TAG).d("Updating DialogFragment content: nickname=$nickname, content=$content")
        popupView?.updateContent(avatarUrl, nickname, content)
    }
}
