package com.score.callmetest.ui.main

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.viewModelScope
import com.score.callmetest.manager.AppConfigManager
import com.score.callmetest.manager.AppLifecycleManager
import com.score.callmetest.manager.GoodsManager
import com.score.callmetest.manager.UserInfoManager
import com.score.callmetest.network.GoodsInfo
import com.score.callmetest.network.LastSpecialOfferResponse
import com.score.callmetest.network.NetworkResult
import com.score.callmetest.network.RcTokenRequest
import com.score.callmetest.network.RetrofitUtils
import com.score.callmetest.network.UploadRiskInfoReqs
import com.score.callmetest.ui.base.BaseViewModel
import com.score.callmetest.util.ThreadUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.checkerframework.checker.units.qual.t
import timber.log.Timber

class MainViewModel : BaseViewModel() {

    // ========== fab1促销弹窗相关 ==========
    /**
     * 活动促销信息 LiveData
     */
    val promotionModelLiveData = MutableLiveData<LastSpecialOfferResponse?>()
    val promotionLoading = MutableLiveData<Boolean>()

    /**
     * 新人促销信息 LiveData
     */
    val presentedCoinsLiveData = MutableLiveData<GoodsInfo?>()
    val presentedCoinsLoading = MutableLiveData<Boolean>()

    fun init() {
        // 开启状态更新轮询
        UserInfoManager.startStatusUpdatePolling {
            UserInfoManager.refreshUserStatus(viewModelScope)
        }
        AppLifecycleManager.setForegroundListener(this.javaClass.simpleName){
            // 切换前台--强制刷新
            UserInfoManager.refreshUserStatus(viewModelScope,true)
        }
    }

    // ========== 原有功能 ==========
    /**
     * 融云token
     */
    fun getRongToken(
        onSuccess: (String?) -> Unit,
        onError: (Exception) -> Unit
    ) {
        viewModelScope.launch {
            try {
                val response = withContext(Dispatchers.IO) {
                    val request =
                        RcTokenRequest(AppConfigManager.getConfigValue("rc_app_key") ?: "")
                    RetrofitUtils.dataRepository.getRongCloudToken(request)
                }
                if (response is NetworkResult.Success) {
                    onSuccess(response.data)
                }
            } catch (e: Exception) {
                onError(e)
            }
        }
    }

    fun uploadRiskInfo() {
        val delay = AppConfigManager.getRiskControlConfig()?.k_interval ?: 5
        uploadRiskInfoInner(
            delay = delay,
            onError = {
                // 失败一次10s后再试一次
                uploadRiskInfoInner(10)
            }
        )
    }

    private fun uploadRiskInfoInner(
        delay: Int = 5,
        onSuccess: ((Boolean?) -> Unit)? = null,
        onError: ((Exception) -> Unit)? = null
    ) {
        val info = getRiskInfo()
        ThreadUtils.runOnIODelayed(delay * 1000L) {
            try {
                val response = withContext(Dispatchers.IO) {
                    RetrofitUtils.dataRepository.uploadRiskInfo(
                        UploadRiskInfoReqs(
                            info = info,
                        )
                    )
                }
                if (response is NetworkResult.Success) {
                    onSuccess?.invoke(response.data)
                }
            } catch (e: Exception) {
                onError?.invoke(e)
            }
        }
    }

    // ========== fab1促销弹窗相关方法 ==========

    /**
     * 获取活动促销信息（使用 GoodsManager 缓存），结果通过 promotionModelLiveData 传递给 UI 层
     * @param forceRefresh 是否强制刷新缓存（默认 false）
     */
    suspend fun getPromotionList(forceRefresh: Boolean = true) {
        promotionLoading.postValue(true)
        try {
            val goodsList = GoodsManager.getLastSpecialOfferV2(forceRefresh)
            if (goodsList.isNotEmpty()) {
                // 将 GoodsInfo 转换为 LastSpecialOfferResponse 格式
                val firstGoods = goodsList[0]
                val promotionData = LastSpecialOfferResponse(
                    code = firstGoods.code,
                    icon = firstGoods.icon,
                    type = firstGoods.type,
                    discount = firstGoods.discount,
                    originalPrice = firstGoods.originalPrice,
                    price = firstGoods.price,
                    exchangeCoin = firstGoods.exchangeCoin,
                    originalExchangeCoin = firstGoods.originalExchangeCoin,
                    originalPriceRupee = firstGoods.originalPriceRupee,
                    priceRupee = firstGoods.priceRupee,
                    localPaymentPriceRupee = firstGoods.localPaymentPriceRupee,
                    isPromotion = firstGoods.isPromotion,
                    extraCoinPercent = firstGoods.extraCoinPercent,
                    extraCoin = firstGoods.extraCoin,
                    remainMilliseconds = firstGoods.remainMilliseconds,
                    rechargeNum = firstGoods.rechargeNum,
                    capableRechargeNum = firstGoods.capableRechargeNum,
                    invitationId = firstGoods.invitationId,
                    activityPic = firstGoods.activityPic,
                    activitySmallPic = firstGoods.activitySmallPic,
                    activityName = firstGoods.activityName,
                    thirdpartyCoinPercent = firstGoods.thirdpartyCoinPercent,
                    localPayOriginalPrice = firstGoods.localPayOriginalPrice,
                    localPayPrice = firstGoods.localPayPrice
                )
                promotionModelLiveData.postValue(promotionData)
            } else {
                promotionModelLiveData.postValue(null)
            }
        } catch (e: Exception) {
            promotionModelLiveData.postValue(null)
            Timber.tag(this.javaClass.name).e(e, "getPromotionList exception")
        } finally {
            promotionLoading.postValue(false)
        }
    }

    /**
     * 获取活动促销信息（供UI层调用，自动处理协程和LiveData）
     */
    fun fetchPromotion(forceRefresh: Boolean = true) {
        promotionLoading.value = true
        viewModelScope.launch {
            try {
                getPromotionList(forceRefresh)
            } finally {
                promotionLoading.value = false
            }
        }
    }

    /**
     * 获取新人促销宝箱（使用 GoodsManager 缓存，供UI层调用，自动处理协程和LiveData）
     */
    fun fetchPresentedCoins(forceRefresh: Boolean = true) {
        presentedCoinsLoading.value = true
        viewModelScope.launch {
            try {
                val promotionGoods = GoodsManager.getPromotionGoods(forceRefresh)
                presentedCoinsLiveData.value = promotionGoods

                // 如果没有新人促销数据，自动获取活动促销数据
                if (promotionGoods == null) {
                    fetchPromotion(forceRefresh)
                }
            } catch (e: Exception) {
                presentedCoinsLiveData.value = null
                Timber.tag(this.javaClass.name).e(e, "fetchPresentedCoins exception")
                // 获取失败时也尝试获取活动促销数据
                fetchPromotion(forceRefresh)
            } finally {
                presentedCoinsLoading.value = false
            }
        }
    }

    override fun onCleared() {
        super.onCleared()
        UserInfoManager.stopStatusUpdatesPolling()
        AppLifecycleManager.setForegroundListener(this.javaClass.simpleName,null)
    }
}