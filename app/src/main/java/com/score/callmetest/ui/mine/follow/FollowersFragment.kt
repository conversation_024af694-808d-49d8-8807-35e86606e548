package com.score.callmetest.ui.mine.follow

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.score.callmetest.R
import com.score.callmetest.databinding.FragmentFollowContentBinding
import com.score.callmetest.network.FollowModel
import com.score.callmetest.ui.base.BaseFragment
import com.score.callmetest.ui.mine.follow.adapter.FollowersAdapter
import com.score.callmetest.util.ToastUtils
import timber.log.Timber


/**
 * 粉丝列表页面
 * 展示用户的粉丝列表
 */
class FollowersFragment : BaseFragment<FragmentFollowContentBinding, FollowViewModel>() {

    // recyclerview 相关
    private lateinit var mAdapter: FollowersAdapter
    private val mLayoutManager by lazy { LinearLayoutManager(context) }

    // 是否需要滚动到顶部的标志
    private var mNeedScrollToTop = false



    override fun getViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?
    ): FragmentFollowContentBinding = FragmentFollowContentBinding.inflate(inflater, container, false)

    override fun getViewModelClass(): Class<FollowViewModel> = FollowViewModel::class.java

    override fun initView() {
        super.initView()
        // 初始化空视图，避免后续 ViewStub 问题
        if (binding.emptyView.parent != null) {
            binding.emptyView.inflate()
        }
        binding.emptyView.visibility = View.GONE

        setupRecyclerView()
        setupSwipeRefresh()
    }
    
    private fun setupRecyclerView() {
        mAdapter = FollowersAdapter(
            onFollowClick = { followModel, position ->
                onFollowButtonClick(followModel, position)
            }
        )
        binding.recyclerView.apply {
            adapter = mAdapter
            layoutManager = mLayoutManager

            // 添加滚动监听，实现加载更多
            addOnScrollListener(object : RecyclerView.OnScrollListener() {
                override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                    super.onScrolled(recyclerView, dx, dy)

                    // 只有向下滚动时才检查
                    if (dy > 0) {
                        val visibleItemCount = mLayoutManager.childCount
                        val totalItemCount = mLayoutManager.itemCount
                        val firstVisibleItemPosition = mLayoutManager.findFirstVisibleItemPosition()

                        // 当滚动到倒数第3个item时开始加载更多
                        // 只有在底部状态为HIDDEN时才允许触发加载更多
                        if (!viewModel.isFollowersLoadingMore() &&
                            viewModel.hasMoreFollowersData() &&
                            viewModel.followersBottomState.value == BottomState.HIDDEN &&
                            (visibleItemCount + firstVisibleItemPosition) >= totalItemCount - 3) {
                            loadFollowersList(false)
                        }
                    }
                }
            })
        }
    }


    
    private fun setupSwipeRefresh() {
        binding.swipeRefreshLayout.apply {
            setColorSchemeResources(R.color.refresh_loading)
            // 下拉刷新
            setOnRefreshListener {
                // 显示刷新动画
                isRefreshing = true
                // 设置需要滚动到顶部的标志
                mNeedScrollToTop = true
                // 重新加载数据（刷新）
                loadFollowersList(isRefresh = true)
            }
        }
    }

    override fun initData() {
        super.initData()
        setupObservers()

        // 显示下拉刷新状态并加载数据
        binding.swipeRefreshLayout.isRefreshing = true
        mNeedScrollToTop = true
        loadFollowersList(isRefresh = true)
    }

    private fun loadFollowersList(isRefresh: Boolean = true) {
        // 调用ViewModel加载粉丝列表
        viewModel.loadFollowersList(isRefresh)
    }
    
    private fun setupObservers() {

        // 观察粉丝列表数据变化
        viewModel.followersList.observe(viewLifecycleOwner) { followersList ->
            // 显示/隐藏空视图
            emptyView(followersList.isNullOrEmpty())

            // 提交新列表前保存当前滚动位置
            val firstVisiblePosition = mLayoutManager.findFirstVisibleItemPosition()

            // 更新适配器数据
            mAdapter.setData(followersList ?: emptyList())

            // 数据加载完成后，停止刷新动画
            binding.swipeRefreshLayout.isRefreshing = false

            // 只有在需要滚动到顶部时才滚动（下拉刷新时）
            if (mNeedScrollToTop) {
                binding.recyclerView.scrollToPosition(0)
                mNeedScrollToTop = false
            } else if (firstVisiblePosition == 0) {
                // 如果原本就在顶部，确保仍然在顶部
                binding.recyclerView.scrollToPosition(0)
            }

        }
        
        // 观察加载状态 - 只有在刷新时才显示顶部加载动画
        viewModel.isLoading.observe(viewLifecycleOwner) { isLoading ->
            // 只有在不是加载更多的情况下才显示顶部加载动画
            if (!viewModel.isFollowersLoadingMore()) {
                binding.swipeRefreshLayout.isRefreshing = isLoading
                if (isLoading) {
                    mNeedScrollToTop = true
                }
            }
        }
        
        // 观察错误信息
        viewModel.errorMessage.observe(viewLifecycleOwner) { errorMessage ->
            if (!errorMessage.isNullOrEmpty()) {
                Timber.tag("follow").e("Error loading followers: $errorMessage")
                // 错误发生时，停止刷新动画
                binding.swipeRefreshLayout.isRefreshing = false
            }
            emptyView(viewModel.followersList.value.isNullOrEmpty())
        }

        // 观察底部状态变化
        viewModel.followersBottomState.observe(viewLifecycleOwner) { bottomState ->
            mAdapter.setBottomState(bottomState)
        }

        // 观察Toast消息
        viewModel.toastMessage.observe(viewLifecycleOwner) { resourceId ->
            resourceId?.let {
                val message = getString(it)
                ToastUtils.showShortToast(message)
            }
        }
    }

    /**
     * 显示/隐藏EmptyView
     */
    private fun emptyView(isEmpty: Boolean) {
        if (isEmpty) {
            // 显示空页面
            binding.emptyView.visibility = View.VISIBLE
            binding.recyclerView.visibility = View.GONE
        } else {
            // 显示列表，隐藏空页面
            binding.emptyView.visibility = View.GONE
            binding.recyclerView.visibility = View.VISIBLE
        }
    }

    /**
     * 处理关注/取关按钮点击
     * 只显示即时反馈，状态管理已在adapter回调中处理
     */
    private fun onFollowButtonClick(followModel: FollowModel, position: Int) {
        val userId = followModel.userId
        val currentState = followModel.isFollows ?: false
        val newState = !currentState

        // 显示即时反馈
        val message = if (newState) getString(R.string.follow_added_to_list) else getString(R.string.follow_removed_from_list)
        ToastUtils.showShortToast(message)

        // 立即发起网络请求
        if (newState) {
            viewModel.followUser(userId) { success ->
                if (!success) {
                    // 请求失败，恢复UI状态
                    requireActivity().runOnUiThread {
                        mAdapter.updateItemFollowState(position, currentState)
                        ToastUtils.showShortToast(getString(R.string.follow_failed))
                    }
                }
            }
        } else {
            viewModel.unfollowUser(userId) { success ->
                if (!success) {
                    // 请求失败，恢复UI状态
                    requireActivity().runOnUiThread {
                        mAdapter.updateItemFollowState(position, currentState)
                        ToastUtils.showShortToast(getString(R.string.unfollow_failed))
                    }
                }
            }
        }
    }
    
} 