package com.score.callmetest.ui.message

import android.Manifest
import android.content.Intent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.core.graphics.toColorInt
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.score.callmetest.CallStatus
import com.score.callmetest.CallmeApplication
import com.score.callmetest.Constant
import com.score.callmetest.R
import com.score.callmetest.databinding.FragmentCallHistoryBinding
import com.score.callmetest.entity.CallHistoryEntity
import com.score.callmetest.entity.CustomEvents
import com.score.callmetest.entity.RechargeSource
import com.score.callmetest.manager.AppPermissionManager
import com.score.callmetest.manager.SocketManager
import com.score.callmetest.manager.StrategyManager
import com.score.callmetest.manager.UserInfoManager
import com.score.callmetest.ui.base.BaseFragment
import com.score.callmetest.ui.broadcaster.BroadcasterDetailActivity
import com.score.callmetest.ui.chat.ChatActivity
import com.score.callmetest.ui.message.adapter.CallHistoryAdapter
import com.score.callmetest.ui.videocall.VideoCallActivity
import com.score.callmetest.ui.widget.InsufficientBalanceDialog
import com.score.callmetest.util.ToastUtils
import timber.log.Timber

/**
 * Call模块--通话记录列表
 * 展示通话记录列表
 */
class CallHistoryFragment : BaseFragment<FragmentCallHistoryBinding, CallHistoryViewModel>(),IMessageStatus {

    // recyclerview 相关
    private lateinit var mAdapter: CallHistoryAdapter
    private val mLayoutManager by lazy { LinearLayoutManager(context) }
    
    // 是否需要滚动到顶部的标志
    private var mNeedScrollToTop = false

    override fun getViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?
    ): FragmentCallHistoryBinding = FragmentCallHistoryBinding.inflate(inflater, container, false)

    override fun getViewModelClass(): Class<CallHistoryViewModel> = CallHistoryViewModel::class.java

    override fun initView() {
        super.initView()
        setupRecyclerView()
        setupSwipeRefresh()
    }

    private fun setupRecyclerView() {
        mAdapter = CallHistoryAdapter()
        binding.recyclerView.apply {
            adapter = mAdapter
            layoutManager = mLayoutManager
        }

    }

    private fun setupSwipeRefresh() {
        binding.swipeRefreshLayout.apply {
            setColorSchemeResources(R.color.refresh_loading)
            // 下拉刷新
            setOnRefreshListener {
                // 显示刷新动画
                isRefreshing = true
                // 设置需要滚动到顶部的标志
                mNeedScrollToTop = true
                // 重新加载数据
                viewModel.loadCallHistoryList()
            }
        }
    }

    override fun initData() {
        super.initData()
        setupObservers()
        setEmptyView()
    }

    private fun setEmptyView() {
        // 确保ViewStub已经被inflate
        if (binding.emptyView.parent != null) { // 检查是否还未inflate
            binding.emptyView.inflate()
        }
        // 现在可以安全地访问inflated的视图
        val emptyLayout = binding.root.findViewById<View>(R.id.layout_empty_rv_parent)
        val hintTv = emptyLayout.findViewById<TextView>(R.id.layout_empty_rv_bg_hint_tv)
        hintTv.text = getString(R.string.null_call_history)
    }
    private fun setupObservers() {

        // 观察数据变化
        viewModel.callHistoryList.observe(viewLifecycleOwner) { callHistoryList ->

            // empty-view
            if(callHistoryList.isNullOrEmpty()){
                emptyView(true)
                binding.swipeRefreshLayout.isRefreshing = false
                return@observe
            }

            emptyView(false)
            // 提交新列表前保存当前滚动位置
            val firstVisiblePosition = mLayoutManager.findFirstVisibleItemPosition()

            // 简化逻辑：有数据就添加底部项
            val listWithBottom = if (callHistoryList.isNotEmpty()) {
                callHistoryList + CallHistoryEntity.provideBottomView()
            } else {
                callHistoryList
            }

            mAdapter.submitList(listWithBottom) {
                // 数据加载完成后，停止刷新动画
                binding.swipeRefreshLayout.isRefreshing = false

                // 只有在需要滚动到顶部时才滚动（下拉刷新时）
                if (mNeedScrollToTop) {
                    binding.recyclerView.scrollToPosition(0)
                    mNeedScrollToTop = false
                } else if (firstVisiblePosition == 0) {
                    // 如果原本就在顶部，确保仍然在顶部
                    binding.recyclerView.scrollToPosition(0)
                }
            }
        }
        
        // 观察加载状态
        viewModel.isLoading.observe(viewLifecycleOwner) { isLoading ->
            binding.swipeRefreshLayout.isRefreshing = isLoading
            if (isLoading) {
                mNeedScrollToTop = true
            }
        }
        
        // 观察错误信息
        viewModel.errorMessage.observe(viewLifecycleOwner) { errorMessage ->
            if (!errorMessage.isNullOrEmpty()) {
                Timber.tag("CallHistoryFragment").e("Error loading call history: $errorMessage")
                // 错误发生时，停止刷新动画
                binding.swipeRefreshLayout.isRefreshing = false
            }
            emptyView(viewModel.callHistoryList.value.isNullOrEmpty())
        }

        // 观察跳转到聊天页面的用户信息
        viewModel.mGoChatUser.observe(viewLifecycleOwner) { user ->
            ChatActivity.start(context, user)
        }
    }

    /**
     * 显示/隐藏EmptyView
     */
    private fun emptyView(isEmpty: Boolean) {
        if (isEmpty) {
            binding.emptyView.visibility = View.VISIBLE
            binding.recyclerView.visibility = View.GONE
        } else {
            binding.emptyView.visibility = View.GONE
            binding.recyclerView.visibility = View.VISIBLE
        }
    }
    override fun initListener() {
        super.initListener()
        binding.recyclerView.apply {
            // 设置滚动监听器
            addOnScrollListener(object : RecyclerView.OnScrollListener() {
                override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                    super.onScrolled(recyclerView, dx, dy)

                    // 只有在顶部时才能下拉刷新
                    val firstVisibleItemPosition = mLayoutManager.findFirstCompletelyVisibleItemPosition()
                    binding.swipeRefreshLayout.isEnabled = firstVisibleItemPosition <= 0
                }
            })
        }
        setupCallItemListener()
    }
    private fun setupCallItemListener() {
        // 设置通话记录项点击监听器
        mAdapter.setOnCallItemClickListener(object : CallHistoryAdapter.OnCallItemClickListener {
            override fun onItemAvatarClick(position: Int, callHistory: CallHistoryEntity) {
                // 头像点击
                handleItemAvatarClick(callHistory)
            }

            override fun onItemClick(position: Int, callHistory: CallHistoryEntity) {
                // 处理通话记录项点击事件，例如跳转到用户详情页
                handleItemClick(callHistory)
            }



            override fun onVideoCallClick(position: Int, callHistory: CallHistoryEntity) {
                // 视频通话点击
                handleVideoCallClick(callHistory)
            }
        })
    }
    // 通话记录项点击事件处理
    private fun handleItemClick(user: CallHistoryEntity) {

        // 跳转到主播资料详情页面
        val intent = Intent(activity, BroadcasterDetailActivity::class.java)
        intent.putExtra(Constant.BROADCASTER_MODEL,user.toBroadcasterModel())
        startActivity(intent)
    }
    // 通话记录项头像点击事件处理
    private fun handleItemAvatarClick(user: CallHistoryEntity) {

        // 跳转到主播资料详情页面
        val intent = Intent(activity, BroadcasterDetailActivity::class.java)
        intent.putExtra(Constant.BROADCASTER_MODEL,user.toBroadcasterModel())
        startActivity(intent)
    }
    // 处理视频通话点击事件
    private fun handleVideoCallClick(callHistory: CallHistoryEntity) {

        // 获取当前缓存的状态来决定执行什么操作
        val currentStatus = UserInfoManager.getCachedStatus(callHistory.userId) ?: CallStatus.OFFLINE

        if (currentStatus == CallStatus.ONLINE) {
            // 在线状态：发起视频通话
            startVideoCall(callHistory)
        } else {
            // 其他状态：跳转到聊天
            startChat(callHistory)
        }

    }

    private fun startChat(callHistory: CallHistoryEntity) {
        viewModel.gotoChat( callHistory.userId)
    }

    private fun startVideoCall(callHistory: CallHistoryEntity) {
        // 首先检查unitPrice是否有效
        val unitPrice = callHistory.unitPrice
        if (unitPrice < 0) {
            return
        }

        if (!StrategyManager.isReviewPkg()) {
            // 判断金币是否足够
            val availableCoins = UserInfoManager.myUserInfo?.availableCoins ?: 0
            if (availableCoins < unitPrice) {

                // 弹出金币充值弹窗（新版：自动拉取商品数据,显示主播每分钟收费）
                val dialog = InsufficientBalanceDialog.newInstance(unitPrice, RechargeSource.CALLS_RECORD.value())
                dialog.show(parentFragmentManager, "insufficient_balance")

                return
            }
        }

        if (!SocketManager.instance.isConnected()) {
            ToastUtils.showToast(requireContext().getString(R.string.long_connection_network_offline))
            return
        }

        // 获取在线状态
        UserInfoManager.loadOnlineStatus(
            lifecycleScope, callHistory.userId
        ) { status, error ->
            activity?.runOnUiThread {
                if (error == null && status != null) {
                    if (CallStatus.ONLINE != status && CallStatus.AVAILABLE != status) {
                        // 金币充足但主播状态不可用时显示toast
                        val statusText = CallStatus.getDisplayText(status)
                        val message = getString(R.string.user_status_not_available, statusText)
                        ToastUtils.showToast(message)
                        return@runOnUiThread
                    }

                    // 在线状态检查通过后，再请求权限
                    AppPermissionManager.checkAndRequestCameraMicrophonePermission(
                        requireActivity() as AppCompatActivity,
                        onGranted = {
                            VideoCallActivity.startOutgoing(
                                context = requireActivity(),
                                userId = callHistory.userId,
                                avatarUrl = callHistory.avatar,
                                nickname = callHistory.userName,
                                age = "", // 通话记录中没有年龄信息
                                country = "", // 通话记录中没有国家信息
                                unitPrice = callHistory.toBroadcasterModel().callCoins.toString()
                            )
                        },
                        onDenied = { deniedPermissions, permanentlyDeniedPermissions ->
                        }
                    )
                } else {
                    ToastUtils.showToast(requireContext().getString(R.string.failed_to_get_user_status))
                }
            }
        }
    }



    // <editor-fold desc="IMessageStatus">
    override fun provideUserIds(): List<String> {
        val userIds: List<String> = viewModel.callHistoryList.value?.mapNotNull {
            if(it is CallHistoryEntity && !it.isBottomView){
                return@mapNotNull it.userId
            }else return@mapNotNull null
        } ?: emptyList()
        return userIds
    }

    override fun notifyStatusChanged(statusMap: Map<String, String>) {
        // 更新ViewModel中的数据
        viewModel.updateStatus(statusMap)

    }

    
    override fun onResume() {
        mAdapter?.notifyDataSetChanged()
        super.onResume()
    }
    
    override fun onDestroyView() {
        // 清除滚动监听器
        binding.recyclerView.clearOnScrollListeners()
        // 释放适配器资源，避免内存泄漏
        mAdapter.release()
        super.onDestroyView()
    }
}
