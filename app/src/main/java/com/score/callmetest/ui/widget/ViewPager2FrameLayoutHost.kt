package com.score.callmetest.ui.widget

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.viewpager2.widget.ViewPager2

/**
 * 解决ViewPager2嵌套ViewPager2的滑动冲突
 */
class ViewPager2FrameLayoutHost(
    context: Context,
    attrs: AttributeSet?,
    defStyleAttr: Int,
    defStyleRes: Int
) : FrameLayout(context, attrs, defStyleAttr, defStyleRes) {
    constructor(context: Context) : this(context, null)

    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, 0)

    constructor(
        context: Context,
        attrs: AttributeSet?,
        defStyleAttr: Int
    ) : this(context, attrs, defStyleAttr, 0)

    override fun onInterceptTouchEvent(ev: MotionEvent): Boolean {
        handleInterceptTouchEvent(ev)
        return super.onInterceptTouchEvent(ev)
    }

    private fun handleInterceptTouchEvent(e: MotionEvent) {
        when (e.getActionMasked()) {
            MotionEvent.ACTION_DOWN -> {
                val viewPager2: ViewGroup? = findViewPager2()
                if (viewPager2 != null) {
//                    只在Down中请求就可以
                    viewPager2.requestDisallowInterceptTouchEvent(true)
                }
            }

            MotionEvent.ACTION_MOVE -> {}
        }
    }

    private fun findViewPager2(): ViewPager2? {
        val count = getChildCount()
        for (i in 0..<count) {
            val child = getChildAt(i)
            if (child is ViewPager2) {
                return child
            }
        }
        return null
    }
}