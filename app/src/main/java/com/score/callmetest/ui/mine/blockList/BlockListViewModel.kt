package com.score.callmetest.ui.mine.blockList

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.score.callmetest.CallmeApplication
import com.score.callmetest.R
import com.score.callmetest.network.BlockListItem
import com.score.callmetest.network.NetworkResult
import com.score.callmetest.network.RetrofitUtils
import com.score.callmetest.ui.mine.follow.BottomState
import com.score.callmetest.util.logAsTag
import kotlinx.coroutines.launch
import timber.log.Timber

class BlockListViewModel : ViewModel() {
    private val _blockList = MutableLiveData<List<BlockListItem>>()
    val blockList: LiveData<List<BlockListItem>> = _blockList

    private val _error = MutableLiveData<String>()
    val error: LiveData<String> = _error

    // 加载状态
    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading

    // 分页状态管理
    private var currentPage = 1
    private var isLoadingMore = false
    private var hasMoreData = true
    private val pageSize = 15

    // 当前列表数据
    private val currentBlockList = mutableListOf<BlockListItem>()

    // 底部状态
    private val _bottomState = MutableLiveData<BottomState>()
    val bottomState: LiveData<BottomState> = _bottomState

    // Toast消息
    private val _toastMessage = MutableLiveData<Int>()
    val toastMessage: LiveData<Int> = _toastMessage

    /**
     * 获取屏蔽列表
     * @param isRefresh 是否是刷新操作，true表示刷新，false表示加载更多
     */
    fun fetchBlockList(isRefresh: Boolean = true) {
        if (isRefresh) {
            // 刷新操作，重置分页状态和底部状态
            currentPage = 1
            hasMoreData = true
            currentBlockList.clear()
            _bottomState.postValue(BottomState.HIDDEN)
        } else {
            // 加载更多操作，检查是否正在加载或没有更多数据
            if (isLoadingMore || !hasMoreData) {
                return
            }
            currentPage++
            // 开始加载更多时显示加载动画
            _bottomState.postValue(BottomState.LOADING)
        }

        isLoadingMore = true
        _isLoading.postValue(true)
        viewModelScope.launch {
            try {
                val request = com.score.callmetest.network.UserBlockListRequest(limit = pageSize, page = currentPage)
                val response = RetrofitUtils.dataRepository.getBlockList(request)
                if(response is NetworkResult.Success){
                    val list = response.data ?: emptyList()

                    if (isRefresh) {
                        currentBlockList.clear()
                    }
                    currentBlockList.addAll(list)

                    // 判断是否还有更多数据
                    hasMoreData = list.size >= pageSize

                    _blockList.postValue(currentBlockList.toList())
                    Timber.d("fetchBlockList: ${_blockList.value.toString()}")

                    // 根据数据情况设置底部状态
                    updateBottomState(isRefresh, list.size)
                }else {
                    if (isRefresh) {
                        currentBlockList.clear()
                        _blockList.postValue(emptyList())
                        _bottomState.postValue(BottomState.HIDDEN)
                    } else {
                        // 加载更多失败时，显示Toast并设置为完成状态
                        _toastMessage.postValue(R.string.there_is_no_more_data)
                        _bottomState.postValue(BottomState.FINISHED)
                    }
                    _error.postValue(CallmeApplication.context.getString(R.string.fetch_block_list_failed))
                    Timber.d(" fetchBlockList: fail")
                }
            } catch (e: Exception) {
                if (!isRefresh) {
                    // 加载更多失败时，回退页码
                    currentPage--
                    // 显示Toast并设置为完成状态
                    _toastMessage.postValue(R.string.there_is_no_more_data)
                    _bottomState.postValue(BottomState.FINISHED)
                } else {
                    _bottomState.postValue(BottomState.HIDDEN)
                }
                e.toString().logAsTag(this.javaClass.name + " fetchBlockList: exception")
                _error.postValue(e.message ?: CallmeApplication.context.getString(R.string.network_error))
            } finally {
                isLoadingMore = false
                _isLoading.postValue(false)
            }
        }
    }

    /**
     * 更新底部状态
     */
    private fun updateBottomState(isRefresh: Boolean, dataSize: Int) {
        if (isRefresh) {
            // 第一次请求
            when {
                currentBlockList.isEmpty() -> {
                    // 数据为空，隐藏底部
                    _bottomState.postValue(BottomState.HIDDEN)
                }
                dataSize < pageSize -> {
                    // 数据少于15条，显示静态底部
                    _bottomState.postValue(BottomState.FINISHED)
                }
                dataSize == pageSize -> {
                    // 数据等于15条，隐藏底部等待用户滚动
                    _bottomState.postValue(BottomState.HIDDEN)
                }
            }
        } else {
            // 后续请求
            when {
                dataSize == 0 -> {
                    // 没有更多数据，显示Toast和静态底部
                    _toastMessage.postValue(R.string.there_is_no_more_data)
                    _bottomState.postValue(BottomState.FINISHED)
                }
                dataSize < pageSize -> {
                    // 数据少于15条，显示静态底部
                    _bottomState.postValue(BottomState.FINISHED)
                }
                dataSize == pageSize -> {
                    // 数据等于15条，隐藏底部继续等待
                    _bottomState.postValue(BottomState.HIDDEN)
                }
            }
        }
    }

    // 取消拉黑
    fun unblock(userId: String, callback: (Boolean) -> Unit) {
        viewModelScope.launch {
            try {
                val resp = kotlinx.coroutines.withContext(kotlinx.coroutines.Dispatchers.IO) {
                    RetrofitUtils.dataRepository.removeBlock(
                        com.score.callmetest.network.RemoveBlockRequest(blockUserId = userId)
                    )
                }
                if (resp is NetworkResult.Success) {
                    // userInfo?.isBlock = false // 这里没有userInfo对象，视具体业务可补充
                    callback(true)
                } else {
                    callback(false)
                }
            } catch (e: Exception) {
                callback(false)
            }
        }
    }

    /**
     * 检查是否还有更多数据
     */
    fun hasMoreData(): Boolean = hasMoreData

    /**
     * 检查是否正在加载更多
     */
    fun isLoadingMore(): Boolean = isLoadingMore
}