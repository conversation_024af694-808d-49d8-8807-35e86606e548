package com.score.callmetest.ui.broadcaster

import android.Manifest
import android.annotation.SuppressLint
import android.app.AlertDialog
import android.content.Intent
import android.graphics.Color
import android.graphics.Paint
import android.graphics.Typeface
import android.graphics.drawable.GradientDrawable
import android.net.Uri
import android.provider.Settings
import android.text.style.ImageSpan
import android.transition.TransitionManager
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.activity.OnBackPressedCallback
import androidx.core.content.ContextCompat
import androidx.core.content.IntentCompat
import androidx.core.graphics.toColorInt
import androidx.core.view.doOnPreDraw
import androidx.core.view.marginBottom
import androidx.lifecycle.lifecycleScope
import com.google.android.flexbox.FlexboxLayout
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.score.callmetest.CallStatus
import com.score.callmetest.CallmeApplication
import com.score.callmetest.Constant
import com.score.callmetest.R
import com.score.callmetest.databinding.ActivityBroadcasterDetailBinding
import com.score.callmetest.entity.CustomEvents
import com.score.callmetest.entity.RechargeSource
import com.score.callmetest.manager.AppPermissionManager
import com.score.callmetest.manager.GlobalManager
import com.score.callmetest.manager.SocketManager
import com.score.callmetest.manager.StrategyManager
import com.score.callmetest.manager.TranslateManager
import com.score.callmetest.manager.UserInfoManager
import com.score.callmetest.network.BroadcasterExtraInfo
import com.score.callmetest.network.BroadcasterModel
import com.score.callmetest.network.GetGiftCountItem
import com.score.callmetest.network.UserInfo
import com.score.callmetest.network.toBroadcasterModel
import com.score.callmetest.ui.base.BaseActivity
import com.score.callmetest.ui.chat.ChatActivity
import com.score.callmetest.ui.main.BroadcasterGiftListActivity
import com.score.callmetest.ui.videocall.VideoCallActivity
import com.score.callmetest.ui.widget.CommonActionBottomSheet
import com.score.callmetest.ui.widget.FollowEvent
import com.score.callmetest.ui.widget.Helper.PagerHelper
import com.score.callmetest.ui.widget.InsufficientBalanceDialog
import com.score.callmetest.util.CustomUtils
import com.score.callmetest.util.DeviceUtils
import com.score.callmetest.util.DisplayUtils
import com.score.callmetest.util.DrawableUtils
import com.score.callmetest.util.EventBus
import com.score.callmetest.util.GlideUtils
import com.score.callmetest.util.LanguageUtils
import com.score.callmetest.util.SharePreferenceUtil
import com.score.callmetest.util.StatusBarUtils
import com.score.callmetest.util.ThreadUtils
import com.score.callmetest.util.ToastUtils
import com.score.callmetest.util.click
import com.score.callmetest.util.logAsTag
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import timber.log.Timber


@SuppressLint("SetTextI18n")
class BroadcasterDetailActivity :
    BaseActivity<ActivityBroadcasterDetailBinding, BroadcasterDetailViewModel>() {
    var broadcaster: BroadcasterModel? = null
    private var emptyViewInflated: View? = null
    private var isTranslationShown = false
    private var translatedText: String? = null
    private var introOriginal: String? = null

    // 等启动换场动画做完
    private var canShowPhotoPager = false

    override fun getViewBinding(): ActivityBroadcasterDetailBinding {
        return ActivityBroadcasterDetailBinding.inflate(layoutInflater)
    }

    override fun getViewModelClass() = BroadcasterDetailViewModel::class.java

    override fun initView() {
        GlobalManager.addViewStatusBarTopMargin(this, binding.topLayout)

        broadcaster = IntentCompat.getParcelableExtra(intent,Constant.BROADCASTER_MODEL, BroadcasterModel::class.java)
        if (broadcaster == null) {
            finish()
            return
        }

        if (StrategyManager.isReviewPkg()) {
            binding.tagsLayout.visibility = View.GONE
            binding.giftTitle.visibility = View.GONE
            binding.giftListLayout.visibility = View.GONE
        }

        initBasicUI()

        if (Constant.USE_TRANSITION) {
            // Initial state for animation: fully transparent and shifted down
            binding.mainContentLayout.translationY =
                DisplayUtils.dp2px(400f).toFloat() // Shifted down by 50dp
            // Animate to fully opaque and original position
            binding.mainContentLayout.animate()
                .translationY(0f)
                .setDuration(600) // Duration of 500ms
                .start()
        }

        binding.mainContentLayout.doOnPreDraw {
            initMainContentLayout()
        }

        // 关注按钮初始状态
        binding.followLayout.setIsFriend(broadcaster?.isFriend ?: false)
    }

    private fun initBasicUI() {
        binding.translate.paint.flags = binding.translate.paint.flags or Paint.UNDERLINE_TEXT_FLAG
        // binding.tagIcon.background = DrawableUtils.createRoundRectDashedDrawable(0, 0f, Color.BLACK, 1, 1f, 3f)
        GlobalManager.setViewRoundBackground(binding.photoCount, "#33000000".toColorInt())
        GlobalManager.setViewRoundBackground(binding.statusLayout, "#33000000".toColorInt())

        GlobalManager.setViewRoundBackground(
            binding.ageLayout,
            ContextCompat.getColor(this@BroadcasterDetailActivity, R.color.age_bg)
        )

        GlobalManager.setViewRoundBackground(
            binding.country,
            ContextCompat.getColor(this@BroadcasterDetailActivity, R.color.country_bg)
        )
        GlobalManager.setViewRoundBackground(
            binding.lineLanguage,
            ContextCompat.getColor(this@BroadcasterDetailActivity, R.color.language_bg)
        )
        binding.videoCallLayout.elevation = DisplayUtils.dp2px(3f).toFloat()
        binding.btnChat.elevation = DisplayUtils.dp2px(5f).toFloat()

        binding.bottomShadow.background = DrawableUtils.createGradientDrawable(
            colors = intArrayOf("#00FFFFFF".toColorInt(), "#FFFFFFFF".toColorInt()),
            orientation = GradientDrawable.Orientation.TOP_BOTTOM,
            radius = DisplayUtils.dp2px(0f).toFloat(),
        )
    }

    private fun initMainContentLayout() {
        binding.bottomShadow.background = DrawableUtils.createGradientDrawable(
            colors = intArrayOf("#00FFFFFF".toColorInt(), "#FFFFFF".toColorInt()),
            orientation = GradientDrawable.Orientation.TOP_BOTTOM,
        )

        val statusHeight = StatusBarUtils.getStatusBarHeight(this)
        val marginTopDistanceWhenExpand = statusHeight + binding.topLayout.height

        // 设置主内容区域背景
        val r = DisplayUtils.dp2px(18f).toFloat()
        val radii = floatArrayOf(r, r, r, r, 0f, 0f, 0f, 0f)
        binding.mainContentLayout.background =
            DrawableUtils.createRoundRectDrawable(Color.WHITE, radii)
        val screenHeight = binding.rootView.height
        // 限制BottomSheet最大高度为屏幕高度-100dp
        val maxExpandedHeight = screenHeight - marginTopDistanceWhenExpand

        binding.mainContentLayout.post {
            val params = binding.mainContentLayout.layoutParams
            params.height = maxExpandedHeight
            binding.mainContentLayout.layoutParams = params

            // 设置BottomSheetBehavior初始高度
            val avatarHeight = screenHeight * 3 / 5
            binding.avatarLayout.layoutParams.height = avatarHeight
            binding.avatarLayout.requestLayout()

            val behavior = BottomSheetBehavior.from(binding.mainContentLayout)
            // 收起时的区域高度
            behavior.peekHeight =
                screenHeight - avatarHeight + binding.statusLayout.marginBottom - DisplayUtils.dp2px(
                    12f
                )
            behavior.state = BottomSheetBehavior.STATE_COLLAPSED
            // 限制滑动到顶部100dp
            behavior.addBottomSheetCallback(object : BottomSheetBehavior.BottomSheetCallback() {
                override fun onStateChanged(bottomSheet: View, newState: Int) {
                    // 防止完全展开
                    if (newState == BottomSheetBehavior.STATE_EXPANDED) {
                        val location = IntArray(2)
                        bottomSheet.getLocationOnScreen(location)
                        val top = location[1]
                        val minTop = marginTopDistanceWhenExpand
                        if (top < minTop) {
                            bottomSheet.y = minTop.toFloat()
                        }
                    }
                }

                override fun onSlide(bottomSheet: View, slideOffset: Float) {
                    val location = IntArray(2)
                    bottomSheet.getLocationOnScreen(location)
                    val top = location[1]
                    val minTop = marginTopDistanceWhenExpand
                    if (top < minTop) {
                        bottomSheet.y = minTop.toFloat()
                    }
                }
            })
        }
    }

    override fun initData() {
        if (StrategyManager.isReviewPkg()) {
            binding.giftArrow.visibility = View.GONE
//            binding.statusLayout.visibility = View.GONE
        }
        binding.nickname.text = broadcaster?.nickname
        binding.age.text = broadcaster?.age?.toString() ?: ""
        binding.country.text = broadcaster?.country ?: ""
        updateStatus(broadcaster?.status.toString())

        // 展示默认图
        broadcaster?.userId?.let { userid ->
            UserInfoManager.getCachedDrawable(userid)?.let {
                binding.defaultAvatar.setImageDrawable(it)
            } ?: run {
                GlideUtils.load(
                    binding.defaultAvatar,
                    broadcaster?.avatar,
                    placeholder = R.drawable.placeholder
                )
            }
        } ?: run {
            GlideUtils.load(
                binding.defaultAvatar,
                broadcaster?.avatar,
                placeholder = R.drawable.placeholder
            )
        }

        if (Constant.USE_TRANSITION) {
            // 500ms之后展示viewpager，
            ThreadUtils.runOnMainDelayed(500L) {
                canShowPhotoPager = true
                if (viewModel.userInfo != null) {
                    updatePhotoPager(viewModel.userInfo!!)
                }
            }
        } else {
            canShowPhotoPager = true
        }

        // 回调方式加载所有数据
        viewModel.loadAll(
            userId = broadcaster!!.userId,
            onOnlineStatus = { status, error ->
                lifecycleScope.launch(Dispatchers.Main) {
                    if (status != null) {
                        status.toString().logAsTag(this.javaClass.name + "status: ")
                        updateStatus(status)
                    }
                }
            },
            onUserInfo = { userInfo, error ->
                lifecycleScope.launch(Dispatchers.Main) {
                    if (userInfo != null) {
                        updateBaseInfo(userInfo)
                        binding.followLayout.setIsFriend(userInfo.isFriend)
                        userInfo.toString().logAsTag(this.javaClass.name + "userInfo: ")
                    }
                }
            },
            onExtraInfo = { extraInfo, error ->
                lifecycleScope.launch(Dispatchers.Main) {
                    if (extraInfo != null) {
                        updateExtraLabels(extraInfo)
                        extraInfo.toString().logAsTag(this.javaClass.name + "extraInfo: ")
                    }
                }
            },
            onGiftCount = { giftInfo, error ->
                lifecycleScope.launch(Dispatchers.Main) {
                    if (giftInfo != null) {
                        updateExtraGift(giftInfo.normalGiftNum)
                        giftInfo.toString().logAsTag(this.javaClass.name + "giftInfo: ")
                    }
                }
            })
    }

    override fun initListener() {
        // 审核模式
        if (StrategyManager.isReviewPkg()) {
            EventBus.observe(this, CustomEvents.ReviewCallEvent::class.java) {
                updateStatus(CallStatus.IN_CALL)
            }
        }
        // 拦截系统返回（含手势返回与预测性返回）
        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                // 消费返回事件，不执行退出
                finish()
            }
        })

        EventBus.observe(this, FollowEvent::class.java) { event ->
            binding.followLayout.setIsFriend(event.isFriend)
        }

        // 监听状态变化
        EventBus.observe(
            scope = lifecycleScope,
            eventType = CustomEvents.NewStatusEvent::class.java
        ) { event ->
            event.statusMap[broadcaster?.userId]?.let { status ->
                updateStatus(status)
            }
        }

        binding.back.click {
            finish()
        }
        binding.more.click {
            showMoreOptionsDialog()
        }
        binding.btnChat.click {
            // 私聊
            viewModel.userInfo?.let { userInfo ->
                ChatActivity.start(this, userInfo)
            }
        }
        binding.followLayout.click {
            // 根据当前关注状态决定执行关注还是取消关注
            if (broadcaster?.isFriend == true) {
                unfollowUser()
            } else {
                followUser()
            }
        }
        if (!StrategyManager.isReviewPkg()) {
            binding.giftListLayout.click {
                viewModel.giftCount?.let { giftResponse ->
                    val intent = Intent(this, BroadcasterGiftListActivity::class.java)
                    intent.putExtra("gift_response", giftResponse)
                    startActivity(intent)
                }
                // fixme: Test
//            GiftDialogFragment().show(supportFragmentManager, "gift_dialog")
            }
            binding.giftTitle.click {
                viewModel.giftCount?.let { giftResponse ->
                    val intent = Intent(this, BroadcasterGiftListActivity::class.java)
                    intent.putExtra("gift_response", giftResponse)
                    startActivity(intent)
                }
            }
        }

        binding.mainContentLayout.click {
            // 拦截点击事件
        }
        // 翻译按钮点击展开/收起
        binding.translateLayout.click {
            if (isTranslationShown) {
                binding.introTranslated.visibility = View.GONE
                isTranslationShown = false
            } else if (!translatedText.isNullOrBlank()) {
                binding.introTranslated.text = translatedText
                binding.introTranslated.visibility = View.VISIBLE
                isTranslationShown = true
            } else if (!introOriginal.isNullOrBlank()) {
                showTranslateLoading()
                // 手动触发翻译
                LanguageUtils.getAppLanguage(context = this, scope = lifecycleScope) { userLang ->
                    TranslateManager.translate(
                        lifecycleScope, introOriginal!!, userLang,
                        callback = translateCallback
                    )
                }
            }
        }
        // 点击翻译内容收起
        binding.introTranslated.click {
            binding.introTranslated.visibility = View.GONE
            isTranslationShown = false
        }
    }

    fun showTranslateLoading() {
        binding.translateIcon.visibility = View.GONE
        binding.translateSvga.visibility = View.VISIBLE
        CustomUtils.playSvga(binding.translateSvga, "translate_loading.svga")
    }

    fun hideTranslateLading() {
        binding.translateIcon.visibility = View.VISIBLE
        binding.translateSvga.visibility = View.GONE
        binding.translateSvga.clearAnimation()
    }

    private var translateCallbackJob: Job? = null
    private var translateCallback: ((String?) -> Unit)? = { result ->
        translateCallbackJob = lifecycleScope.launch(Dispatchers.Main) {
            hideTranslateLading()
            translatedText = result
            if (!result.isNullOrBlank()) {
                binding.introTranslated.text = result
                binding.introTranslated.visibility = View.VISIBLE
                isTranslationShown = true
            } else {
                binding.introTranslated.visibility = View.GONE
            }
        }
    }

    fun updateBaseInfo(userInfo: UserInfo) {
        if (canShowPhotoPager) {
            updatePhotoPager(userInfo)
        }
        binding.apply {
            intro.text = userInfo.about ?: ""
            introOriginal = userInfo.about ?: ""
            nickname.text = userInfo.nickname
            age.text = userInfo.age?.toString() ?: ""
            country.text = userInfo.country ?: ""
            tvLanguage.text = userInfo.language?.replaceFirstChar { it.uppercaseChar() } ?: ""

            GlobalManager.setViewRoundBackground(
                giftIndicator, resources.getColor(R.color.tab_indicator_color)
            )

            // 自动翻译逻辑
            val context = this@BroadcasterDetailActivity
            LanguageUtils.getAppLanguage(context = context, scope = lifecycleScope) { userLang ->
                lifecycleScope.launch {
                    // 检查自动翻译开关
                    val autoTranslateEnabled =
                        SharePreferenceUtil.getBoolean(Constant.AUTO_TRANSLATE, true, "settings")
                    lifecycleScope.launch(Dispatchers.Main) {
                        // 假设 userInfo.aboutLang 表示介绍的原始语言（如有）
                        if (autoTranslateEnabled && introOriginal != null && introOriginal!!.isNotBlank()) {
                            // 自动翻译
                            showTranslateLoading()
                            TranslateManager.translate(
                                lifecycleScope, introOriginal!!, userLang,
                                callback = translateCallback
                            )
                        } else {
                            binding.introTranslated.visibility = View.GONE
                            isTranslationShown = false
                        }
                    }
                }
            }
        }
    }

    // 更新轮播
    fun updatePhotoPager(userInfo: UserInfo) {
        val realImageUrls = mutableListOf<GlideUtils.DoubleUrl>()
        if (broadcaster != null) {
            realImageUrls.add(GlideUtils.DoubleUrl(broadcaster!!.avatar, broadcaster!!.avatarThumbUrl))
        } else {
            realImageUrls.add(GlideUtils.DoubleUrl(userInfo.avatarUrl, userInfo.avatarThumbUrl))
        }

        if (!userInfo.mediaList.isNullOrEmpty()) {
            for (media in userInfo.mediaList) {
                realImageUrls.add(GlideUtils.DoubleUrl(media.mediaUrl, media.thumbUrl))
            }
        }

        binding.photoPager.visibility = View.INVISIBLE
        // 使用PhotoPagerHelper设置轮播，支持自动轮播，加载到有图片时才可见
        PagerHelper.setupPhotoPager(
            viewPager = binding.photoPager,
            doubleUrls = realImageUrls,
            enableAutoScroll = true,
            onPageSelected = { realIndex ->
                binding.photoCount.text = "${realIndex + 1}/${realImageUrls.size}"
            }
        )
        // 设置指示器
        if (realImageUrls.size > 1) {
            binding.photoCount.visibility = View.VISIBLE
            binding.photoCount.text = "1/${realImageUrls.size}"
        } else {
            binding.photoCount.text = "1/1"
            binding.photoCount.visibility = View.GONE
        }
    }

    fun updateExtraLabels(extra: BroadcasterExtraInfo) {
        if (StrategyManager.isReviewPkg()) {
            binding.tagsLayout.visibility = View.GONE
            return
        }
        val container = binding.tagsContainer
        container.removeAllViews()

//        // 1. 添加第一个带虚线边框和图标的按钮
//        container.addView(ImageView(this).apply {
//            setImageResource(R.drawable.labels) // 用已有的labels图标.
//            layoutParams = FlexboxLayout.LayoutParams(
//                DisplayUtils.dp2px(28f), DisplayUtils.dp2px(28f)
//            ).apply {
//                setMargins(0, 0, 0, 16)
//            }
//            background = DrawableUtils.createRoundRectDashedDrawable(0, 0f, Color.BLACK, 1, 1f, 3f)
//        })

        // 2. 添加labelsList标签
        extra.labelsList?.mapNotNull { label ->
            val parts = label.split(":")
            if (parts.size == 2) {
                val name = parts[0].trim()
                val count = parts[1].trim().toIntOrNull() ?: 0
                name to count
            } else null
        }?.sortedByDescending { it.second }?.take(4)?.forEach { (name, _) ->
            container.addView(TextView(this).apply {
                text = name
                setTextColor(Color.BLACK)
                textSize = 12f
                val paddingHorizontal = DisplayUtils.dp2px(14f)
                val paddingVertical = DisplayUtils.dp2px(7f)
                setPadding(paddingHorizontal, paddingVertical, paddingHorizontal, paddingVertical)
                setTypeface(Typeface.create("sans-serif", Typeface.NORMAL));
                GlobalManager.setViewRoundBackground(this, "#F3F5FA".toColorInt())
                layoutParams = FlexboxLayout.LayoutParams(
                    LinearLayout.LayoutParams.WRAP_CONTENT, LinearLayout.LayoutParams.WRAP_CONTENT
                ).apply {
                    setMargins(0, 0, DisplayUtils.dp2px(5f), 16)
                }
            })
        }
    }

    // 审核模式下用的四个
    val reviewGiftList =
        listOf<String>("1", "2", "3", "4", "5", "6", "7", "8", "9", "10").shuffled().take(4)

    fun updateExtraGift(giftList: List<GetGiftCountItem>?) {
        val giftListLayout = binding.giftListLayout
        giftListLayout.removeAllViews()
        if (giftList.isNullOrEmpty()) {
            binding.giftTitle.visibility = View.GONE
            giftListLayout.visibility = View.GONE
            return
        }
        giftListLayout.visibility = View.VISIBLE
        binding.giftTitle.visibility = View.VISIBLE
        val maxGifts = 4
        val context = this
        val sortedGifts = giftList.sortedByDescending { it.num ?: 0 }.take(maxGifts)
        val itemMargin = DisplayUtils.dp2px(8f)
        val itemWidth = if (giftListLayout.width > 0) {
            (giftListLayout.width - giftListLayout.paddingStart - giftListLayout.paddingEnd - itemMargin * (maxGifts - 1)) / maxGifts
        } else {
            DisplayUtils.dp2px(74f) // fallback
        }
        for ((idx, item) in sortedGifts.withIndex()) {
            val itemView = LayoutInflater.from(context)
                .inflate(R.layout.item_broadcaster_detail_gift, giftListLayout, false)
            DrawableUtils.setRoundRectBackground(
                itemView, ("#FFF3F5FA").toColorInt(), DisplayUtils.dp2pxInternal(13f).toFloat()
            )
            val ivGift = itemView.findViewById<ImageView>(R.id.iv_gift)
            val tvGiftCount = itemView.findViewById<TextView>(R.id.tv_gift_count)
            if (StrategyManager.isReviewPkg()) {
                ivGift.setImageResource(CustomUtils.getGiftResIdById(reviewGiftList[idx]))
            } else {
                // 加载图片
                ivGift.setImageResource(CustomUtils.getGiftResIdById(item.code))
            }
            // 设置数量
            tvGiftCount.text = "x${item.num ?: 0}"
            // 设置宽度和间距
            val params = itemView.layoutParams as LinearLayout.LayoutParams
            params.width = itemWidth
            if (idx > 0) params.leftMargin = itemMargin
            itemView.layoutParams = params
            giftListLayout.addView(itemView)
        }
    }

    fun updateStatus(status: String) {
        var newStatus = status
        // 审核模式下
        if (StrategyManager.isReviewPkg()) {
            if (broadcaster?.isAnswer == true && !StrategyManager.reviewPkgUsers.contains(
                    broadcaster?.userId
                )
            ) {
                newStatus = CallStatus.ONLINE
            } else {
                newStatus = GlobalManager.getReviewOtherStatus(broadcaster?.userId)
            }
        }

        Log.d(this.javaClass.name, "status: ${newStatus}")

        GlobalManager.setViewRoundBackground(
            binding.statusIndicator, GlobalManager.getStatusColor(newStatus)
        )
        binding.statusText.text = newStatus

        val btnVideoCall = binding.btnVideoCall
        val videoCallLayout = binding.videoCallLayout
        val videoCallTip = binding.videoCallTip
        val firstLineTip = binding.firstLineTip
        val secondLine = binding.secondLine
        //审核模式下隐藏
        if (StrategyManager.isReviewPkg()) {
            secondLine.visibility = View.GONE
        }

        when (newStatus) {
            CallStatus.ONLINE, CallStatus.AVAILABLE -> {
                videoCallLayout.background = resources.getDrawable(R.drawable.video_btn_bg)

                videoCallTip.text = "Video Call"
                GlobalManager.setTextTypefaceSansSerif(videoCallTip, true)

                firstLineTip.visibility = View.GONE
                val price = viewModel.userInfo?.unitPrice ?: broadcaster?.callCoins
                binding.secondLine.text = CustomUtils.createCoinSpannableText(
                    context = this,
                    text = "(icon${price}/min)",
                    coinSizeDp = 14f,
                    alignment = ImageSpan.ALIGN_BOTTOM,
                    spacesBefore = 0,
                    spacesAfter = 1
                )

                CustomUtils.playSvga(btnVideoCall, "info_video.svga")
                btnVideoCall.setPadding(0, 0, 0, 0)
                videoCallLayout.isClickable = true
                videoCallLayout.click {
                    handleVideoCallClick(newStatus)
                }
            }

            else -> {
                // 灰色背景和不可用样式
                val videoBtnPadding = DisplayUtils.dp2pxInternal(3f)
                btnVideoCall.setPadding(
                    videoBtnPadding, videoBtnPadding, videoBtnPadding, videoBtnPadding
                )
                videoCallLayout.background = resources.getDrawable(R.drawable.video_btn_gray)
                videoCallLayout.isClickable = false
                videoCallLayout.isTouchable = false
                btnVideoCall.stopAnimation(true)
                btnVideoCall.setImageResource(R.drawable.video_disabled)
                videoCallTip.text = "Video Call"
                val price = viewModel.userInfo?.unitPrice ?: broadcaster?.callCoins
                binding.firstLineTip.text = CustomUtils.createCoinSpannableText(
                    context = this,
                    text = "(icon${price}/min)",
                    coinSizeDp = 14f,
                    alignment = ImageSpan.ALIGN_BOTTOM,
                    spacesBefore = 0,
                    spacesAfter = 1
                )
                firstLineTip.visibility = View.VISIBLE

                //审核模式下隐藏
                if (StrategyManager.isReviewPkg()) {
                    firstLineTip.visibility = View.GONE
                }

                secondLine.text = "Unacceptable Now"

                // 添加非在线状态的点击处理
                videoCallLayout.click {
                    handleVideoCallClick(newStatus)
                }
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        PagerHelper.cleanup()
        binding.followLayout.stopFollowHighlightTask()
        translateCallbackJob?.cancel()
        translateCallbackJob = null
        translateCallback = null
    }

    override fun onRequestPermissionsResult(
        requestCode: Int, permissions: Array<String>, grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        AppPermissionManager.handleRequestPermissionsResult(
            this, requestCode, permissions, grantResults
        )
    }

    /**
     * 显示权限引导对话框
     */
    private fun showPermissionGuideDialog() {
        val dialog = AlertDialog.Builder(this).setTitle("🔧 Permission Settings Guide").setMessage(
            "It looks like you haven't enabled the relevant permissions yet. For the best user experience, we recommend enabling the following permissions:\n\n" + "📷 Album permission: Upload avatar and photos\n" + "📹 Camera permission: Video calls\n" + "🎤 Microphone permission: Voice calls\n\n" + "Click the button below and we'll open the settings page for you."
        ).setPositiveButton("⚙️ Go to Settings") { _, _ ->
            val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
            val uri = Uri.fromParts("package", packageName, null)
            intent.data = uri
            startActivity(intent)
        }.setNegativeButton("❌ Later") { _, _ ->
            // 用户选择稍后再说，不做任何操作
        }.setCancelable(true).create()

        dialog.show()
    }

    override fun onResume() {
        super.onResume()
        viewModel.userId?.let { id ->
            UserInfoManager.getCachedStatus(id).let { status ->
                if (!status.isNullOrEmpty()) {
                    updateStatus(status)
                }
            }
        }
    }

    override fun onPause() {
        super.onPause()
    }

    private fun showMoreOptionsDialog() {
        val isFollowed = viewModel.userInfo?.isFriend == true
        val isBlocked = viewModel.userInfo?.isBlock == true

        // 使用通用底部对话框组件
        val sheet = CommonActionBottomSheet.builder()

        // 只有已关注时才显示取消关注选项
        if (isFollowed) {
            sheet.addAction(getString(R.string.str_btn_unfollow)) {
                unfollowUser()
            }
        }

        // 根据拉黑状态显示不同的选项
        if (isBlocked) {
            sheet.addAction(getString(R.string.str_btn_unblock)) {
                unblockUser()
            }
        } else {
            sheet.addAction(getString(R.string.str_btn_block)) {
                blockUser()
            }
        }

        // 添加举报选项
        sheet.addAction(getString(R.string.str_btn_report)) {
            showReportDialog()
        }

        // 显示对话框
        sheet.build().show(supportFragmentManager, "BroadcasterMoreOptionsSheet")
    }

    private fun followUser() {
        if (viewModel.userInfo != null && viewModel.userInfo?.isFriend == false) {
            binding.followLayout.addFriend(lifecycleScope, viewModel.userInfo?.userId!!) { friend ->
                if (friend) {
                    viewModel.userInfo?.isFriend = true
//                    ToastUtils.showToast("Follow successfully")
                    ToastUtils.showToast(CallmeApplication.context.getString(R.string.follow_success))
                } else {
//                    ToastUtils.showToast("Follow failed")
                    ToastUtils.showToast(CallmeApplication.context.getString(R.string.follow_failed))
                }
            }
        }
    }

    private fun unfollowUser() {
        if (viewModel.userInfo != null && viewModel.userInfo?.isFriend == true) {
            binding.followLayout.unfollow(lifecycleScope, viewModel.userInfo?.userId!!) { success ->
                if (success) {
                    viewModel.userInfo?.isFriend = false
                    ToastUtils.showToast(CallmeApplication.context.getString(R.string.unfollow_success))
                } else {
                    ToastUtils.showToast(CallmeApplication.context.getString(R.string.unfollow_failed))
                }
            }
        }
    }

    private fun blockUser() {
        val userId = viewModel.userInfo?.userId ?: return
        viewModel.block(userId) { success ->
            runOnUiThread {
                if (success) {
                    // 显示空状态
                    updateEmptyState(true)
                    ToastUtils.showToast(CallmeApplication.context.getString(R.string.block_successfully))
                    //如果拉黑了，取消关注
                    unfollowUser()
                    // TODO: 全局屏蔽该主播相关内容（墙、消息、匹配、电话等）
                } else {
                    ToastUtils.showToast(CallmeApplication.context.getString(R.string.block_failed))
                }
            }
        }
    }

    private fun unblockUser() {
        val userId = viewModel.userInfo?.userId ?: return
        viewModel.unblock(userId) { success ->
            runOnUiThread {
                if (success) {
                    // 显示正常状态
                    updateEmptyState(false)
                    ToastUtils.showToast(CallmeApplication.context.getString(R.string.unblock_successfully))
                    // TODO: 取消全局屏蔽该主播
                } else {
                    ToastUtils.showToast(CallmeApplication.context.getString(R.string.unblock_failed))
                }
            }
        }
    }

    /**
     * 更新空状态显示
     * @param isEmpty 数据是否为空
     */
    private fun updateEmptyState(isEmpty: Boolean) {
        val bottomSheetBehavior =
            BottomSheetBehavior.from<RelativeLayout>(findViewById(R.id.main_content_layout))
        if (isEmpty) {
            // 显示空状态，隐藏RecyclerView
            if (emptyViewInflated == null) {
                emptyViewInflated = binding.emptyView.inflate()
            }

            emptyViewInflated?.visibility = View.VISIBLE
            bottomSheetBehavior.isDraggable = false
            binding.detailScrollview.visibility = View.GONE
            binding.lineBottomButton.visibility = View.GONE
        } else {
            // 隐藏空状态，显示RecyclerView
            emptyViewInflated?.visibility = View.GONE
            bottomSheetBehavior.isDraggable = true
            binding.detailScrollview.visibility = View.VISIBLE
            binding.lineBottomButton.visibility = View.VISIBLE
        }
    }


    private fun showReportDialog() {
        val reportOptions = resources.getStringArray(R.array.report_reason)/*val reportOptions = arrayOf(
            "Abusive Behavior", "Cheat Behavior", "Pornography Behavior",
            "Bloody Violence", "Harassment Behavior", "Others"
        )*/

        // 创建举报选项的底部对话框
        val reportSheet = CommonActionBottomSheet.builder()
//            .setTitle("举报原因")

        // 添加所有举报选项
        reportOptions.forEachIndexed { index, option ->
            reportSheet.addAction(option) {
                val userId = viewModel.userInfo?.userId ?: return@addAction
                val complainSub = reportOptions[index]

                viewModel.reportUser(userId, complainSub) { success ->
                    runOnUiThread {
                        if (success) {
                            ToastUtils.showToast(CallmeApplication.context.getString(R.string.report_success))
                        } else {
                            ToastUtils.showToast(CallmeApplication.context.getString(R.string.report_failed))
                        }
                    }
                }
            }
        }

        reportSheet.build().show(supportFragmentManager, "ReportOptionsSheet")
    }

    /**
     * 处理视频通话按钮点击事件
     * 统一处理金币检查、状态检查和权限检查
     */
    private fun handleVideoCallClick(currentStatus: String) {
        if (!StrategyManager.isReviewPkg()) {
            // 1. 先判断金币是否足够
            val availableCoins = UserInfoManager.myUserInfo?.availableCoins ?: 0
            val unitPrice = viewModel.userInfo?.unitPrice ?: broadcaster?.callCoins ?: 0
            if (availableCoins < unitPrice) {
                // 弹出金币充值弹窗（新版：自动拉取商品数据,显示主播每分钟收费）
                val dialog = InsufficientBalanceDialog.newInstance(
                    unitPrice,
                    RechargeSource.ANCHOR_PROFILE_VIDEO_CALL.value()
                )
                dialog.show(supportFragmentManager, "insufficient_balance")
                return
            }

            // 2. 金币足够的情况下，检查主播状态
            if (currentStatus != CallStatus.ONLINE && currentStatus != CallStatus.AVAILABLE) {
                // 显示状态不可用的toast
                val statusText = CallStatus.getDisplayText(currentStatus)
                val message = getString(R.string.user_status_not_available, statusText)
                ToastUtils.showToast(message)
                return
            }
        }

        // 3. 检查网络连接
        if (!SocketManager.instance.isConnected()) {
            Timber.d("Long connection network is offline")
            ToastUtils.showToast(CallmeApplication.context.getString(R.string.net_error_and_try_again))
            return
        }

        // 4. 金币和状态检查通过后，再检查摄像头和麦克风权限
        AppPermissionManager.checkAndRequestCameraMicrophonePermission(this, onGranted = {
            // 权限授权成功，再获取一次在线状态确认
            UserInfoManager.loadOnlineStatus(
                lifecycleScope, viewModel.userInfo?.userId.toString()
            ) { status, error ->
                runOnUiThread {
                    if (error == null && status != null) {
                        if (CallStatus.ONLINE != status && CallStatus.AVAILABLE != status) {
                            updateStatus(status)
                            return@runOnUiThread
                        }
                        VideoCallActivity.startOutgoing(
                            context = this@BroadcasterDetailActivity,
                            userId = viewModel.userInfo?.userId!!,
                            avatarUrl = viewModel.userInfo?.avatarUrl.toString(),
                            nickname = viewModel.userInfo?.nickname.toString(),
                            age = viewModel.userInfo?.age.toString(),
                            country = viewModel.userInfo?.country.toString(),
                            unitPrice = viewModel.userInfo?.unitPrice.toString()
                        )
                    }
                }
            }
        }, onDenied = { deniedPermissions, permanentlyDeniedPermissions ->

        })
    }
}