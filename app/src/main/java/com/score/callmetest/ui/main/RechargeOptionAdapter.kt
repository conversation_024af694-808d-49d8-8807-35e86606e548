package com.score.callmetest.ui.main

import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.graphics.Color
import android.graphics.drawable.GradientDrawable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.graphics.toColorInt
import androidx.recyclerview.widget.RecyclerView
import com.score.callmetest.databinding.ItemRechargeOptionBinding
import com.score.callmetest.manager.CountdownManager
import com.score.callmetest.util.AnimatorUtil
import com.score.callmetest.util.CustomUtils
import com.score.callmetest.util.DisplayUtils
import com.score.callmetest.util.DrawableUtils
import com.score.callmetest.util.ThreadUtils
import com.score.callmetest.util.TimeUtils
import com.score.callmetest.util.click
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import timber.log.Timber
import java.lang.ref.WeakReference

class RechargeOptionAdapter(
    private var options: List<RechargeOption>,
    private val onClick: (RechargeOption) -> Unit,
    private val limitToSix: Boolean = false,
    private val onCountdownEnd: (() -> Unit)? = null
) : RecyclerView.Adapter<RechargeOptionAdapter.ViewHolder>(), DefaultLifecycleObserver {

    // 处理后的展示数据（排序+可选限制6个）
    private val displayOptions: List<RechargeOption> by lazy {
        val sortedOptions = options.sortedByDescending { option ->
            when (option.type) {
                "2" -> 2 // 活动商品最高优先级
                "1" -> 1 // 促销商品中等优先级
                else -> 0 // 普通商品最低优先级
            }
        }

        if (limitToSix) {
            sortedOptions.take(6) // 限制6个
        } else {
            sortedOptions // 不限制数量
        }
    }
    // 提供两种创建方式
    companion object {
        // 方式1：排序并限制6个
        fun createWithLimit(
            options: List<RechargeOption>,
            onClick: (RechargeOption) -> Unit,
            onCountdownEnd: (() -> Unit)?,
            lifecycleOwner: LifecycleOwner? = null
        ): RechargeOptionAdapter {
            val adapter = RechargeOptionAdapter(options, onClick, true, onCountdownEnd)
            lifecycleOwner?.let { adapter.attachLifecycleOwner(it) }
            return adapter
        }

        // 方式2：只排序不限制数量
        fun createWithoutLimit(
            options: List<RechargeOption>,
            onClick: (RechargeOption) -> Unit,
            onCountdownEnd: (() -> Unit)?,
            lifecycleOwner: LifecycleOwner? = null
        ): RechargeOptionAdapter {
            val adapter = RechargeOptionAdapter(options, onClick, false, onCountdownEnd)
            lifecycleOwner?.let { adapter.attachLifecycleOwner(it) }
            return adapter
        }
    }

    // 存储监听者ID，用于清理
    private val listenerIds = mutableMapOf<Int, String>()

    // 活跃的ViewHolder弱引用集合
    private val activeViewHolders = mutableSetOf<WeakReference<ViewHolder>>()

    // 生命周期所有者，用于监听生命周期事件
    private var lifecycleOwner: LifecycleOwner? = null

    // 标记是否已经清理过，避免重复清理
    private var isCleared = false

    /**
     * 附加生命周期所有者，用于监听生命周期事件
     */
    fun attachLifecycleOwner(owner: LifecycleOwner) {
        lifecycleOwner = owner
        owner.lifecycle.addObserver(this)
    }

    /**
     * 分离生命周期所有者
     */
    fun detachLifecycleOwner() {
        lifecycleOwner?.lifecycle?.removeObserver(this)
        lifecycleOwner = null
    }

    // 旋转动画相关常量
    private val ROTATE_DURATION = 6000L

    // SVGA动画文件名常量
    private val COUNTDOWN_SVGA_NAME = "countdown.svga" // 倒计时背景动画文件名

    // 实现 DefaultLifecycleObserver 接口
    override fun onDestroy(owner: LifecycleOwner) {
        Timber.tag("RechargeOptionAdapter").d("生命周期onDestroy触发，执行最终清理")
        performFinalCleanup()
    }

    var selectedIndex: Int = 0
        set(value) {
            val old = field
            if (old != value) {
                field = value
                notifyItemChanged(old)
                notifyItemChanged(field)
            }
        }

    inner class ViewHolder(val binding: ItemRechargeOptionBinding) : RecyclerView.ViewHolder(binding.root) {
        var listenerId: String? = null
        // 添加旋转动画引用
        var rotateAnimator: ObjectAnimator? = null
        var scaleAnimatorSet: AnimatorSet? = null
        
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val binding = ItemRechargeOptionBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return ViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val option = displayOptions[position]

        // 根据商品类型设置不同的图标
        when (option.type) {
            "1", "2" -> {
                // 促销商品：设置促销图标
                holder.binding.ivIcon1.setImageResource(option.iconRes)
            }
            else -> {
                // 普通商品：设置普通图标
                holder.binding.ivIcon.setImageResource(option.iconRes)
            }
        }

        holder.binding.tvCoin.text = option.coinAmount.toString()

        // 设置价格信息
        setupPriceInfo(holder, option)

        if (option.extraCoinPercent != null && option.extraCoinPercent > 0) {
            holder.binding.tvBonus.text = "+${option.extraCoinPercent}%"
            holder.binding.imageCoin.visibility = View.VISIBLE
            holder.binding.tvBonus.visibility = View.VISIBLE
        } else {
            holder.binding.tvBonus.visibility = View.GONE
            holder.binding.imageCoin.visibility = View.GONE
        }

        // 设置HOT标签的可见性和内容
        setupHotLabel(holder, option, position)

        val selected = position == selectedIndex
        holder.itemView.isSelected = selected

        // 使用AnimatorUtil制作选中时的动效
        if (selected) {
            cancelIconAnimations(holder)

            when (option.type) {
                "1", "2" -> {
                    setupPromotionSelectedAnimation(holder)
                }
                else -> {
                    setupNormalSelectedAnimation(holder)
                }
            }
        } else {
            cancelIconAnimations(holder)

            when (option.type) {
                "1", "2" -> {
                    restorePromotionIconState(holder)
                }
                else -> {
                    restoreNormalIconState(holder)
                }
            }
        }

        // 设置价格布局背景
        holder.binding.priceLayout.background = DrawableUtils.createGradientDrawable(
            colors = intArrayOf("#FFF4CD".toColorInt(), "#FFFEDB".toColorInt()),
            orientation = GradientDrawable.Orientation.TOP_BOTTOM,
        )

        setupUILayoutAndBackground(holder, option, selected)
        setupClickEvents(holder, option, position)

        // 记录活跃ViewHolder
        activeViewHolders.add(WeakReference(holder))
    }

    override fun getItemCount(): Int = displayOptions.size

    override fun onViewRecycled(holder: ViewHolder) {
        super.onViewRecycled(holder)

        // 清理倒计时监听者
        val position = holder.adapterPosition
        if (position != RecyclerView.NO_POSITION) {
            val listenerId = listenerIds[position]
            if (listenerId != null) {
                val option = options.getOrNull(position)
                if (option?.goodsCode != null) {
                    CountdownManager.removeListener(
                        option.type,
                        option.goodsCode,
                        listenerId
                    )
                }
                listenerIds.remove(position)
            }
        }

        holder.listenerId = null

        // 清理SVGA动画
        holder.binding.svgaCountdown.stopAnimation()
        holder.binding.svgaCountdown.clear()

        // 隐藏倒计时容器
        holder.binding.frameCountdowm.visibility = View.GONE

        // 清理图标动画
        cancelIconAnimations(holder)

        // 恢复普通图标原始状态
        holder.binding.ivIcon.scaleX = 1f
        holder.binding.ivIcon.scaleY = 1f
        holder.binding.ivIcon.rotation = 0f

        // 恢复促销图标原始状态
        holder.binding.ivIcon1.scaleX = 1f
        holder.binding.ivIcon1.scaleY = 1f
        holder.binding.ivIcon1.rotation = 0f

        // 恢复普通背景动画容器原始状态
        holder.binding.bgAnimationImage.rotation = 0f
        holder.binding.bgAnimationImage.visibility = View.GONE

        // 恢复促销背景动画容器原始状态
        holder.binding.bgAnimationImage1.rotation = 0f
        holder.binding.bgAnimationImage1.visibility = View.GONE

        // 移除活跃ViewHolder引用
        activeViewHolders.removeAll { it.get() == holder || it.get() == null }
    }

    override fun onDetachedFromRecyclerView(recyclerView: RecyclerView) {
        super.onDetachedFromRecyclerView(recyclerView)
        Timber.tag("RechargeOptionAdapter").d("onDetachedFromRecyclerView触发，执行清理")
        performFinalCleanup()
    }

    /**
     * 执行最终清理，确保所有资源都被正确释放
     */
    private fun performFinalCleanup() {
        if (isCleared) {
            Timber.tag("RechargeOptionAdapter").d("已经清理过，跳过重复清理")
            return
        }

        isCleared = true
        Timber.tag("RechargeOptionAdapter").d("开始执行最终清理")

        // 清理所有监听者
        clearAllListeners()

        // 取消所有活跃ViewHolder上的动画
        activeViewHolders.forEach { ref ->
            ref.get()?.let { cancelIconAnimations(it) }
        }
        activeViewHolders.clear()

        // 分离生命周期观察者
        detachLifecycleOwner()

        Timber.tag("RechargeOptionAdapter").d("最终清理完成")
    }

    private fun setupHotLabel(holder: ViewHolder, option: RechargeOption, position: Int) {
        // 清理之前的监听者
        val oldListenerId = listenerIds[position]
        if (oldListenerId != null && option.goodsCode != null) {
            CountdownManager.removeListener(
                option.type,
                option.goodsCode,
                oldListenerId
            )
            listenerIds.remove(position)
        }
        holder.listenerId = null

        when (option.type) {
            "1", "2" -> {
                val goodsCode = option.goodsCode
                val goodsType = option.type
                if (!goodsCode.isNullOrEmpty() && option.remainMilliseconds != null && option.remainMilliseconds > 0) {
                    CountdownManager.initOrUpdateCountdown(
                        goodsType,
                        goodsCode,
                        option.remainMilliseconds
                    )

                    if (CountdownManager.isExpired(goodsType, goodsCode)) {
                        holder.binding.tvHotRed.visibility = View.GONE
                        holder.binding.frameCountdowm.visibility = View.GONE
                        onCountdownEnd?.invoke()
                    } else {
                        // 初始化倒计时显示
                        val remainingMillis = CountdownManager.getCurrentRemainingTime(option.type?:"", option.goodsCode?:"")
                        holder.binding.tvCountdown.text = TimeUtils.formatSecondsToTime(remainingMillis/ 1000)

                        setupCountdownAnimation(holder, goodsCode, goodsType, position)
                    }
                } else {
                    holder.binding.tvHotRed.visibility = View.GONE
                    holder.binding.frameCountdowm.visibility = View.GONE
                }
            }
            else -> {
                holder.binding.frameCountdowm.visibility = View.GONE
                if (!option.tags.isNullOrEmpty()) {
                    holder.binding.tvHotRed.visibility = View.VISIBLE
                    holder.binding.tvHotRed.text = option.tags
                } else {
                    holder.binding.tvHotRed.visibility = View.GONE
                }
            }
        }
    }

    private fun setupCountdownAnimation(holder: ViewHolder, goodsCode: String, goodsType: String, position: Int) {
        holder.binding.frameCountdowm.visibility = View.VISIBLE
        holder.binding.tvHotRed.visibility = View.GONE

        // 播放SVGA倒计时背景动画（循环播放）
        CustomUtils.playSvga(
            holder.binding.svgaCountdown,
            COUNTDOWN_SVGA_NAME,
            loops = 0
        )

        startCountdownWithSvga(holder, goodsCode, goodsType, position)
    }

    private fun startCountdownWithSvga(holder: ViewHolder, goodsCode: String, goodsType: String, position: Int) {
        val listenerId = CountdownManager.startCountdown(
            type = goodsType,
            code = goodsCode,
            onTick = { remainingMillis ->
                ThreadUtils.runOnMain {
                    try {
                        holder.binding.tvCountdown.text = TimeUtils.formatSecondsToTime(remainingMillis / 1000)
                    } catch (e: Exception) {
                        Timber.tag("RechargeOptionAdapter").e(e, "更新倒计时UI失败")
                    }
                }
            },
            onFinish = {
                ThreadUtils.runOnMain {
                    try {
                        holder.binding.frameCountdowm.visibility = View.GONE
                        holder.listenerId = null

                        val adapterPosition = holder.adapterPosition
                        if (adapterPosition != RecyclerView.NO_POSITION) {
                            listenerIds.remove(adapterPosition)
                        }

                        // 通知外部计时结束
                        onCountdownEnd?.invoke()
                    } catch (e: Exception) {
                        Timber.tag("RechargeOptionAdapter").e(e, "倒计时结束处理失败")
                    }
                }
            }
        )

        holder.listenerId = listenerId
        if (listenerId != null) {
            listenerIds[position] = listenerId
        }
    }

    /**
     * 取消图标动画(金币闪光)
     */
    private fun cancelIconAnimations(holder: ViewHolder) {
        AnimatorUtil.releaseAnimator(holder.rotateAnimator)
        holder.rotateAnimator = null

        AnimatorUtil.releaseAnimator(holder.scaleAnimatorSet)
        holder.scaleAnimatorSet = null

        // 重置普通图标状态
        holder.binding.ivIcon.run {
            animate().cancel()
            clearAnimation()
        }

        // 重置促销图标状态
        holder.binding.ivIcon1.run {
            animate().cancel()
            clearAnimation()
        }

        // 重置普通背景动画容器状态
        holder.binding.bgAnimationImage.run {
            animate().cancel()
            clearAnimation()
            rotation = 0f
            visibility = View.GONE
        }

        // 重置促销背景动画容器状态
        holder.binding.bgAnimationImage1.run {
            animate().cancel()
            clearAnimation()
            rotation = 0f
            visibility = View.GONE
        }
    }

    /**
     * 设置价格信息
     */
    private fun setupPriceInfo(holder: ViewHolder, option: RechargeOption) {
        holder.binding.tvPrice.text = option.price
        setupOldPrice(holder.binding.tvOldPrice, option)
    }

    /**
     * 设置旧价格的显示和下划线
     */
    private fun setupOldPrice(priceTextView: TextView, option: RechargeOption) {
        // 如果价格符号是"₹"，则不显示旧价格和下划线
        if (option.priceSymbol == "₹" && option.type == "0") {
            priceTextView.text = ""
            priceTextView.paint.isStrikeThruText = false
            priceTextView.visibility = View.GONE
            return
        }

        if (!option.oldPrice.isNullOrEmpty() &&
            option.oldPrice.isNotBlank() &&
            CustomUtils.comparePrice(option.oldPrice, option.price) > 0) {
            priceTextView.text = option.oldPrice
            priceTextView.paint.isStrikeThruText = true
            priceTextView.visibility = View.VISIBLE
        } else {
            priceTextView.text = ""
            priceTextView.paint.isStrikeThruText = false
            priceTextView.visibility = View.GONE
        }
    }

    /**
     * 根据商品类型设置UI布局和背景
     */
    private fun setupUILayoutAndBackground(holder: ViewHolder, option: RechargeOption, selected: Boolean) {
        when (option.type) {
            "1", "2" -> setupPromotionProductUI(holder)
            else -> setupNormalProductUI(holder, selected)
        }
    }

    /**
     * 设置促销商品和活动商品的UI
     */
    private fun setupPromotionProductUI(holder: ViewHolder) {
        holder.binding.priceLayout.visibility = View.VISIBLE
        holder.binding.ivIcon1.visibility = View.VISIBLE
        holder.binding.bgAnimationImage.visibility = View.GONE
        holder.binding.ivIcon.visibility = View.GONE

        holder.binding.cardView.background = DrawableUtils.createRoundRectDrawable(
            color = "#C19BFF".toColorInt(),
            radius = DisplayUtils.dp2pxInternalFloat(18f)
        )
    }

    /**
     * 设置普通商品的UI
     */
    private fun setupNormalProductUI(holder: ViewHolder, selected: Boolean) {
        holder.binding.priceLayout.visibility = View.VISIBLE
        holder.binding.ivIcon.visibility = View.VISIBLE
        holder.binding.bgAnimationImage1.visibility = View.GONE
        holder.binding.ivIcon1.visibility = View.GONE

        holder.binding.cardView.background = DrawableUtils.createRoundRectDrawable(
            color = Color.WHITE,
            radius = DisplayUtils.dp2pxInternalFloat(18f)
        )
    }

    /**
     * 设置点击事件
     */
    private fun setupClickEvents(holder: ViewHolder, option: RechargeOption, position: Int) {
        holder.binding.priceLayout.isClickable = false
        holder.binding.priceLayout.isFocusable = false

        holder.itemView.click {
            if (selectedIndex != position) {
                selectedIndex = position
            }
            onClick(option)
        }
    }

    /**
     * 设置促销商品选中时的动画
     */
    private fun setupPromotionSelectedAnimation(holder: ViewHolder) {
        holder.binding.bgAnimationImage1.visibility = View.VISIBLE

        holder.scaleAnimatorSet = AnimatorUtil.scale(
            view = holder.binding.ivIcon1,
            scaleFrom = 1f,
            scaleTo = 1.1f,
            scaleDuration = 300
        ) {
            holder.rotateAnimator = AnimatorUtil.rotate(
                view = holder.binding.bgAnimationImage1,
                duration = ROTATE_DURATION,
                repeatCount = ObjectAnimator.INFINITE
            )
        }
    }

    /**
     * 设置普通商品选中时的动画
     */
    private fun setupNormalSelectedAnimation(holder: ViewHolder) {
        holder.binding.bgAnimationImage.visibility = View.VISIBLE

        holder.scaleAnimatorSet = AnimatorUtil.scale(
            view = holder.binding.ivIcon,
            scaleFrom = 1f,
            scaleTo = 1.1f,
            scaleDuration = 300
        ) {
            holder.rotateAnimator = AnimatorUtil.rotate(
                view = holder.binding.bgAnimationImage,
                duration = ROTATE_DURATION,
                repeatCount = ObjectAnimator.INFINITE
            )
        }
    }

    /**
     * 恢复促销商品图标状态
     */
    private fun restorePromotionIconState(holder: ViewHolder) {
        holder.binding.bgAnimationImage1.visibility = View.GONE

        holder.scaleAnimatorSet = AnimatorUtil.scale(
            view = holder.binding.ivIcon1,
            scaleFrom = holder.binding.ivIcon1.scaleX,
            scaleTo = 1f,
            scaleDuration = 200
        )
    }

    /**
     * 恢复普通商品图标状态
     */
    private fun restoreNormalIconState(holder: ViewHolder) {
        holder.binding.bgAnimationImage.visibility = View.GONE

        holder.scaleAnimatorSet = AnimatorUtil.scale(
            view = holder.binding.ivIcon,
            scaleFrom = holder.binding.ivIcon.scaleX,
            scaleTo = 1f,
            scaleDuration = 200
        )
    }

    /**
     * 清理所有倒计时监听者，防止内存泄露
     */
    fun clearAllListeners() {
        if (listenerIds.isEmpty()) {
            Timber.tag("RechargeOptionAdapter").d("没有监听者需要清理")
            return
        }

        Timber.tag("RechargeOptionAdapter").d("开始清理 ${listenerIds.size} 个倒计时监听者")

        val clearedCount = mutableMapOf<String, Int>()

        listenerIds.forEach { (position, listenerId) ->
            val option = displayOptions.getOrNull(position) ?: options.getOrNull(position)
            if (option?.goodsCode != null) {
                try {
                    CountdownManager.removeListener(
                        option.type,
                        option.goodsCode,
                        listenerId
                    )
                    val key = "${option.goodsCode}_${option.type}"
                    clearedCount[key] = (clearedCount[key] ?: 0) + 1
                    Timber.tag("RechargeOptionAdapter").d("成功移除监听者: position=$position, listenerId=$listenerId, key=$key")
                } catch (e: Exception) {
                    Timber.tag("RechargeOptionAdapter").e(e, "移除监听者失败: position=$position, listenerId=$listenerId")
                }
            } else {
                Timber.tag("RechargeOptionAdapter").w("无效的option或goodsCode为空: position=$position, listenerId=$listenerId")
            }
        }

        listenerIds.clear()

        Timber.tag("RechargeOptionAdapter").d("清理完成，共清理了 ${clearedCount.values.sum()} 个监听者，涉及 ${clearedCount.size} 个倒计时")
        clearedCount.forEach { (key, count) ->
            Timber.tag("RechargeOptionAdapter").d("倒计时 $key 清理了 $count 个监听者")
        }
    }
}