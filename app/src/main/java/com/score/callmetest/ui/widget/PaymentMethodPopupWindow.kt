package com.score.callmetest.ui.widget

import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.PopupWindow
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.score.callmetest.R
import com.score.callmetest.manager.PaymentMethodManager
import com.score.callmetest.ui.widget.adapter.PaymentMethodAdapter
import com.score.callmetest.util.DisplayUtils
import androidx.core.graphics.drawable.toDrawable

class PaymentMethodPopupWindow(
    private val context: Context,
    private val anchorView: View,
    private val onPaymentMethodSelected: (String) -> Unit
) {
    private lateinit var popupWindow: PopupWindow
    private lateinit var adapter: PaymentMethodAdapter

    fun show() {
        setupPopupWindow()
        showPopup()
    }
    
    fun setOnDismissListener(listener: () -> Unit) {
        popupWindow.setOnDismissListener {
            listener()
        }
    }

    private fun setupPopupWindow() {
        val contentView = LayoutInflater.from(context).inflate(R.layout.popup_payment_method, null)
        
        popupWindow = PopupWindow(
            contentView,
            ViewGroup.LayoutParams.WRAP_CONTENT,
            ViewGroup.LayoutParams.WRAP_CONTENT,
            true
        )

        popupWindow.setBackgroundDrawable(Color.TRANSPARENT.toDrawable())
        popupWindow.isOutsideTouchable = true
        popupWindow.isFocusable = true

        setupRecyclerView(contentView)
    }

    private fun setupRecyclerView(contentView: View) {
        val rvPaymentMethods = contentView.findViewById<RecyclerView>(R.id.rv_payment_methods)
        rvPaymentMethods.layoutManager = LinearLayoutManager(context)

        val items = getPaymentMethods()
        //todo coin store的数据，暂时注释
//        adapter = PaymentMethodAdapter(items) { payChannel ->
//            onPaymentMethodSelected(payChannel)
//            popupWindow.dismiss()
//        }
        rvPaymentMethods.adapter = adapter
    }


        private fun getPaymentMethods(): List<PaymentMethodItem> {
        val availableChannels = PaymentMethodManager.getAvailablePayChannels()
        val defaultMethod = PaymentMethodManager.getDefaultPaymentMethod()
        return availableChannels.map { channel ->
            PaymentMethodItem(
                payChannel = channel,
                displayName = PaymentMethodManager.getPaymentChannelDisplayName(channel),
                iconRes = PaymentMethodManager.getPaymentChannelIcon(channel),
                discount = PaymentMethodManager.getChannelDiscount(channel),
                isSelected = channel == defaultMethod
            )
        }
    }

    private fun showPopup() {
        val location = IntArray(2)
        anchorView.getLocationOnScreen(location)
        val x = location[0] - DisplayUtils.dp2px(55f) // 向左偏移
        val y = location[1] + anchorView.height

        val rootView = (anchorView.context as? android.app.Activity)?.findViewById<View>(android.R.id.content)
        popupWindow.showAtLocation(rootView, Gravity.NO_GRAVITY, x, y)
    }
} 