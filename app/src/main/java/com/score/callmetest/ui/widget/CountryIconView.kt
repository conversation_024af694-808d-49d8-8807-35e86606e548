package com.score.callmetest.ui.widget

import android.content.Context
import android.graphics.Canvas
import android.graphics.Path
import android.util.AttributeSet
import android.view.MotionEvent
import androidx.core.content.ContextCompat
import androidx.core.graphics.withClip
import com.score.callmetest.R

/**
 * 专门用于显示国家图标的自定义View
 * 以图标的宽度为圆的直径，在图标中心进行圆形裁剪
 */
class CountryIconView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : AlphaImageView(context, attrs, defStyleAttr) {

    private var normalAlpha = 1.0f
    private var pressedAlpha = 0.5f
    private var disabledAlpha = 0.2f
    private val clipPath = Path()

    init {
        isClickable = true
        isFocusable = true
        alpha = normalAlpha
        scaleType = ScaleType.CENTER_CROP
    }

    override fun setEnabled(enabled: Boolean) {
        super.setEnabled(enabled)
        alpha = if (enabled) normalAlpha else disabledAlpha
    }

    override fun onTouchEvent(event: MotionEvent?): Boolean {
        when (event?.action) {
            MotionEvent.ACTION_DOWN -> alpha = pressedAlpha
            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> alpha = normalAlpha
        }
        return super.onTouchEvent(event)
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        updateClipPath(w, h)
    }

    private fun updateClipPath(width: Int, height: Int) {
        clipPath.reset()
        
        // 以宽度为直径
        val diameter = width.toFloat()
        val radius = diameter / 2f
        
        // 在图标中心进行圆形裁剪
        val centerX = width / 2f
        val centerY = height / 2f
        
        clipPath.addCircle(centerX, centerY, radius, Path.Direction.CW)
        clipPath.close()
    }

    override fun onDraw(canvas: Canvas) {
        if (!clipPath.isEmpty) {
            canvas.withClip(clipPath) {
                super.onDraw(this)
            }
        } else {
            super.onDraw(canvas)
        }
    }
}
