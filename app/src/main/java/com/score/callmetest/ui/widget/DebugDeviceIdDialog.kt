package com.score.callmetest.ui.widget

import android.app.Dialog
import android.content.Context
import android.graphics.Color
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Switch
import android.widget.TextView
import com.score.callmetest.R
import com.score.callmetest.databinding.DialogDebugDeviceIdBinding
import com.score.callmetest.util.DeviceUtils
import com.score.callmetest.util.ToastUtils
import com.score.callmetest.util.click

/**
 * todo 调试模式设备ID设置弹窗
 */
class DebugDeviceIdDialog(
    context: Context,
    private val onConfirm: (() -> Unit)? = null
) : Dialog(context, androidx.appcompat.R.style.Theme_AppCompat_Dialog) {
    
    private lateinit var binding: DialogDebugDeviceIdBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = DialogDebugDeviceIdBinding.inflate(LayoutInflater.from(context))
        setContentView(binding.root)
        setCancelable(false)

        // 设置全屏/居中
        window?.setLayout(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.WRAP_CONTENT
        )
        
        // 隐藏状态栏
        window?.decorView?.systemUiVisibility = (
                View.SYSTEM_UI_FLAG_FULLSCREEN or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                )
        window?.setBackgroundDrawableResource(R.color.transparent)
        
        // 点击空白区域不可取消
        setCanceledOnTouchOutside(false)

        // 设置内容左右38dp边距
        val paddingPx = (38 * context.resources.displayMetrics.density).toInt()
        binding.root.setPadding(
            paddingPx,
            binding.root.paddingTop,
            paddingPx,
            binding.root.paddingBottom
        )

        initViews()
        initListeners()
    }

    private fun initViews() {
        // 初始化开关状态
        binding.switchDebugDeviceId.isChecked = DeviceUtils.isDebugDeviceIdEnabled()
        
        // 显示设备ID信息
        updateDeviceIdDisplay()
        
        // 根据开关状态控制生成按钮的可用性
        updateGenerateButtonState()
    }

    private fun initListeners() {
        // 开关监听
        binding.switchDebugDeviceId.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked && DeviceUtils.getDebugDeviceId().isEmpty()) {
                // 如果开启但没有调试设备ID，自动生成一个
                DeviceUtils.generateNewDebugDeviceId()
                updateDeviceIdDisplay()
            }
            updateGenerateButtonState()
        }

        // 生成新设备ID按钮
        binding.btnGenerateNewId.click {
            if (binding.switchDebugDeviceId.isChecked) {
                val newId = DeviceUtils.generateNewDebugDeviceId()
                updateDeviceIdDisplay()
                ToastUtils.showToast("已生成新的设备ID: $newId")
            } else {
                ToastUtils.showToast("请先开启随机设备ID开关")
            }
        }

        // 确认按钮
        binding.btnConfirm.click {
            // 保存设置
            DeviceUtils.setDebugDeviceIdEnabled(binding.switchDebugDeviceId.isChecked)
            
            val message = if (binding.switchDebugDeviceId.isChecked) {
                "已启用调试模式设备ID: ${DeviceUtils.getDebugDeviceId()}"
            } else {
                "已关闭调试模式，使用真实设备ID"
            }
            ToastUtils.showToast(message)
            
            onConfirm?.invoke()
            dismiss()
        }

        // 取消按钮
        binding.btnCancel.click {
            dismiss()
        }
    }

    private fun updateDeviceIdDisplay() {
        // 显示真实设备ID
        binding.tvRealDeviceId.text = DeviceUtils.getRealAndroidId()
        
        // 显示当前使用的设备ID
        val currentId = if (binding.switchDebugDeviceId.isChecked && DeviceUtils.getDebugDeviceId().isNotEmpty()) {
            "${DeviceUtils.getDebugDeviceId()} (调试模式)"
        } else {
            "${DeviceUtils.getRealAndroidId()} (真实设备)"
        }
        binding.tvCurrentDeviceId.text = currentId
    }

    private fun updateGenerateButtonState() {
        val isEnabled = binding.switchDebugDeviceId.isChecked
        binding.btnGenerateNewId.alpha = if (isEnabled) 1.0f else 0.5f
        binding.btnGenerateNewId.isEnabled = isEnabled
    }
}
