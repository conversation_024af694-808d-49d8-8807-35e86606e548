package com.score.callmetest.ui.message

import android.os.Handler
import android.os.Looper
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.score.callmetest.CallStatus
import com.score.callmetest.Constant
import com.score.callmetest.manager.GlobalManager
import com.score.callmetest.manager.StrategyManager
import com.score.callmetest.manager.UserInfoManager
import com.score.callmetest.manager.UserInfoManager.getUserInfo
import com.score.callmetest.network.BroadcasterModel
import com.score.callmetest.network.GetUserListOnlineStatusPostV2Request
import com.score.callmetest.network.NetworkResult
import com.score.callmetest.network.RetrofitUtils
import com.score.callmetest.ui.base.BaseViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Runnable
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import kotlin.collections.set

class MessageViewModel : BaseViewModel() {

    // <editor-folder desc="更新在线状态">

    /**
     * 查询在线状态---添加到队列
     */
    fun checkOnlineStatus(providedList: List<String>){
        if(providedList.isEmpty()) return
        viewModelScope.launch {
            // 过滤空字符串并去重
            val userIds = providedList.filter {
                // 非空 && 非官方号 && 非机器人
                it.isNotBlank() && !StrategyManager.isTopOfficialUser(it)
                        && Constant.ROBOt_ID != it
            }.distinct()

            UserInfoManager.addUserToRefreshQueue(*userIds.toTypedArray())
        }
    }

    // </editor-folder>

} 