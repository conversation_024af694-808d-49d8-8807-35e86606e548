package com.score.callmetest.ui.widget

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import androidx.appcompat.widget.AppCompatImageView
import com.opensource.svgaplayer.SVGAImageView

open class AlphaSVGAImageView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : SVGAImageView(context, attrs, defStyleAttr) {

    private var normalAlpha = 1.0f
    private var pressedAlpha = 0.5f

    init {
        isClickable = true
        isFocusable = true
        alpha = normalAlpha
    }

    override fun onTouchEvent(event: MotionEvent?): <PERSON><PERSON><PERSON> {
        when (event?.action) {
            MotionEvent.ACTION_DOWN -> alpha = pressedAlpha
            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> alpha = normalAlpha
        }
        return super.onTouchEvent(event)
    }
} 