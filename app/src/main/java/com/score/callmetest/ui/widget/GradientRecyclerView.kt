package com.score.callmetest.ui.widget

import android.content.Context
import android.graphics.Canvas
import android.graphics.LinearGradient
import android.graphics.Paint
import android.graphics.Shader
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import android.view.inputmethod.InputMethodManager
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.score.callmetest.R
import com.score.callmetest.util.DisplayUtils

/**
 * 带渐变效果的 RecyclerView
 * 支持最大高度限制和 Item 透明度渐变效果
 *
 * 特性：
 * 1. 强制最大高度限制
 * 2. Item 透明度渐变：从底部0%透明度到顶部18%透明度
 * 3. 线性递增透明度
 * 4. 支持滚动监听
 */
class GradientRecyclerView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : RecyclerView(context, attrs, defStyleAttr) {

    companion object {
        private const val DEFAULT_MAX_HEIGHT_DP = 200f
        private const val MAX_ALPHA = 0.82f // 最大透明度（顶部Item为82%）
        private const val MIN_ALPHA = 0.0f // 最小透明度（底部Item为0%）
    }

    private var maxHeight: Int = DisplayUtils.dp2px(DEFAULT_MAX_HEIGHT_DP)
    
    // 滚动监听器列表
    private val scrollListeners = mutableListOf<OnScrollListener>()

    // 数据观察器，用于在数据变化时更新透明度
    private val dataObserver = object : AdapterDataObserver() {
        override fun onChanged() {
            super.onChanged()
            updateItemAlpha()
        }

        override fun onItemRangeInserted(positionStart: Int, itemCount: Int) {
            super.onItemRangeInserted(positionStart, itemCount)
            updateItemAlpha()
        }

        override fun onItemRangeRemoved(positionStart: Int, itemCount: Int) {
            super.onItemRangeRemoved(positionStart, itemCount)
            updateItemAlpha()
        }

        override fun onItemRangeMoved(fromPosition: Int, toPosition: Int, itemCount: Int) {
            super.onItemRangeMoved(fromPosition, toPosition, itemCount)
            updateItemAlpha()
        }
    }

    // 输入法管理器
    private val inputMethodManager by lazy {
        context.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
    }
    
    init {
        // 设置默认的滚动监听器来更新Item透明度
        addOnScrollListener(object : OnScrollListener() {
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)
                updateItemAlpha() // 更新Item透明度

                // 通知其他监听器
                scrollListeners.forEach { listener ->
                    listener.onScrolled(recyclerView, dx, dy)
                }
            }

            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                super.onScrollStateChanged(recyclerView, newState)

                // 通知其他监听器
                scrollListeners.forEach { listener ->
                    listener.onScrollStateChanged(recyclerView, newState)
                }
            }
        })

        // 设置点击监听器，用于隐藏输入法
        setOnTouchListener { _, event ->
            if (event.action == MotionEvent.ACTION_DOWN) {
                // 检查是否有输入法显示
                if (isInputMethodVisible()) {
                    // 隐藏输入法
                    hideInputMethod()
                    return@setOnTouchListener true
                }
            }
            false
        }
    }

    override fun setAdapter(adapter: Adapter<*>?) {
        // 移除旧适配器的观察器
        getAdapter()?.unregisterAdapterDataObserver(dataObserver)

        // 设置新适配器
        super.setAdapter(adapter)

        // 为新适配器注册观察器
        adapter?.registerAdapterDataObserver(dataObserver)

        // 更新透明度
        updateItemAlpha()
    }

    /**
     * 设置最大高度（dp）
     */
    fun setMaxHeightDp(heightDp: Float) {
        maxHeight = DisplayUtils.dp2px(heightDp)
        requestLayout()
    }

    /**
     * 添加滚动监听器
     */
    fun addScrollListener(listener: OnScrollListener) {
        scrollListeners.add(listener)
    }

    /**
     * 移除滚动监听器
     */
    fun removeScrollListener(listener: OnScrollListener) {
        scrollListeners.remove(listener)
    }

    override fun onMeasure(widthSpec: Int, heightSpec: Int) {
        // 强制限制最大高度
        val newHeightSpec = if (MeasureSpec.getSize(heightSpec) > maxHeight) {
            MeasureSpec.makeMeasureSpec(maxHeight, MeasureSpec.AT_MOST)
        } else {
            heightSpec
        }
        super.onMeasure(widthSpec, newHeightSpec)
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        updateItemAlpha()
    }

    /**
     * 更新Item透明度
     * 只对当前200dp内可见的Item设置透明度
     * 顶部Item透明度为82%，底部Item透明度为0%，线性递减
     * 线程安全版本
     */
    private fun updateItemAlpha() {
        post {
            val layoutManager = layoutManager ?: return@post
            val visibleChildCount = childCount

            if (visibleChildCount == 0) return@post

            // 获取可见Item的位置范围
            val visibleItems = mutableListOf<Pair<View, Int>>()
            for (i in 0 until visibleChildCount) {
                val child = getChildAt(i) ?: continue
                val position = getChildAdapterPosition(child)
                if (position != RecyclerView.NO_POSITION) {
                    visibleItems.add(child to position)
                }
            }

            if (visibleItems.isEmpty()) return@post

            // 按位置排序，确保顺序正确
            visibleItems.sortBy { it.second }

            val visibleItemCount = visibleItems.size

            // 为每个可见Item设置透明度
            visibleItems.forEachIndexed { index, (child, position) ->
                val alphaValue = if (visibleItemCount == 1) {
                    MIN_ALPHA // 只有一个可见Item时，透明度为0%
                } else {
                    // 线性插值：index越小（越靠顶部），透明度越高
                    // index=0（最顶部）-> 82%透明度，index=visibleItemCount-1（最底部）-> 0%透明度
                    val ratio = index.toFloat() / (visibleItemCount - 1).toFloat()
                    MAX_ALPHA * (1f - ratio) + MIN_ALPHA * ratio
                }

                // 设置Item的透明度（线程安全）
                child.alpha = 1f - alphaValue // alphaValue是透明度，View.alpha是不透明度
            }
        }
    }

    /**
     * 检查是否可以向上滚动
     */
    fun canScrollUp(): Boolean {
        return canScrollVertically(-1)
    }

    /**
     * 检查是否可以向下滚动
     */
    fun canScrollDown(): Boolean {
        return canScrollVertically(1)
    }

    /**
     * 滚动到底部
     * 确保在布局完成后执行滚动，避免ItemDecoration计算错误
     */
    fun scrollToBottom() {
        adapter?.let { adapter ->
            if (adapter.itemCount > 0) {
                // 使用post确保在布局完成后滚动
                post {
                    // 再次检查adapter和itemCount，确保数据仍然有效
                    if (adapter.itemCount > 0) {
                        smoothScrollToPosition(adapter.itemCount - 1)
                        // 滚动完成后更新透明度
                        post { updateItemAlpha() }
                    }
                }
            }
        }
    }

    /**
     * 立即滚动到底部（无动画）
     * 确保在布局完成后执行滚动，避免ItemDecoration计算错误
     */
    fun scrollToBottomImmediately() {
        adapter?.let { adapter ->
            if (adapter.itemCount > 0) {
                // 使用post确保在布局完成后滚动
                post {
                    // 再次检查adapter和itemCount，确保数据仍然有效
                    if (adapter.itemCount > 0) {
                        scrollToPosition(adapter.itemCount - 1)
                        // 滚动完成后更新透明度
                        post { updateItemAlpha() }
                    }
                }
            }
        }
    }

    /**
     * 检查是否滚动到底部
     */
    fun isScrolledToBottom(): Boolean {
        return !canScrollDown()
    }

    /**
     * 检查是否滚动到顶部
     */
    fun isScrolledToTop(): Boolean {
        return !canScrollUp()
    }

    /**
     * 检查输入法是否可见
     */
    private fun isInputMethodVisible(): Boolean {
        return inputMethodManager.isActive
    }

    /**
     * 隐藏输入法
     */
    private fun hideInputMethod() {
        inputMethodManager.hideSoftInputFromWindow(windowToken, 0)
    }
}
