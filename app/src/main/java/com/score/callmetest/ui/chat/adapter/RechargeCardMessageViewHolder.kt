package com.score.callmetest.ui.chat.adapter

import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.google.gson.Gson
import com.score.callmetest.R
import com.score.callmetest.databinding.ItemChatMessageRechargeCardBinding
import com.score.callmetest.entity.ChatMessageEntity
import com.score.callmetest.entity.ImCardEntity
import com.score.callmetest.entity.isRobot
import com.score.callmetest.util.ClickUtils
import com.score.callmetest.util.DisplayUtils
import com.score.callmetest.util.GlideUtils
import timber.log.Timber

/**
 * 充值卡片消息ViewHolder
 * 用于显示包含充值卡片的消息
 */
internal class RechargeCardMessageViewHolder(
    private val binding: ItemChatMessageRechargeCardBinding,
    private val mChatMessageListeners: ChatAdapterListeners
) : RecyclerView.ViewHolder(binding.root), MessageHolder {

    private var mCurrentMessage: ChatMessageEntity? = null

    init {
        // 消息容器点击
        ClickUtils.setOnGlobalDebounceClickListener(binding.messageContainer) {
            mCurrentMessage?.let { message ->
                mChatMessageListeners.mOnRechargeCardClickListener?.invoke(message)
            }
        }

        // 消息文本点击
        ClickUtils.setOnGlobalDebounceClickListener(binding.tvMessage) {
            mCurrentMessage?.let { message ->
                mChatMessageListeners.mOnRechargeCardClickListener?.invoke(message)
            }
        }

        // 查看详情点击
        ClickUtils.setOnGlobalDebounceClickListener(binding.tvClickDetails) {
            mCurrentMessage?.let { message ->
                mChatMessageListeners.mOnRechargeCardClickListener?.invoke(message)
            }
        }

        // 图片点击
        ClickUtils.setOnGlobalDebounceClickListener(binding.ivRechargeCard) {
            mCurrentMessage?.let { message ->
                mChatMessageListeners.mOnRechargeCardClickListener?.invoke(message)
            }
        }


        // 头像
        /*ClickUtils.setOnGlobalDebounceClickListener(binding.ivAvatar) {
            mCurrentMessage?.let { message ->
                mChatMessageListeners.mOnAvatarClickListener?.invoke(message)
            }
        }*/

    }

    /**
     * 绑定消息数据
     * @param message 消息实体
     */
    override fun bind(message: ChatMessageEntity) {
        mCurrentMessage = message

        try {

            if (message.isRobot()) {
                // 客服账号
                GlideUtils.load(
                    view = binding.ivAvatar,
                    url = R.drawable.customer_service,
                    placeholder = R.drawable.customer_service,
                    isCircle = true
                )
            } else {
                // 加载头像
                GlideUtils.load(
                    view = binding.ivAvatar,
                    url = message.senderAvatar,
                    placeholder = R.drawable.placeholder,
                    error = R.drawable.placeholder,
                    isCircle = true
                )
            }

            val cardEntity = Gson().fromJson(message.content, ImCardEntity::class.java)

            // 设置消息文本内容
            binding.tvMessage.text = cardEntity.tppTitle
            binding.tvMessage.isTouchable = false

            // 设置时间（如果需要显示）
          /*  if (binding.tvTime.visibility == View.VISIBLE) {
                val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault())
                binding.tvTime.text = dateFormat.format(Date(message.timestamp))
            }*/

            if(cardEntity.tppImageUrl.isNullOrBlank()){
                binding.ivRechargeCard.isVisible = false
                return
            }
            // 设置充值卡片图片
            GlideUtils.load(
                view =binding.ivRechargeCard,
                url = cardEntity.tppImageUrl,
                placeholder = R.drawable.image_placeholder,
                error = R.drawable.image_placeholder,
                radius = DisplayUtils.dp2px(20f))
            binding.ivRechargeCard.isVisible = true

        } catch (e: Exception) {
            Timber.e(e, "Error binding recharge card message")
        }
    }

}
