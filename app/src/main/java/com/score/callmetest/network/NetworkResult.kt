package com.score.callmetest.network

import com.score.callmetest.CallmeApplication
import com.score.callmetest.Constant
import com.score.callmetest.R
import com.score.callmetest.util.CustomUtils
import com.score.callmetest.util.SharePreferenceUtil
import com.score.callmetest.util.ToastUtils
import retrofit2.HttpException
import java.io.IOException
import java.net.ConnectException
import java.net.SocketTimeoutException

sealed class NetworkResult<out T> {
    data class Success<T>(val data: T?,val originResp: BaseResponse<T>?) : NetworkResult<T>()
    data class Error(
        val message: String,
        val code: Int? = -1,
        val e: Exception? = null,
        val isNetworkError: Boolean = false
    ) : NetworkResult<Nothing>()
}

// 使用lazy延迟加载字符串资源
private val networkConnectionError by lazy { CallmeApplication.context.getString(R.string.error_network_connection) }
private val requestTimeoutError by lazy { CallmeApplication.context.getString(R.string.error_request_timeout) }
private val serverUnreachableError by lazy { CallmeApplication.context.getString(R.string.error_server_unreachable) }
private val networkError by lazy { CallmeApplication.context.getString(R.string.error_network) }
private val unknownError by lazy { CallmeApplication.context.getString(R.string.error_unknown) }
private val emptyResponseError by lazy { CallmeApplication.context.getString(R.string.error_empty_response) }
private val serverError by lazy { CallmeApplication.context.getString(R.string.error_server) }

suspend fun <T> safeApiCall(apiCall: suspend () -> BaseResponse<T>): NetworkResult<T> {
    return try {
        val response = apiCall()
        /**
         * 10010301 -> "未提供token"
         * 10010302 -> "token格式错误"
         * 100103 -> "无效的token"
         * 10010303 -> "token已过期"
         * 10010304 -> "其他设备登录"
         * 10010003 -> "用户被禁用"
         *
         * 遇上上述错误码，客户端需要处理退出登录并清理状态
         */
        var logoutMsg: String? = null
        val result = when (response.code) {
            0 -> {
                return NetworkResult.Success(response.data, response)
                // 不能确保有的接口不需要data，只需要code，如果还是ERROR就有问题，所以这里直接丢出去，外面使用的时候判空。
    //            response.data?.let { NetworkResult.Success(it) }
    //                ?: NetworkResult.Error(emptyResponseError)
            }
            100103,
            10010301,
            10010302,
            10010303 -> {
                // token过期，需要退出登录
                val token = SharePreferenceUtil.getString(Constant.TOKEN_KEY, "")
                logoutMsg =    if(token?.isNotEmpty() == true){
                    CallmeApplication.context.getString(R.string.token_is_expire)
                }else null
            }
            10010304 -> {
                // 其他设备登录
                logoutMsg = CallmeApplication.context.getString(R.string.other_device_login)
            }
            10010003 -> {
                // 用户被禁用
                logoutMsg = CallmeApplication.context.getString(R.string.user_is_disabled)
            }
            else -> {
                logoutMsg = null
            }
        }
        logoutMsg?.let {
            ToastUtils.showShortToast(it)
            CustomUtils.logoutAndClearData(context = CallmeApplication.context)
        }
        NetworkResult.Error(
            message = response.msg ?: unknownError,
            e = null,
            code = response.code
        )

    } catch (e: NoNetworkException) {
        NetworkResult.Error(
            message = networkConnectionError,
            e = e,
            isNetworkError = true
        )
    } catch (e: SocketTimeoutException) {
        NetworkResult.Error(
            message = requestTimeoutError,
            e = e,
            isNetworkError = true
        )
    } catch (e: ConnectException) {
        NetworkResult.Error(
            message = serverUnreachableError,
            e = e,
            isNetworkError = true
        )
    } catch (e: HttpException) {
        NetworkResult.Error(
            message = serverError.format(e.message()),
            e = e,
            code = e.code()
        )
    } catch (e: IOException) {
        NetworkResult.Error(
            message = networkError,
            e = e,
            isNetworkError = true
        )
    } catch (e: Exception) {
        NetworkResult.Error(
            message = unknownError.format(e.message ?: ""),
            e = e,
            isNetworkError = false
        )
    }
}