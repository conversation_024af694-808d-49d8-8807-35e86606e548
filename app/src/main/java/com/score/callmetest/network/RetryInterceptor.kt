package com.score.callmetest.network

import okhttp3.Interceptor
import okhttp3.Response
import java.io.IOException

/**
 * 重试拦截器--重试次数2次
 * <AUTHOR>
 * @date 2025/08/02
 * @constructor 创建[RetryInterceptor]
 */
class RetryInterceptor : Interceptor {
    private val maxRetries = 2

    override fun intercept(chain: Interceptor.Chain): Response {
        val request = chain.request()
        var response: Response? = null
        var exception: IOException? = null

        for (i in 0..maxRetries) {
            try {
                response = chain.proceed(request)
                if (response.isSuccessful || response.code < 500) {
                    return response
                }
                response.close()
            } catch (e: IOException) {
                exception = e
                if (i == maxRetries) {
                    break
                }
            }

            // 指数退避重试
            val waitTime = (1000 * Math.pow(2.0, i.toDouble())).toLong()
            Thread.sleep(waitTime)
        }

        throw exception ?: IOException("Request failed after $maxRetries retries")
    }
}