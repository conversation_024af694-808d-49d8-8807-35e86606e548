package com.score.callmetest.network

import kotlinx.serialization.Serializable

/**
 * 日志上报请求模型
 */
@Serializable
data class LogReportRequest(
    val list: List<LogReportData>,
    val log_type: String = "event",
    val subtype: String,
    val behavior: String = "event",
    val utm_source: String = "",
    val android_id: String,
    val user_id: String,
    val pkg: String,
    val ver: String,
    val platform: String = "Android",
    val platform_ver: Int,
    val model: String,
    val lan_id: String = "",
    val sec_id: Int = 0,
    val sys_lan: String,
    val country: String = "",
    val is_in_bg: Boolean = false,
    val is_anchor: Boolean = false,
    val client_type: String = "common",
    val bizver: String = "",
    val tm: Long
)

/**
 * 日志数据模型
 */
@Serializable
data class LogReportData(
    val channelName: String? = null,
    val action: String? = null,
    val ext: String? = null,
    val ext2: String? = null,
    val tm: Long? = null,
    // 购买事件相关字段 - 按照规范ARG_*命名
    val event: String? = null,        // ARG_EVENT
    val code: String? = null,         // ARG_CODE
    val uuid: String? = null,         // ARG_UUID
    val orderId: String? = null,      // ARG_ORDER_ID
    val durationTime: Long? = null,   // ARG_DURATION_TIME
    val elapsedTime: Long? = null,    // ARG_ELAPSED_TIME
    val result: String? = null,       // ARG_RESULT
    val resultCode: Int? = null       // ARG_RESULT_CODE
)

/**
 * 通话流程行为打点Action枚举
 */
object LiveCallAction {
    // 呼叫界面
    const val ENTER = "enter"
    const val HANGUP = "hangup"
    const val CALL_RESP = "call_resp"
    const val EXIT = "exit"
    
    // 通话界面
    const val PERMISSION_RESULT = "permission_result"
    const val JOIN_SUCCESS = "join_success"
    const val USER_JOIN = "user_join"
}

/**
 * 通话流程行为打点Ext枚举
 */
object LiveCallExt {
    const val CALLING = "calling"
    const val ON_CALL = "oncall"
    const val CHATTING = "chatting"
    const val OK = "ok"
    const val ERROR = "error"
    const val REFUSE = "refuse"
    const val PRESS = "press"
}

/**
 * 通话流程行为打点Ext2枚举
 */
object LiveCallExt2 {
    const val TIME_OUT = "time_out"
    const val HANGUP_BUTTON = "hangup_button"
}

/**
 * 购买事件类型枚举
 * 按照规范定义的事件类型
 */
object PurchaseEventType {
    const val CREATE_ORDER = "create_order"                    // 创建订单 调用创建订单接口前打点
    const val CREATE_ORDER_RESPONSE = "create_order_resp"      // 创建订单回调 无论回调成功与否都需要上传相应的响应
    const val REVIEW_ORDER = "review_order"                    // 请求库存列表
    const val REVIEW_ORDER_RESPONSE = "review_order_resp"      // 请求库存列表回调
    const val LAUNCH_PAY = "launch_pay"                        // 调起支付 调起相应支付前打点
    const val LAUNCH_PAY_RESPONSE = "launch_pay_resp"          // 支付结果回调 onPurchaseUpdated
    const val VERIFY_ORDER = "verify_order"                    // 校验订单 调用校验订单前打点
    const val VERIFY_ORDER_RESPONSE = "verify_order_resp"      // 校验订单回调
    const val CONSUME_ORDER = "consume_order"                  // 消费订单 普通商品使用 调用消费接口前打点
    const val CONSUME_ORDER_RESPONSE = "consume_order_resp"    // 消费订单回调
    const val ACKNOWLEDGED_ORDER = "acknowledged_order"        // 确认订单 订阅商品使用 调用确认接口前打点
    const val ACKNOWLEDGED_ORDER_RESPONSE = "acknowledged_order_resp" // 确认订单回调
} 