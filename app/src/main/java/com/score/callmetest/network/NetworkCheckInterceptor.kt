package com.score.callmetest.network

import com.score.callmetest.CallmeApplication
import com.score.callmetest.util.NetworkUtils
import okhttp3.Interceptor
import okhttp3.Response
import timber.log.Timber
import java.io.IOException
import java.net.SocketTimeoutException


/**
 *  网络检查
 */
class NetworkCheckInterceptor: Interceptor {

    override fun intercept(chain: Interceptor.Chain): Response {
        if(!NetworkUtils.isConnected(CallmeApplication.context)){
            throw NoNetworkException("No internet connection")
        }
        return chain.proceed(chain.request())
    }
}

// 自定义异常
class NoNetworkException(message: String) : IOException(message)