package com.score.callmetest.network

import com.score.callmetest.entity.InstallReferrerEntity
import retrofit2.http.POST
import timber.log.Timber

/**
 * 数据仓库类，封装所有网络请求
 * 使用safeApiCall进行统一的错误处理和网络异常捕获
 *
 * @param apiService 主要API服务
 * @param logApiService 日志上报专用API服务
 */
class DataRepository(
    private val apiService: ApiService,
    private val logApiService: ApiService,
) {

    // <editor-fold desc="认证相关接口">

    /**
     * 用户登录
     * @param request 登录请求参数
     * @return 登录结果
     */
    suspend fun login(request: LoginRequest): NetworkResult<LoginData> {
        return safeApiCall { apiService.login(request) }
    }

    /**
     * 校验Token有效性
     * @param token 用户token
     * @return 校验结果
     */
    suspend fun isValidToken(token: String): NetworkResult<Boolean> {
        return safeApiCall { apiService.isValidToken(TokenBody(token)) }
    }

    /**
     * 退出登录
     * @return 退出结果
     */
    suspend fun logout(): NetworkResult<Boolean> {
        return safeApiCall { apiService.logout() }
    }

    // </editor-fold>

    // <editor-fold desc="配置相关接口">

    /**
     * 获取应用配置
     * @param ver 版本号，默认为0
     * @return 应用配置数据
     */
    suspend fun getAppConfigPostV2(ver: Int = 0): NetworkResult<AppConfigData> {
        return safeApiCall { apiService.getAppConfigPostV2(ConfigPostV2(ver)) }
    }

    /**
     * 获取策略配置V2
     * @return 策略配置数据
     */
    suspend fun getStrategyPostV2(): NetworkResult<StrategyConfig> {
        return safeApiCall { apiService.getStrategyPostV2() }
    }

    // </editor-fold>

    // <editor-fold desc="用户相关接口">

    /**
     * 获取用户信息
     * @param request 获取用户信息请求参数
     * @return 用户信息
     */
    suspend fun getUserInfoPostV2(request: GetUserInfoPostV2Request): NetworkResult<UserInfo> {
        return safeApiCall { apiService.getUserInfoPostV2(request) }
    }

    /**
     * 获取用户在线状态
     * @param request 获取用户在线状态请求参数
     * @return 用户在线状态
     */
    suspend fun getUserOnlineStatusPostV2(request: GetUserOnlineStatusPostV2Request): NetworkResult<String> {
        return safeApiCall { apiService.getUserOnlineStatusPostV2(request) }
    }

    /**
     * 批量获取用户在线状态
     * @param request 批量获取用户在线状态请求参数
     * @return 用户在线状态映射表
     */
    suspend fun getUserListOnlineStatusPostV2(request: GetUserListOnlineStatusPostV2Request): NetworkResult<Map<String, String>> {
        return safeApiCall { apiService.getUserListOnlineStatusPostV2(request) }
    }

    /**
     * 获取用户金币
     * @return 用户金币数量
     */
    suspend fun getUserCoins(): NetworkResult<Int> {
        return safeApiCall { apiService.getUserCoins() }
    }

    /**
     * 获取用户头像和昵称
     * @param userId 用户ID
     * @return 用户资料信息
     */
    suspend fun getUsernameAndAvatar(userId: String): NetworkResult<UserProfileResponse> {
        return safeApiCall { apiService.getUsernameAndAvatar(userId) }
    }

    /**
     * 更新Agora UID
     * @param request 更新Agora UID请求参数
     * @return 更新结果
     */
    suspend fun updateAgoraUid(request: UpdateAgoraUidRequest): NetworkResult<Boolean> {
        return safeApiCall { apiService.updateAgoraUid(request) }
    }

    /**
     * 用户心跳上报
     * @return 上报结果
     */
    suspend fun reportUserActive(): NetworkResult<Boolean> {
        return safeApiCall { apiService.reportUserActive() }
    }

    /**
     * 用户前后台切换上报
     * @param request 模式切换请求参数
     * @return 上报结果
     */
    suspend fun switchUserMode(request: ModeSwitchRequest): NetworkResult<Boolean> {
        return safeApiCall { apiService.switchUserMode(request) }
    }

    /**
     * 保存用户所有信息
     * @param request 保存用户信息请求参数
     * @return 保存结果
     */
    suspend fun saveUserInfo(request: SaveUserInfoRequest): NetworkResult<Boolean> {
        return safeApiCall { apiService.saveUserInfo(request) }
    }

    /**
     * 保存用户基本信息
     * @param request 保存基本信息请求参数
     * @return 保存结果
     */
    suspend fun saveBasicInfo(request: SaveBasicInfoRequest): NetworkResult<Boolean> {
        return safeApiCall { apiService.saveBasicInfo(request) }
    }

    /**
     * 获取语言选择列表
     * @return 语言列表
     */
    suspend fun getLanguageSelections(): NetworkResult<List<String>> {
        return safeApiCall { apiService.getLanguageSelections() }
    }

    /**
     * 账号删除
     * @return 删除结果
     */
    suspend fun deleteAccount(): NetworkResult<Boolean> {
        return safeApiCall { apiService.deleteAccount() }
    }

    // </editor-fold>

    // <editor-fold desc="通话相关接口">

    /**
     * 创建通话频道
     * @param request 创建频道请求参数
     * @return 创建频道响应
     */
    suspend fun createChannel(request: CreateChannelRequest): NetworkResult<CreateChannelResponse> {
        return safeApiCall { apiService.createChannel(request) }
    }

    /**
     * 挂断通话
     * @param request 挂断请求参数
     * @return 挂断结果
     */
    suspend fun hangUp(request: HangUpRequest): NetworkResult<Boolean> {
        return safeApiCall { apiService.hangUp(request) }
    }

    /**
     * 接听通话
     * @param request 接听请求参数
     * @return 接听结果
     */
    suspend fun pickUp(request: PickUpRequest): NetworkResult<Boolean> {
        return safeApiCall { apiService.pickUp(request) }
    }

    /**
     * 查询用户最近通话记录
     * @param request 查询通话记录请求参数
     * @return 通话记录响应
     */
    suspend fun getLatelyRecord(request: LatelyRecordRequest): NetworkResult<LatelyRecordResponseObj> {
        return safeApiCall { apiService.latelyRecord(request) }
    }

    /**
     * 加入频道
     * @param request 加入频道请求参数
     * @return 加入结果
     */
    suspend fun joinChannel(request: JoinChannelRequest): NetworkResult<Boolean> {
        return safeApiCall { apiService.joinChannel(request) }
    }

    /**
     * 查询通话结果
     * @param request 通话结果请求参数
     * @return 通话结果
     */
    suspend fun getCallResult(request: CallResultRequest): NetworkResult<CallResult> {
        return safeApiCall { apiService.getCallResult(request) }
    }

    /**
     * 获取通话时长
     * @param request 通话时长请求参数
     * @return 通话时长（秒）
     */
    suspend fun getCallDuration(request: CallDurationRequest): NetworkResult<Int> {
        return safeApiCall { apiService.getCallDuration(request) }
    }

    /**
     * FlashChat 快速匹配
     * @param request 快速匹配请求参数
     * @return 匹配结果
     */
    suspend fun flashChat(request: FlashChatRequest): NetworkResult<FlashChatResponse> {
        return safeApiCall { apiService.flashChat(request) }
    }

    /**
     * FlashChat 取消匹配
     */
    suspend fun matchCancel(request: MatchCancelReq): NetworkResult<Boolean> {
        return safeApiCall { apiService.matchCancel(request) }
    }


    /**
     * 轮播话术
     */
    suspend fun configContentSearch(request: ConfigContentSearchReq): NetworkResult<List<String>> {
        return safeApiCall { apiService.configContentSearch(request) }
    }

    // </editor-fold>

    // <editor-fold desc="充值相关接口">

    /**
     * 创建充值订单
     * @param request 创建充值订单请求参数
     * @return 充值订单响应
     */
    suspend fun createRechargeOrder(request: CreateRechargeRequest): NetworkResult<CreateRechargeResponse> {
        return safeApiCall { apiService.createRechargeOrder(request) }
    }

    /**
     * GooglePlay订单支付校验
     * @param request GooglePlay支付校验请求参数
     * @return 校验结果
     */
    suspend fun verifyGooglePlayPayment(request: GooglePlayPaymentVerifyRequest): NetworkResult<Boolean> {
        return safeApiCall { apiService.verifyGooglePlayPayment(request) }
    }

    /**
     * 查询充值结果
     * @param request 充值结果查询请求参数
     * @return 充值结果响应
     */
    suspend fun searchRechargeResult(request: RechargeSearchRequest): NetworkResult<RechargeSearchResponse> {
        return safeApiCall { apiService.searchRechargeResult(request) }
    }

    /**
     * 检查主播邀请充值链接
     * @param invitationId 邀请ID
     * @return 检查结果
     */
    suspend fun checkBroadcasterInvitation(invitationId: String): NetworkResult<Boolean> {
        return safeApiCall { apiService.checkBroadcasterInvitation(CheckBroadcasterInvitationRequest(invitationId)) }
    }

    /**
     * 查询支付渠道
     * @return 支付渠道响应
     */
    suspend fun getChannelList(): NetworkResult<PayChannelResponse> {
        return safeApiCall { apiService.getChannelList() }
    }

    // </editor-fold>

    // <editor-fold desc="商品相关接口">

    /**
     * 查询商品列表
     * @param request 商品列表查询请求参数
     * @return 商品信息列表
     */
    suspend fun queryGoodsListPostV2(request: QueryGoodsListRequest): NetworkResult<List<GoodsInfo>> {
        return safeApiCall { apiService.queryGoodsListPostV2(request) }
    }

    /**
     * 搜索主播邀请链接的商品列表
     * @param request 主播邀请商品请求参数
     * @return 商品信息列表
     */
    suspend fun getBroadcasterInvitationGoods(request: BroadcasterInvitationGoodsRequest): NetworkResult<List<GoodsInfo>> {
        return safeApiCall { apiService.getBroadcasterInvitationGoods(request) }
    }

    /**
     * 获取新人促销商品
     * @param request 促销商品请求参数
     * @return 促销商品信息
     */
    suspend fun getPromotionGoods(request: PromotionGoodsRequest): NetworkResult<GoodsInfo> {
        return safeApiCall { apiService.getPromotionGoods(request) }
    }

    /**
     * 获取活动商品信息
     * @param request 特价商品请求参数
     * @return 特价商品响应列表
     */
    suspend fun getLastSpecialOfferV2(request: SpecialOfferRequest): NetworkResult<List<LastSpecialOfferResponse>> {
        return safeApiCall { apiService.getLastSpecialOfferV2(request) }
    }

    /**
     * 搜索订阅商品
     * @param request 订阅搜索请求参数
     * @return 订阅商品列表
     */
    suspend fun searchSubscriptions(request: SubscriptionSearchRequest): NetworkResult<List<SubscriptionItemResponse>> {
        return safeApiCall { apiService.searchSubscriptions(request) }
    }

    // </editor-fold>

    // <editor-fold desc="主播相关接口">

    /**
     * 搜索主播墙列表
     * @param request 搜索主播请求参数
     * @return 主播模型列表
     */
    suspend fun searchBroadcasters(request: SearchBroadcastersRequest): NetworkResult<List<BroadcasterModel>> {
        return safeApiCall { apiService.searchBroadcasters(request) }
    }

    /**
     * 获取主播额外信息
     * @param request 获取主播额外信息请求参数
     * @return 主播额外信息
     */
    suspend fun getBroadcasterExtraInfoPostV2(request: GetBroadcasterExtraInfoRequest): NetworkResult<BroadcasterExtraInfo> {
        return safeApiCall { apiService.getBroadcasterExtraInfoPostV2(request) }
    }

    /**
     * 评价主播
     * @param request 主播评价请求参数
     * @return 评价结果
     */
    suspend fun evaluateBroadcaster(request: BroadcasterEvaluateRequest): NetworkResult<Boolean> {
        return safeApiCall { apiService.evaluateBroadcaster(request) }
    }

    /**
     * 主播排行榜列表查询
     * @param request 主播排行榜搜索请求参数
     * @return 主播排行榜响应
     */
    suspend fun searchBroadcasterRank(request: BroadcasterRankSearchRequest): NetworkResult<BroadcasterRankResponse> {
        return safeApiCall { apiService.searchBroadcasterRank(request) }
    }

    /**
     * 获取一个随机主播信息
     * @return 随机主播用户信息
     */
    suspend fun getRandomBroadcasterPostV2(): NetworkResult<UserInfo> {
        return safeApiCall { apiService.getRandomBroadcasterPostV2() }
    }

    /**
     * 获取推荐主播
     * @return 推荐主播响应列表
     */
    suspend fun getRecommendedBroadcasters(): NetworkResult<List<RecommendedBroadcasterResponse>> {
        return safeApiCall { apiService.getRecommendedBroadcasters() }
    }

    /**
     * 发送推荐主播
     * @param request 发送推荐主播请求参数
     * @return 发送结果
     */
    suspend fun sendRecommendedBroadcaster(request: SendRecommendedBroadcasterRequest): NetworkResult<Boolean> {
        return safeApiCall { apiService.sendRecommendedBroadcaster(request) }
    }

    // </editor-fold>

    // <editor-fold desc="礼物相关接口">

    /**
     * 获取礼物数量
     * @param request 获取礼物数量请求参数
     * @return 礼物数量响应
     */
    suspend fun getGiftCount(request: GetGiftCountRequest): NetworkResult<GetGiftCountResponse> {
        return safeApiCall { apiService.getGiftCount(request) }
    }

    /**
     * 赠送礼物
     * @param request 赠送礼物请求参数
     * @return 赠送礼物响应
     */
    suspend fun giveUserGifts(request: GiveGiftRequest): NetworkResult<GiveGiftResponse> {
        return safeApiCall { apiService.giveUserGifts(request) }
    }

    /**
     * 获取礼物列表
     * @return 礼物信息列表
     */
    suspend fun getGiftListPostV2(request: GetGiftListRequest): NetworkResult<List<GiftInfo>> {
        return safeApiCall { apiService.getGiftListPostV2(request) }
    }

    // </editor-fold>

    // <editor-fold desc="头像和媒体相关接口">

    /**
     * 查询用户头像
     * @param request 头像搜索请求参数
     * @return 头像搜索响应
     */
    suspend fun searchAvatar(request: AvatarSearchRequest): NetworkResult<AvatarSearchResponse> {
        return safeApiCall { apiService.searchAvatar(request) }
    }

    /**
     * 获取OSS上传权限
     * @return OSS策略响应
     */
    suspend fun getOssPolicyPostV2(): NetworkResult<OssPolicyResponse> {
        return safeApiCall { apiService.getOssPolicyPostV2() }
    }

    /**
     * 更新头像
     * @param request 更新头像请求参数
     * @return 更新头像响应
     */
    suspend fun updateAvatar(request: UpdateAvatarRequest): NetworkResult<UpdateAvatarResponse> {
        return safeApiCall { apiService.updateAvatar(request) }
    }

    /**
     * 更新媒体资源
     * @param request 更新媒体请求参数
     * @return 更新媒体响应列表
     */
    suspend fun updateMedia(request: UpdateMediaRequest): NetworkResult<List<UpdateMediaResponse>> {
        return safeApiCall { apiService.updateMedia(request) }
    }

    /**
     * 批量查询图片远程URL
     * @param request 媒体URL请求列表
     * @return 媒体URL响应列表
     */
    suspend fun getMediaUrls(request: List<MediaUrlRequest>): NetworkResult<List<MediaUrlResponse>> {
        return safeApiCall { apiService.getMediaUrls(request) }
    }

    // </editor-fold>

    // <editor-fold desc="排行榜相关接口">

    /**
     * 用户排行榜列表查询
     * @param request 用户排行榜搜索请求参数
     * @return 用户排行榜响应
     */
    suspend fun searchUserRank(request: UserRankSearchRequest): NetworkResult<UserRankResponse> {
        return safeApiCall { apiService.searchUserRank(request) }
    }

    // </editor-fold>

    // <editor-fold desc="好友相关接口">

    /**
     * 添加好友
     * @param request 添加好友请求参数
     * @return 添加结果
     */
    suspend fun addFriend(request: AddFriendRequest): NetworkResult<Boolean> {
        return safeApiCall { apiService.addFriend(request) }
    }

    /**
     * 删除好友
     * @param request 删除好友请求参数
     * @return 删除结果
     */
    suspend fun unfriend(request: UnFriendRequest): NetworkResult<Boolean> {
        return safeApiCall { apiService.unfriend(request) }
    }

    /**
     * 获取用户访客分页列表
     * @param request 用户关注分页请求参数
     * @return 关注模型列表
     */
    suspend fun getUserFollowPage(request: UserFollowPageRequest): NetworkResult<List<FollowModel>> {
        return safeApiCall { apiService.getUserFollowPage(request) }
    }

    // </editor-fold>

    // <editor-fold desc="投诉和屏蔽相关接口">

    /**
     * 屏蔽
     * @param request 投诉记录插入请求参数
     * @return 投诉结果
     */
    suspend fun blockUser(request: ComplainInsertRecordRequest): NetworkResult<Boolean> {
        request.complainCategory = "Block"
        return safeApiCall { apiService.insertComplainRecord(request) }
    }

    /**
     * 投诉
     * @param request 投诉记录插入请求参数
     * @return 投诉结果
     */
    suspend fun reportUser(request: ComplainInsertRecordRequest): NetworkResult<Boolean> {
        request.complainCategory = "Report"
        return safeApiCall { apiService.insertComplainRecord(request) }
    }

    /**
     * 取消屏蔽
     * @param request 移除屏蔽请求参数
     * @return 取消结果
     */
    suspend fun removeBlock(request: RemoveBlockRequest): NetworkResult<Boolean> {
        return safeApiCall { apiService.removeBlock(request) }
    }

    /**
     * 获取屏蔽列表
     * @param request 用户屏蔽列表请求参数
     * @return 屏蔽列表项
     */
    suspend fun getBlockList(request: UserBlockListRequest): NetworkResult<List<BlockListItem>> {
        return safeApiCall { apiService.getBlockList(request) }
    }

    // </editor-fold>

    // <editor-fold desc="IM和聊天相关接口">

    /**
     * 获取融云Token
     * @param request 融云Token请求参数
     * @return 融云Token
     */
    suspend fun getRongCloudToken(request: RcTokenRequest): NetworkResult<String> {
        return safeApiCall { apiService.getRongCloudToken(request) }
    }

    /**
     * IM聊天限制上报
     * @param request 创建IM会话请求参数
     * @return 创建IM会话响应
     */
    suspend fun createImSession(request: CreateImSessionRequest): NetworkResult<CreateImSessionResponse> {
        return safeApiCall { apiService.createImSession(request) }
    }

    /**
     * 获取FAQ列表
     * @return FAQ信息列表
     */
    suspend fun getFaqList(): NetworkResult<FAQInfoList> {
        return safeApiCall { apiService.getFaqList() }
    }

    /**
     * 获取关系数量统计
     * @param timestamp 当前时间戳
     * @return 关系数量统计
     */
    suspend fun getRelationsCounter(timestamp: relationsCounterRequest): NetworkResult<RelationsCounter> {
        return safeApiCall { apiService.getRelationsCounter(timestamp) }
    }

    // </editor-fold>

    // <editor-fold desc="邮箱和密码相关接口">

    /**
     * 用户绑定邮箱
     * @param request 绑定邮箱请求参数
     * @return 绑定结果
     */
    suspend fun bindEmail(request: BindEmailRequest): NetworkResult<Boolean> {
        return safeApiCall { apiService.bindEmail(request) }
    }

    /**
     * 用户密码确认与修改
     * @param request 用户密码请求参数
     * @return 操作结果
     */
    suspend fun manageUserPassword(request: UserPasswordRequest): NetworkResult<Boolean> {
        return safeApiCall { apiService.manageUserPassword(request) }
    }

    // </editor-fold>

    // <editor-fold desc="机器人相关接口">

    /**
     * 通知后台机器人呼叫
     * @param request 机器人动作请求参数
     * @return 通知结果
     */
    suspend fun notifyRobotCall(request: RobotActionRequest): NetworkResult<Boolean> {
        return safeApiCall { apiService.notifyRobotCall(request) }
    }

    /**
     * 通知后台机器人IM
     * @param request 机器人动作请求参数
     * @return 通知结果
     */
    suspend fun notifyRobotIM(request: RobotActionRequest): NetworkResult<Boolean> {
        return safeApiCall { apiService.notifyRobotIM(request) }
    }

    /**
     * 注册奖励
     */
    suspend fun getPresentedCoin(): NetworkResult<GetPresentedCoinResp> {
        return safeApiCall { apiService.getPresentedCoin() }
    }

    // </editor-fold>

    // <editor-fold desc="上报和统计相关接口">

    /**
     * AF归因主动上报
     * @param request AF归因记录请求参数
     * @return 上报结果
     */
    suspend fun reportAfAttribution(request: AfAttributionRecordRequest): NetworkResult<Boolean> {
        return safeApiCall { apiService.reportAfAttribution(request) }
    }

    /**
     * 用户跳转界面上报
     * @param request 跳转页面请求参数
     * @return 上报结果
     */
    suspend fun reportSkipPage(request: SkipPageRequest): NetworkResult<Boolean> {
        return safeApiCall { apiService.reportSkipPage(request) }
    }

    /**
     * 提交安装引荐信息
     * @param args 安装引荐实体
     * @return 提交结果
     */
    suspend fun submitInstallReferrer(args: InstallReferrerEntity): NetworkResult<Boolean> {
        return safeApiCall { apiService.submitInstallReferrer(args) }
    }

    /**
     * 日志上报（使用专用的日志API服务）
     * @param request 日志上报请求参数
     * @return 上报结果
     */
    suspend fun reportLog(request: LogReportRequest): NetworkResult<Boolean> {
        return safeApiCall {
            Timber.d("Reporting log to: ${logApiService}")
            logApiService.reportLog(request)
        }
    }

    // </editor-fold>

    // <editor-fold desc="Banner和游戏相关接口">

    /**
     * 获取Banner列表
     * @return Banner信息响应列表
     */
    suspend fun getBannerInfo(): NetworkResult<List<BannerInfoResponse>> {
        return safeApiCall { apiService.getBannerInfo() }
    }

    // </editor-fold>

    // <editor-fold desc="设置相关接口">

    /**
     * 免打扰开关
     * @param request 免打扰开关请求参数
     * @return 开关结果
     */
    suspend fun switchNotDisturb(request: NotDisturbSwitchRequest): NetworkResult<Boolean> {
        return safeApiCall { apiService.switchNotDisturb(request) }
    }

    /**
     * 获取后置摄像头配置
     * @return 后置摄像头配置响应
     */
    suspend fun getRearCameraConfig(): NetworkResult<RearCameraConfigResponse> {
        return safeApiCall { apiService.getRearCameraConfig() }
    }

    /**
     * 开通后置摄像头
     * @param request 开通请求参数（可选）
     * @return 开通结果
     */
    suspend fun openRearCamera(request: Any? = null): NetworkResult<Boolean> {
        return safeApiCall { apiService.openRearCamera(request) }
    }


    // </editor-fold>


    // <editor-fold desc="风控">

    /**
     * 风控上报
     */
    suspend fun uploadRiskInfo(request: UploadRiskInfoReqs): NetworkResult<Boolean> {
        return safeApiCall { apiService.uploadRiskInfo(request) }
    }

    // </editor-fold>


    // <editor-fold desc="前后台切换上报">

    /**
     * 前后台切换上报, 模式（0-前台 1-后台）
     */
    suspend fun userModeSwitch(request: UserModeSwitchReq): NetworkResult<Boolean> {
        return safeApiCall { apiService.userModeSwitch(request) }
    }

    // </editor-fold>
}