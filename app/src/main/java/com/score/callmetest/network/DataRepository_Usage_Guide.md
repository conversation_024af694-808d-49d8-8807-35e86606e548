# DataRepository 使用指南

## 概述

`DataRepository` 是一个完整的网络请求封装类，基于 `ApiService` 中的所有接口实现，提供了统一的错误处理和网络异常捕获。

## 主要特性

### 1. 统一错误处理
- 使用 `safeApiCall` 进行统一的网络异常捕获
- 返回 `NetworkResult<T>` 类型，便于处理成功和失败状态
- 自动处理网络超时、连接异常等常见错误

### 2. 完整的接口覆盖
- 覆盖了 `ApiService` 中的所有 68 个接口
- 按功能模块分组，便于查找和使用
- 提供了便捷方法简化常用操作

### 3. 双API服务支持
- `apiService`: 主要API服务
- `logApiService`: 专用于日志上报的API服务

## 接口分类

### 认证相关接口
```kotlin
// 用户登录
val result = dataRepository.login(LoginRequest(oauthType, token))

// 校验Token
val isValid = dataRepository.isValidToken(userToken)

// 退出登录
val logoutResult = dataRepository.logout()
```

### 配置相关接口
```kotlin
// 获取应用配置
val config = dataRepository.getAppConfig(ver = 1)

// 获取策略配置
val strategy = dataRepository.getStrategy()
```

### 用户相关接口
```kotlin
// 获取用户信息（便捷方法）
val userInfo = dataRepository.getUserInfoSimple(userId)

// 获取用户金币
val coins = dataRepository.getUserCoins()

// 用户心跳上报
val heartbeat = dataRepository.reportUserActive()

// 前后台切换上报（便捷方法）
val switchResult = dataRepository.switchUserModeSimple(isForeground = true)
```

### 通话相关接口
```kotlin
// 创建通话频道
val channel = dataRepository.createChannel(CreateChannelRequest(...))

// 接听通话
val pickUpResult = dataRepository.pickUp(PickUpRequest(...))

// 挂断通话
val hangUpResult = dataRepository.hangUp(HangUpRequest(...))

// FlashChat 快速匹配
val flashResult = dataRepository.flashChat(FlashChatRequest(...))
```

### 充值相关接口
```kotlin
// 创建充值订单
val order = dataRepository.createRechargeOrder(CreateRechargeRequest(...))

// GooglePlay支付校验
val verifyResult = dataRepository.verifyGooglePlayPayment(GooglePlayPaymentVerifyRequest(...))

// 查询充值结果
val rechargeResult = dataRepository.searchRechargeResult(RechargeSearchRequest(...))
```

### 商品相关接口
```kotlin
// 查询商品列表
val goods = dataRepository.queryGoodsList(QueryGoodsListRequest(...))

// 获取促销商品
val promotion = dataRepository.getPromotionGoods(PromotionGoodsRequest(...))

// 搜索订阅商品
val subscriptions = dataRepository.searchSubscriptions(SubscriptionSearchRequest(...))
```

### 主播相关接口
```kotlin
// 搜索主播
val broadcasters = dataRepository.searchBroadcasters(SearchBroadcastersRequest(...))

// 获取主播额外信息（便捷方法）
val extraInfo = dataRepository.getBroadcasterExtraInfoSimple(broadcasterId)

// 评价主播
val evaluateResult = dataRepository.evaluateBroadcaster(BroadcasterEvaluateRequest(...))

// 获取随机主播
val randomBroadcaster = dataRepository.getRandomBroadcaster()
```

### 礼物相关接口
```kotlin
// 获取礼物列表
val gifts = dataRepository.getGiftList()

// 赠送礼物
val giftResult = dataRepository.giveUserGifts(GiveGiftRequest(...))

// 获取礼物数量
val giftCount = dataRepository.getGiftCount(GetGiftCountRequest(...))
```

### IM和聊天相关接口
```kotlin
// 获取融云Token
val rcToken = dataRepository.getRongCloudToken(RcTokenRequest(...))

// 创建IM会话
val imSession = dataRepository.createImSession(CreateImSessionRequest(...))

// 获取FAQ列表
val faqList = dataRepository.getFaqList()
```

## 使用示例

### 在ViewModel中使用
```kotlin
class UserViewModel : ViewModel() {
    private val dataRepository = RetrofitUtils.dataRepository
    
    fun loadUserInfo(userId: String) {
        viewModelScope.launch {
            when (val result = dataRepository.getUserInfoSimple(userId)) {
                is NetworkResult.Success -> {
                    // 处理成功结果
                    val userInfo = result.data
                    _userInfo.value = userInfo
                }
                is NetworkResult.Error -> {
                    // 处理错误
                    _error.value = result.message
                    if (result.isNetworkError) {
                        // 网络错误处理
                    }
                }
                is NetworkResult.Loading -> {
                    // 显示加载状态
                    _loading.value = true
                }
            }
        }
    }
}
```

### 错误处理
```kotlin
// 统一的错误处理方式
when (val result = dataRepository.someApiCall()) {
    is NetworkResult.Success -> {
        // 成功处理
        val data = result.data
    }
    is NetworkResult.Error -> {
        when {
            result.isNetworkError -> {
                // 网络错误：显示网络异常提示
                showToast("网络连接异常，请检查网络设置")
            }
            result.code == 401 -> {
                // 认证错误：跳转登录页
                navigateToLogin()
            }
            else -> {
                // 其他错误：显示具体错误信息
                showToast(result.message)
            }
        }
    }
    is NetworkResult.Loading -> {
        // 显示加载状态
        showLoading(true)
    }
}
```

## 便捷方法

DataRepository 提供了一些便捷方法来简化常用操作：

```kotlin
// 简化的用户信息获取
dataRepository.getUserInfoSimple(userId)

// 简化的在线状态获取
dataRepository.getUserOnlineStatusSimple(userId)

// 批量获取用户在线状态
dataRepository.getUserListOnlineStatusSimple(listOf("user1", "user2"))

// 简化的主播信息获取
dataRepository.getBroadcasterExtraInfoSimple(broadcasterId)

// 简化的屏蔽列表获取
dataRepository.getBlockListSimple(page = 1, limit = 20)

// 简化的前后台切换
dataRepository.switchUserModeSimple(isForeground = true)
```

## 注意事项

1. **线程安全**: 所有方法都是 `suspend` 函数，需要在协程中调用
2. **错误处理**: 建议统一使用 `NetworkResult` 的模式匹配进行错误处理
3. **日志上报**: `reportLog` 方法使用专用的 `logApiService`
4. **参数验证**: 调用前请确保请求参数的正确性
5. **网络状态**: 建议在网络异常时提供重试机制
