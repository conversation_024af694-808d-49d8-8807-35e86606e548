package com.score.callmetest.network

import retrofit2.Retrofit
import okhttp3.OkHttpClient
import java.util.concurrent.TimeUnit
import com.score.callmetest.util.SharePreferenceUtil
import com.score.callmetest.Constant
import com.score.callmetest.CallmeApplication
import android.util.Log
import com.score.callmetest.BuildConfig
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.logging.HttpLoggingInterceptor
import okhttp3.Interceptor
import okio.Buffer
import org.json.JSONObject
import okhttp3.Request
import com.score.callmetest.util.AESUtils
import com.score.callmetest.manager.AppConfigManager
import com.score.callmetest.util.HeaderUtils
import com.score.callmetest.util.logAsTag
import okhttp3.Headers
import okhttp3.Protocol
import okhttp3.RequestBody.Companion.toRequestBody
import okhttp3.Response
import okhttp3.ResponseBody.Companion.toResponseBody
import retrofit2.converter.gson.GsonConverterFactory
import timber.log.Timber

object RetrofitUtils {

    // URL映射表 - 将代码中的接口路径映射到实际的服务器接口路径
    // 根据callmeso.com映射关系.txt文件生成，已去除重复项并按功能分类
    private val urlMappingMap = mapOf(
        "/broadcaster/getCustomizePricePostV2" to "/baseApi/v2/config_maximum_value/set",
        "/wigo/info-flow/report" to "/baseApi/v0/msg_maximum_string/upload",
        "/security/getOTP" to "/baseApi/v0/userorder_last_key/download",
        "/test/testZset" to "/baseApi/resource/goods_first_hash/del",
        "/soul/user/getCommonConfig" to "/baseApi/resource/document_min_array/search",
        "/livekit/user/recharge" to "/baseApi/v2/memory_top_map/delete",
        "/review/zodiac/info-flow" to "/baseApi/v0/harddisk_maximum_table/del",
        "/security/logout" to "/baseApi/v2/config_list/get",
        "/soul/security/call/screenShotConfig" to "/baseApi/v3/product_last_group/add",
        "/user/getUserInfoForIndiaPkgPostV2" to "/baseApi/res/mem_maximum_rank/download",
        "/user/club/survey" to "/baseApi/v2/report_minimum_value/get",
        "/broadcaster/multiple/search" to "/baseApi/v1/userinfo_top_group/del",
        "/rankActivity/rankInfo" to "/baseApi/meminfo_second_hash/remove",
        "/mg-room/boss/enter-exit/confirm" to "/baseApi/v2/network_maximum_key/search",
        "/api/channel/withdrawOrderCreate" to "/baseApi/v0/mem_last_hash/update",
        "/user/backpack/latestExpireRechargeCardPostV2" to "/baseApi/resource/game_first_map/del",
        "/broadcaster/broadcasterInfo/uploadBatchAvatar" to "/baseApi/v1/report_max_json/get",
        "/info-flow/delInfoFlowAlbum" to "/baseApi/v3/stock_last_json/upload",
        "/config/saveUserScreenshots" to "/baseApi/v0/activity_second_value/update",
        "/user/getUserOnlineStatusForH5PostV2" to "/baseApi/v0/config_average_string/update",
        "/soul/security/oss/policy" to "/baseApi/res/user_average_rank/del",
        "/user/user/updateCreateTime" to "/baseApi/res/stock_top_json/download",
        "/activity/canRouletteShowPostV2" to "/baseApi/resource/doc_average_string/set",
        "/user/getUserInfoByUserNoPostV2" to "/baseApi/resource/device_average_array/del",
        "/soul/user/simpleConfig" to "/baseApi/res/activity_average_price/download",
        "/gift/live/list/get" to "/baseApi/res/money_average_string/patch",
        "/myLevel/list" to "/baseApi/v1/stock_max_rank/upload",
        "/user/skipPage" to "/baseApi/resource/diagram_max_string/patch",
        "/test/robot/call/user" to "/baseApi/resource/product_first_array/add",
        "/user/s3/uploadUrlPostV2" to "/baseApi/v0/order_second_hash/set",
        "/LiveInteractiveGift/theSwitch" to "/baseApi/v1/msg_maximum_key/patch",
        "/video-call/user/callResult" to "/baseApi/v3/config_average_list/update",
        "/showRoom/roomRankPostV2" to "/baseApi/v2/user_minimum_json/list",
        "/showRoom/open" to "/baseApi/v0/msg_maximum_item/post",
        "/review/ios/berrylive/video" to "/baseApi/v3/network_json/update",
        "/userLiveLevel/gradePoint" to "/baseApi/res/coin_max_map/patch",
        "/broadcaster/rank/searchRankForH5" to "/baseApi/v0/meminfo_minimum_price/download",
        "/showRoomUser/getUserSpecialDay" to "/baseApi/v2/memory_json/search",
        "/user/broadcasterVisitors" to "/baseApi/v3/meminfo_max_group/get",
        "/broadcaster/evaluate/submit" to "/baseApi/v3/mem_first_result/download",
        "/showRoomGoldContest/activityInfoPostV2" to "/baseApi/v1/message_list/delete",
        "/coin/recharge/search" to "/baseApi/v2/userorder_maximum_key/upload",
        "/showRoomTestEnv/open" to "/baseApi/v3/config_average_map/patch",
        "/user/activeing" to "/baseApi/v2/device_maximum_array/add",
        "/broadcaster/swipe/list" to "/baseApi/v0/user_maximum_array/del",
        "/security/existsByMobile" to "/baseApi/v3/userinfo_average_json/post",
        "/user/new-subscription/infoPostV2" to "/baseApi/msg_first_map/delete",
        "/info-flow/comment/report" to "/baseApi/v2/activity_max_map/upload",
        "/broadcaster/synCallInfo" to "/baseApi/money_minimum_item/search",
        "/coin/voiceToTextConsume" to "/baseApi/res/doc_minimum_array/get",
        "/showRoomUser/upscaleRank" to "/baseApi/doc_key/download",
        "/user/instruct/evaluationPostV2" to "/baseApi/resource/gift_average_hash/update",
        "/gift/giftMap/list" to "/baseApi/res/config_maximum_list/post",
        "/user/addFriend" to "/baseApi/promotion_last_list/upload",
        "/broadcaster/topGifters/search" to "/baseApi/v0/diagram_maximum_table/update",
        "/event/shopping/submit" to "/baseApi/v0/harddisk_group/search",
        "/user/isSendGift2" to "/baseApi/v1/order_json/remove",
        "/showRoom/quitSession" to "/baseApi/v0/link_second_group/set",
        "/user/acceptFriendApplyInReview" to "/baseApi/v2/activity_minimum_value/upload",
        "/showRoom/getPrivilegeByUserId" to "/baseApi/v1/link_average_string/add",
        "/broadcaster/wall/search" to "/baseApi/res/harddisk_rank/remove",
        "/broadcaster/freeCallPopup" to "/baseApi/v2/document_second_list/search",
        "/showRoomUser/room/viewers" to "/baseApi/v2/memory_top_hash/remove",
        "/review/multiSingSongRoom" to "/baseApi/goods_item/remove",
        "/user/getMediaUrl" to "/baseApi/resource/order_min_key/add",
        "/user/saveMoney" to "/baseApi/v1/order_average_list/set",
        "/user/languageSelectPostV2" to "/baseApi/v1/stock_string/update",
        "/video-call/command/confirm" to "/baseApi/document_second_hash/list",
        "/video-call/meetDislike" to "/baseApi/v1/config_second_result/get",
        "/user/getAvatarList" to "/baseApi/v3/promotion_key/post",
        "/user/getUserCoinsPostV2" to "/baseApi/game_minimum_table/delete",
        "/broadcaster/group/sendMessage" to "/baseApi/resource/msg_minimum_price/update",
        "/broadcaster/broadcasterInfo/uploadHorizontalAvatar" to "/baseApi/resource/report_average_string/set",
        "/user/broadcasterMaskFindAndMark" to "/baseApi/v3/promotion_top_json/delete",
        "/config/activity/slideshow" to "/baseApi/userorder_min_hash/del",
        "/game/slotMachine/list" to "/baseApi/v0/money_min_table/update",
        "/video-call/channel/uploadAbnormalData" to "/baseApi/v3/meminfo_minimum_value/del",
        "/live/activity/lottery/lottery" to "/baseApi/userorder_first_price/search",
        "/broadcaster/getBroadcasterMedias" to "/baseApi/v1/agent_max_key/upload",
        "/info-flow/topOption" to "/baseApi/res/link_first_result/get",
        "/coin/updateInsightPayOrderStatus" to "/baseApi/report_last_value/upload",
        "/user/freeUserWatchAdsAward" to "/baseApi/res/memory_first_string/remove",
        "/user/closeCamera/config" to "/baseApi/resource/promotion_top_table/search",
        "/mg-room/audiences" to "/baseApi/v2/money_minimum_string/remove",
        "/push/config/update" to "/baseApi/v1/activity_maximum_group/remove",
        "/hit/clientStartLog" to "/baseApi/message_min_list/patch",
        "/activity/canRechageTagShowPostV2" to "/baseApi/v2/mem_minimum_map/patch",
        "/myLevel/current" to "/baseApi/resource/diagram_first_map/download",
        "/coin/promotionPayChannel/get" to "/baseApi/resource/document_minimum_list/upload",
        "/broadcaster/warnContentsPostV2" to "/baseApi/v0/harddisk_minimum_json/search",
        "/broadcaster/settlementCycle" to "/baseApi/v3/link_minimum_value/download",
        "/hit/iconEntry/hit" to "/baseApi/v3/product_list/get",
        "/activity/getEntrance" to "/baseApi/v0/document_first_map/download",
        "/user/showRoomGiftFlow" to "/baseApi/res/promotion_minimum_hash/upload",
        "/gift/blindbox/award/record" to "/baseApi/res/game_first_group/post",
        "/videoCallV2/user/callResult" to "/baseApi/v2/network_average_string/upload",
        "/popUp/list" to "/baseApi/v1/document_last_hash/upload",
        "/video-call/match/soul/guide" to "/baseApi/harddisk_maximum_map/search",
        "/user/isFriendPaid" to "/baseApi/res/activity_maximum_item/list",
        "/broadcasterVideo/take" to "/baseApi/v2/link_first_price/set",
        "/video-call/zego/rtcTokenPostV2" to "/baseApi/resource/money_rank/set",
        "/user/inviteCode/bindV2" to "/baseApi/v1/memory_average_map/search",
        "/gift/v2/listPostV2" to "/baseApi/res/config_min_string/del",
        "/coin/callCard/info" to "/baseApi/promotion_top_group/get",
        "/video-call/match" to "/baseApi/v2/link_string/search",
        "/broadcaster/newUserTask/listPostV2" to "/baseApi/v1/userorder_group/list",
        "/wigo/info-flow/publish" to "/baseApi/v1/userinfo_first_string/upload",
        "/game/paypal-order/create" to "/baseApi/res/user_top_group/search",
        "/broadcaster/userEvaluate/evaluateList" to "/baseApi/v3/harddisk_first_key/set",
        "/video-call/record/warn" to "/baseApi/res/user_average_key/delete",
        "/hit/startAppHit" to "/baseApi/v2/memory_max_rank/search",
        "/broadcaster/groupMessageConfigPostV2" to "/baseApi/v1/promotion_min_list/remove",
        "/broadcaster/mediaScore/receive" to "/baseApi/v2/network_maximum_string/add",
        "/user/dingTalkRobot" to "/baseApi/res/userorder_top_table/set",
        "/test/incomeCoefficient" to "/baseApi/config_max_array/del",
        "/retrieve/custom/emailSubmit" to "/baseApi/res/message_top_array/list",
        "/user/getUserInfoForWigo" to "/baseApi/v0/game_top_table/patch",
        "/mg-room/exit" to "/baseApi/v1/document_top_array/search",
        "/info-flow/getUserListNewInfoFlow" to "/baseApi/v2/goods_second_value/get",
        "/coin/recharge/list" to "/baseApi/coin_last_value/list",
        "/video-call/soul/channel/create" to "/baseApi/resource/message_second_rank/add",
        "/test/miles/swipe/config" to "/baseApi/link_average_map/list",
        "/user/getUserCardInfo" to "/baseApi/res/gift_maximum_key/remove",
        "/user/guide/searchOldToken" to "/baseApi/v3/config_min_key/search",
        "/gift/broadcasterMask/unlock" to "/baseApi/resource/activity_top_price/add",
        "/review/purchaseMedia" to "/baseApi/v3/config_last_string/list",
        "/user/getHobbies" to "/baseApi/res/userinfo_average_group/list",
        "/video-call/broadcaster/settlementIncome" to "/baseApi/v2/msg_maximum_price/del",
        "/broadcaster/rank/search" to "/baseApi/doc_maximum_price/patch",
        "/user/club/notice" to "/baseApi/resource/report_top_result/delete",
        "/soul/user/getLanguageTag" to "/baseApi/v0/userorder_max_list/post",
        "/user/sendMissCallMessage" to "/baseApi/v0/coin_max_value/get",
        "/user/deleteAccount" to "/baseApi/document_second_table/patch",
        "/user/alias/create" to "/baseApi/v0/user_minimum_table/del",
        "/test/redis" to "/baseApi/v2/doc_result/delete",
        "/mg-room/mic/queue/details" to "/baseApi/res/product_maximum_result/update",
        "/gift/live/list/v3" to "/baseApi/v0/product_second_string/delete",
        "/broadcaster/acitvityRank/search" to "/baseApi/v1/report_min_result/set",
        "/user/svip/gradePoint" to "/baseApi/v2/meminfo_last_item/delete",
        "/shortLink/deeplink/config" to "/baseApi/v0/stock_max_json/patch",
        "/coin/h5-recharge/search" to "/baseApi/agent_second_table/post",
        "/user/rejectFriendApplyInReview" to "/baseApi/order_maximum_result/upload",
        "/security/selectCallActionLog" to "/baseApi/v0/activity_last_key/download",
        "/game/room/info" to "/baseApi/v2/msg_table/get",
        "/videoCallV2/hangUp" to "/baseApi/v0/mem_second_price/post",
        "/broadcaster/recommend/sayHi" to "/baseApi/resource/gift_second_json/set",
        "/showRoom/status" to "/baseApi/v2/user_max_group/list",
        "/msg/getActivityMsg" to "/baseApi/v0/doc_min_value/get",
        "/risk/shuMei/video/callback" to "/baseApi/v2/agent_second_table/post",
        "/activity/livChatStar" to "/baseApi/v3/money_last_item/download",
        "/security/cleanOTP" to "/baseApi/res/product_min_group/delete",
        "/mg-room/mgDailyTaskInfo" to "/baseApi/v0/user_list/download",
        "/showRoom/statisticsPostV2" to "/baseApi/resource/userorder_first_table/del",
        "/user/backpack/giftsPostV2" to "/baseApi/res/memory_first_group/upload",
        "/broadcaster/mediaScore/get" to "/baseApi/resource/game_maximum_array/patch",
        "/game/slotMachine/rank" to "/baseApi/v1/meminfo_min_json/list",
        "/callback/rongcloud/chatRoomStatus" to "/baseApi/res/config_min_map/post",
        "/review/icool/broadcasterWall" to "/baseApi/goods_array/upload",
        "/live/activity/confessionWall/invitations" to "/baseApi/v0/activity_last_item/add",
        "/pipline/vShotPostV2" to "/baseApi/v1/agent_average_hash/delete",
        "/wigo/info-flow/comment-delete" to "/baseApi/network_second_key/download",
        "/video-call/newMatch" to "/baseApi/v2/game_last_array/update",
        "/user/incentive/collect" to "/baseApi/v1/document_value/search",
        "/broadcaster/exclusive/taskList" to "/baseApi/promotion_value/download",
        "/game/lingxian/updateCoin" to "/baseApi/res/document_max_group/patch",
        "/shortLink/targetPostV2" to "/baseApi/config_max_list/patch",
        "/live/gift/favorite/switchTop" to "/baseApi/res/promotion_first_string/delete",
        "/callback/zego/cloudRecord" to "/baseApi/v2/product_maximum_group/patch",
        "/retrieve/custom/goodsTrigger" to "/baseApi/user_top_list/remove",
        "/user/getBroadcasterExtraInfo" to "/baseApi/agent_minimum_result/get",
        "/user/giveUserGifts" to "/baseApi/resource/coin_second_array/add",
        "/common/all/getHandlerMethod" to "/baseApi/v2/message_second_result/set",
        "/shortLink/media/get" to "/baseApi/res/report_array/get",
        "/gift/list" to "/baseApi/res/coin_max_key/del",
        "/broadcaster/invite/linkPostV2" to "/baseApi/v2/agent_maximum_group/get",
        "/user/listRandomBroadcasterPostV3" to "/baseApi/v2/gift_minimum_string/del",
        "/coin/recharge/upgradeGoods" to "/baseApi/v1/meminfo_first_map/del",
        "/test/field/map" to "/baseApi/v0/gift_average_value/search",
        "/user/saveUserInfoIncludeHobby" to "/baseApi/v0/coin_maximum_group/search",
        "/test/testPush" to "/baseApi/v2/order_rank/del",
        "/videoCallV2/userEvaluate/evaluateList" to "/baseApi/resource/promotion_min_price/delete",
        "/user/broadcasterChangeMask" to "/baseApi/v3/game_max_key/patch",
        "/broadcaster/recommend/search" to "/baseApi/res/memory_top_value/del",
        "/info-flow/comment/delete" to "/baseApi/mem_first_group/patch",
        "/user/getWatchAdsConfig" to "/baseApi/v2/user_last_result/update",
        "/coin/recharge/broadcasterInvitation" to "/baseApi/v2/money_max_group/search",
        "/config/getAppConfig" to "/baseApi/v3/message_minimum_result/download",
        "/robotScript/paidScript" to "/baseApi/link_minimum_json/post",
        "/user/getFriendsList" to "/baseApi/resource/link_average_json/upload",
        "/videoCallV2/match/exit" to "/baseApi/v1/meminfo_item/download",
        "/user/avatar/search" to "/baseApi/promotion_average_string/post",
        "/activity/assembleAwardRecord" to "/baseApi/v3/config_top_json/download",
        "/user/listUserInfos" to "/baseApi/v2/agent_average_price/get",
        "/info-flow/getOtherUserTopic" to "/baseApi/v1/goods_second_item/remove",
        "/gift/v2/user-code/batch" to "/baseApi/v3/link_minimum_list/add",
        "/mg-room/mic/down" to "/baseApi/v3/meminfo_first_key/search",
        "/user/giveUserGiftsForSoul" to "/baseApi/v3/memory_first_map/delete",
        "/robotScript/me/robotScript/config" to "/baseApi/v3/document_maximum_map/get",
        "/user/rearCamera/open" to "/baseApi/res/product_minimum_string/search",
        "/user/getUserListOnlineStatus" to "/baseApi/res/activity_max_result/remove",
        "/video-call/match/cancel" to "/baseApi/msg_second_json/upload",
        "/mg-room/mic/up/queue" to "/baseApi/v0/product_maximum_string/get",
        "/broadcaster/swipe/miles/config" to "/baseApi/v1/message_max_string/update",
        "/mg-room/callPkgInfo" to "/baseApi/v2/memory_second_rank/post",
        "/callback/oss/callback" to "/baseApi/resource/diagram_item/update",
        "/user/wall/search" to "/baseApi/v1/followlist_max_string/patch",
        "/test/encrypt" to "/baseApi/v3/coin_maximum_item/add",
        "/user/recommendBroadcaster/send" to "/baseApi/res/user_max_string/upload",
        "/retrieve/custom/info" to "/baseApi/v2/meminfo_second_price/update",
        "/common/appLog/upload" to "/baseApi/v3/followlist_min_string/remove",
        "/test/visitBroadcaster" to "/baseApi/config_max_group/download",
        "/week-card/purchase" to "/baseApi/v0/promotion_last_group/post",
        "/.well-known/acme-challenge" to "/baseApi/res/document_minimum_string/update",
        "/user/robotCall" to "/baseApi/v1/meminfo_second_price/get",
        "/broadcaster/group/message/del" to "/baseApi/v0/disk_average_list/search",
        "/activity/getRoulettePartAward" to "/baseApi/v1/promotion_minimum_key/get",
        "/broadcaster/checkBroadcasterConfigPostV2" to "/baseApi/user_min_price/download",
        "/user/getFriendsListPage" to "/baseApi/v0/userinfo_minimum_price/upload",
        "/user/getChargeMedia" to "/baseApi/resource/link_max_array/set",
        "/liveUser/friend/del" to "/baseApi/v3/order_average_result/list",
        "/userLiveLevel/shining" to "/baseApi/resource/document_max_string/get",
        "/user/game/sign" to "/baseApi/v0/order_average_map/list",
        "/user/location" to "/baseApi/res/doc_key/set",
        "/user/getUserOnlineStatus" to "/baseApi/resource/followlist_average_group/delete",
        "/soulVideoCall/v2/pickUp" to "/baseApi/v1/message_max_list/update",
        "/coin/tppRecharge/infoPostV2" to "/baseApi/v3/promotion_min_map/download",
        "/showRoom/statusPostV2" to "/baseApi/res/meminfo_max_hash/list",
        "/coin/goods/getLastSpecialOffer" to "/baseApi/resource/diagram_max_array/download",
        "/region/getLoginPicByIp" to "/baseApi/v1/order_first_array/set",
        "/coin/order/writeBackPayNo" to "/baseApi/v0/mem_first_json/get",
        "/user/getIsFollowedInReview" to "/baseApi/v2/game_top_array/upload",
        "/game/banner/info" to "/baseApi/v3/product_map/get",
        "/broadcaster/wall/broadcasterProvince" to "/baseApi/gift_average_map/delete",
        "/broadcasterVideo/teach/languageList" to "/baseApi/report_list/remove",
        "/gift/v2/user-code" to "/baseApi/v2/promotion_min_item/add",
        "/config/activity/slideshowPostV2" to "/baseApi/v0/goods_second_rank/search",
        "/coin/callCard/receive" to "/baseApi/userinfo_average_list/add",
        "/broadcaster/wall/search/v2" to "/baseApi/v2/document_min_key/download",
        "/broadcaster/mediaScore/desc" to "/baseApi/report_max_array/get",
        "/security/otpLogin" to "/baseApi/game_first_string/download",
        "/user/feedbackType" to "/baseApi/v2/user_second_hash/search",
        "/report/complain/apply" to "/baseApi/res/message_json/post",
        "/mg-room/audiencesPostV2" to "/baseApi/resource/followlist_array/get",
        "/mg-room/mic/last-active/details" to "/baseApi/resource/report_top_group/del",
        "/user/setGiftWallAction" to "/baseApi/v3/gift_hash/post",
        "/user/camera/close" to "/baseApi/v1/activity_second_rank/remove",
        "/security/phoneLogin" to "/baseApi/v1/mem_maximum_value/update",
        "/broadcaster/userEvaluate/submitPostV2" to "/baseApi/agent_average_rank/set",
        "/user/createImSession" to "/baseApi/v2/followlist_last_result/del",
        "/test/sendConfirm" to "/baseApi/device_maximum_map/list",
        "/robotScript/me/paidScript" to "/baseApi/resource/memory_last_hash/list",
        "/broadcaster/club/config" to "/baseApi/resource/activity_maximum_string/list",
        "/callback/paypal/notify" to "/baseApi/resource/user_max_value/patch",
        "/coin/usdtPayScreenshotUpload" to "/baseApi/v1/msg_min_string/post",
        "/config/getIOSConfigPostV2" to "/baseApi/res/meminfo_minimum_json/remove",
        "/mg-room/mic/speak/apply" to "/baseApi/v1/order_average_result/download",
        "/user/freeAddFriendCountPostV2" to "/baseApi/v0/device_second_map/get",
        "/soulVideoCall/broadcaster/match" to "/baseApi/v3/message_second_map/update",
        "/msg/notificationList" to "/baseApi/res/order_first_list/get",
        "/coin/goods/search" to "/baseApi/link_minimum_string/remove",
        "/broadcaster/invitation/infoPostV2" to "/baseApi/resource/document_max_price/upload",
        "/user/followEachOther" to "/baseApi/v0/userinfo_result/delete",
        "/user/batchUserInfo" to "/baseApi/v3/product_top_item/add",
        "/live/activity/lottery/overview" to "/baseApi/money_min_hash/list",
        "/review/cherry/top-pics" to "/baseApi/memory_average_item/remove",
        "/retrieve/custom/task/complete" to "/baseApi/v0/network_minimum_price/delete",
        "/video-call/match/clean/history" to "/baseApi/v2/promotion_first_list/add",
        "/mg-room/mic/invitation" to "/baseApi/v0/meminfo_maximum_value/list",
        "/wigo/info-flow/comment-list" to "/baseApi/followlist_min_json/post",
        "/user/feedback" to "/baseApi/v2/user_hash/remove",
        "/broadcaster/invitation/info/v2" to "/baseApi/v3/harddisk_min_rank/delete",
        "/live/activity/h5/config/list" to "/baseApi/v3/order_min_group/update",
        "/broadcaster/invitation/search" to "/baseApi/v2/config_minimum_table/remove",
        "/mg-room/updateCoinTaskAnnounce" to "/baseApi/resource/doc_minimum_table/list",
        "/user/getMediaUrlPostV2" to "/baseApi/v2/memory_minimum_array/get",
        "/broadcaster/freeCallPopupClose" to "/baseApi/v0/message_first_price/add",
        "/showRoomUser/recommends" to "/baseApi/v3/doc_average_result/patch",
        "/user/rank/searchCouple" to "/baseApi/res/config_max_json/delete",
        "/info-flow/like/updateStatus" to "/baseApi/resource/memory_last_price/set",
        "/broadcaster/dayStatisticPostV2" to "/baseApi/v0/link_first_map/delete",
        "/user/isGuardBroadcaster" to "/baseApi/v0/msg_top_item/post",
        "/user/guardBroadcaster" to "/baseApi/stock_average_hash/upload",
        "/coin/paypal/create" to "/baseApi/goods_last_group/get",
        "/coin/recharge/getCoinLinkInfo" to "/baseApi/v0/mem_average_array/search",
        "/live/activity/confessionWall/delete" to "/baseApi/res/mem_average_table/update",
        "/test/revenue/job/api" to "/baseApi/v0/link_min_item/download",
        "/pipline/switchPostV2" to "/baseApi/res/msg_first_hash/post",
        "/common/methodMapping" to "/baseApi/v0/stock_maximum_map/get",
        "/coin/recharge/payment/gp" to "/baseApi/res/link_array/upload",
        "/test/t/checkReviewStatus" to "/baseApi/v1/message_key/download",
        "/showRoom/statistics" to "/baseApi/res/stock_max_group/download",
        "/broadcaster/newUserTask/delete" to "/baseApi/resource/doc_maximum_hash/patch",
        "/broadcaster/broadcasterInfo/uploadMediumAvatar" to "/baseApi/v2/document_first_key/add",
        "/live/activity/lottery/tasks" to "/baseApi/mem_first_result/del",
        "/pipline/payUnlock" to "/baseApi/resource/msg_max_item/del",
        "/activity/charge-active/receive" to "/baseApi/v3/network_key/update",
        "/user/getBroadcasterCoins" to "/baseApi/config_first_table/remove",
        "/broadcaster/swipe/like" to "/baseApi/v1/userorder_last_table/remove",
        "/broadcaster/invitation/search/v2" to "/baseApi/v2/harddisk_first_value/remove",
        "/config/getLaunchScreen" to "/baseApi/v2/meminfo_top_hash/post",
        "/test/field/decode" to "/baseApi/resource/memory_first_rank/search",
        "/activity/livChatStarPostV2" to "/baseApi/resource/meminfo_maximum_result/update",
        "/user/getAvatarListPostV2" to "/baseApi/v0/config_key/list",
        "/security/captchaApp" to "/baseApi/resource/coin_max_group/remove",
        "/video-call/match/exit" to "/baseApi/res/user_min_array/download",
        "/broadcaster/freeCallTrigger" to "/baseApi/v0/link_first_array/delete",
        "/ads/getUnlockConfig" to "/baseApi/res/diagram_top_list/del",
        "/game/paypal-order/query" to "/baseApi/v0/product_last_json/post",
        "/security/cleanOTPPostV2" to "/baseApi/res/doc_maximum_string/set",
        "/showRoomUser/quotaRank" to "/baseApi/v0/coin_last_json/upload",
        "/showRoom/room/rank" to "/baseApi/v2/stock_maximum_map/add",
        "/video-call/broadcaster/onlinePostV2" to "/baseApi/v2/harddisk_last_list/add",
        "/ads/recordVIP" to "/baseApi/product_average_rank/search",
        "/user/suggestion/submit" to "/baseApi/resource/document_minimum_result/add",
        "/user/robotIM" to "/baseApi/v1/message_second_price/add",
        "/callback/agora/request" to "/baseApi/resource/network_max_rank/remove",
        "/activity/assembleAward" to "/baseApi/res/goods_min_table/update",
        "/common/translate" to "/baseApi/v0/harddisk_min_group/download",
        "/activity/active/report" to "/baseApi/res/order_second_hash/upload",
        "/broadcaster/rank/searchV2" to "/baseApi/v1/mem_first_item/del",
        "/broadcaster/wall/highQuality" to "/baseApi/v2/meminfo_average_price/download",
        "/pipline/livStar" to "/baseApi/resource/money_max_table/patch",
        "/test/status" to "/baseApi/v0/order_last_string/get",
        "/shortLink/finalTarget" to "/baseApi/money_top_rank/delete",
        "/user/listRelateUserInfos" to "/baseApi/msg_last_group/delete",
        "/coin/recharge/createCoinLink" to "/baseApi/v1/followlist_second_result/update",
        "/test/match-weight-score" to "/baseApi/resource/order_top_string/list",
        "/user/favorite/pageFavorites" to "/baseApi/v3/promotion_key/patch",
        "/showRoom/config" to "/baseApi/resource/link_last_result/update",
        "/user/isRechargeUsers" to "/baseApi/v1/harddisk_min_list/delete",
        "/report/complain/insertRecord" to "/baseApi/res/money_second_hash/update",
        "/broadcaster/task/settlementPostV2" to "/baseApi/res/disk_last_array/download",
        "/broadcasterVideo/teach/commentAndLike" to "/baseApi/v1/disk_minimum_array/patch",
        "/user/functionInfo" to "/baseApi/v1/msg_min_hash/list",
        "/broadcaster/commonCallPrice" to "/baseApi/resource/order_second_group/del",
        "/pipline/switch" to "/baseApi/v3/document_last_result/delete",
        "/broadcaster/task/settlement" to "/baseApi/v2/harddisk_first_rank/get",
        "/activity/checkin-active/check" to "/baseApi/v1/device_average_json/upload",
        "/coin/recharge/checkBroadcasterInvitation" to "/baseApi/v3/agent_min_item/remove",
        "/user/getIsFollowedPostV2" to "/baseApi/v1/user_minimum_price/upload",
        "/soulVideoCall/v2/channel/create" to "/baseApi/product_last_rank/set",
        "/pipline/vShot" to "/baseApi/v0/product_second_value/patch",
        "/user/v2/userRelations" to "/baseApi/v2/device_minimum_string/add",
        "/activity/user-active/bonusReceive" to "/baseApi/activity_result/list",
        "/user/recommendGoddess" to "/baseApi/v1/disk_array/del",
        "/gift/v2/user-codePostV2" to "/baseApi/v3/memory_minimum_string/search",
        "/coin/goods/list" to "/baseApi/device_min_key/update",
        "/report/apply" to "/baseApi/resource/money_group/download",
        "/ads/descUnlockTimes" to "/baseApi/resource/agent_average_group/patch",
        "/video-call/channel/log" to "/baseApi/v0/disk_min_group/download",
        "/review/zodiac/list" to "/baseApi/v0/stock_min_item/post",
        "/activity/rouletteLottery" to "/baseApi/v1/message_last_hash/patch",
        "/dressup/user/off" to "/baseApi/res/document_top_group/del",
        "/activity/getActivityIdList" to "/baseApi/v1/link_min_price/post",
        "/activity/getGiftScoreSummary" to "/baseApi/v2/disk_second_map/download",
        "/gift/getGiftCount" to "/baseApi/v2/mem_average_map/remove",
        "/broadcaster/autoCall/autoCallStrategyConfig" to "/baseApi/v3/network_maximum_value/patch",
        "/user/searchCoinFlow" to "/baseApi/v3/gift_average_list/download",
        "/user/getChargeMediaForH5PostV2" to "/baseApi/resource/order_last_hash/post",
        "/permission/setting/report" to "/baseApi/v0/message_minimum_array/download",
        "/user/invitation/getCode" to "/baseApi/resource/report_maximum_table/del",
        "/game/flow/submit" to "/baseApi/resource/memory_average_hash/upload",
        "/broadcaster/updateCustomizePrice" to "/baseApi/config_last_list/patch",
        "/pipline/livStarPostV2" to "/baseApi/v2/msg_first_rank/post",
        "/user/getUserMaskListPostV2" to "/baseApi/resource/doc_average_json/upload",
        "/wigo/info-flow/delete" to "/baseApi/v2/followlist_first_result/del",
        "/coin/callCard/infoPostV2" to "/baseApi/resource/game_minimum_rank/update",
        "/activity/getRouletteOverview" to "/baseApi/v1/document_top_map/update",
        "/info-flow/Gifts/giveUser" to "/baseApi/v1/doc_last_hash/search",
        "/showRoomUser/uploadInfoFlowCover" to "/baseApi/v1/msg_value/search",
        "/broadcasterVideo/teach/list" to "/baseApi/message_min_table/set",
        "/user/getRandomBroadcaster" to "/baseApi/config_list/download",
        "/user/getChargeMediaPostV2" to "/baseApi/v3/diagram_max_json/del",
        "/security/exceptionCode" to "/baseApi/res/game_first_map/add",
        "/showRoom/dataPostV2" to "/baseApi/v2/order_average_table/upload",
        "/user/FAQ/get" to "/baseApi/v3/game_last_table/patch",
        "/showRoom/coinTaskInfo" to "/baseApi/resource/userorder_second_key/download",
        "/broadcaster/guardian/search" to "/baseApi/v0/gift_minimum_group/download",
        "/live/questionnaire/byNo" to "/baseApi/v1/agent_last_value/add",
        "/live/activity/lottery/rewards" to "/baseApi/v2/device_minimum_value/post",
        "/user/sendMissCallMessagePostV2" to "/baseApi/v2/diagram_first_result/del",
        "/mg-room/callPkgInfoPostV2" to "/baseApi/v3/followlist_max_json/set",
        "/unity/user/updateUserEvaluation" to "/baseApi/v1/followlist_average_table/delete",
        "/test/google/translate" to "/baseApi/res/agent_key/set",
        "/video-call/channel/logPostV2" to "/baseApi/link_value/delete",
        "/video-call/pickUp" to "/baseApi/resource/report_maximum_price/del",
        "/shortLink/ascribe/submit" to "/baseApi/agent_min_item/del",
        "/user/visitor/listPostV2" to "/baseApi/res/game_last_map/search",
        "/liveRoom/bulletScreen/send" to "/baseApi/res/mem_second_result/remove",
        "/config/sendDelAccountPostV2" to "/baseApi/res/msg_second_value/add",
        "/liveRoom/list" to "/baseApi/diagram_first_rank/get",
        "/activity/user-active/data" to "/baseApi/resource/meminfo_second_group/remove",
        "/liveUser/getFriendsListPage" to "/baseApi/message_maximum_hash/add",
        "/user/svip/equity" to "/baseApi/v3/userinfo_minimum_rank/get",
        "/config/sh" to "/baseApi/v3/goods_first_json/patch",
        "/user/guardianRank" to "/baseApi/resource/goods_min_item/update",
        "/skiResort/comment/list" to "/baseApi/v2/diagram_max_key/get",
        "/user/getUserInfoForIndiaPkg" to "/baseApi/v3/document_max_json/patch",
        "/user/getRandomBroadcasterPostV2" to "/baseApi/v2/meminfo_last_map/set",
        "/broadcaster/exclusive/receiveTaskBonus" to "/baseApi/res/link_max_map/list",
        "/broadcaster/exclusive/weeklyTaskList" to "/baseApi/res/message_average_map/add",
        "/user/status/setOrRemoveOnCallStatus" to "/baseApi/res/document_second_json/update",
        "/showRoom/uploadCover" to "/baseApi/v2/coin_maximum_array/update",
        "/user/saveMoney/list" to "/baseApi/res/message_json/update",
        "/showRoomUser/joinSession" to "/baseApi/res/gift_top_item/list",
        "/broadcaster/exclusive/taskListPostV2" to "/baseApi/resource/msg_first_rank/download",
        "/activity/giftUpgradeRankDataPostV2" to "/baseApi/v0/userinfo_top_array/add",
        "/week-card/bannerDetail" to "/baseApi/v3/harddisk_second_rank/add",
        "/soulVideoCall/v2/popUp" to "/baseApi/v3/activity_average_item/post",
        "/coin/recharge/subscribeVipSign" to "/baseApi/v3/network_first_list/patch",
        "/mg-room/boss/status-switch" to "/baseApi/order_second_string/download",
        "/user/v2/broadcasterRelations" to "/baseApi/resource/goods_last_value/remove",
        "/videoCallV2/popUp" to "/baseApi/v0/game_max_string/upload",
        "/user/addRobotVisitor" to "/baseApi/harddisk_min_string/patch",
        "/live/activity/info" to "/baseApi/v0/document_minimum_group/download",
        "/retrieve/custom/goodsGet" to "/baseApi/res/gift_average_table/add",
        "/showRoomUser/quitSession" to "/baseApi/resource/stock_average_rank/del",
        "/risk/info/upload" to "/baseApi/v1/harddisk_maximum_item/add",
        "/user/action/refuseCallBroadcaster" to "/baseApi/v0/meminfo_last_list/get",
        "/IMBack/robotScriptV2" to "/baseApi/memory_min_value/update",
        "/liveRoom/data" to "/baseApi/v3/doc_minimum_result/list",
        "/user/relationsCounter" to "/baseApi/resource/activity_max_value/delete",
        "/review/cherry/video-flows" to "/baseApi/v0/coin_first_map/search",
        "/scriptKill/listPostV2" to "/baseApi/order_max_array/set",
        "/live/activity/lottery/awards" to "/baseApi/v1/device_first_map/update",
        "/info-flow/comment/publish" to "/baseApi/v2/network_min_item/remove",
        "/showRoomUser/audiences" to "/baseApi/v3/money_group/search",
        "/user/rank/searchV2" to "/baseApi/v2/user_min_hash/remove",
        "/coin/recharge/checkBroadcasterInvitationPostV2" to "/baseApi/resource/gift_maximum_group/upload",
        "/coin/h5-recharge/goods" to "/baseApi/v0/activity_last_array/update",
        "/robotScript/autoSayHi" to "/baseApi/v0/memory_table/delete",
        "/user/searchGiftFlow" to "/baseApi/v3/product_min_string/set",
        "/unity/user/getUnityTopic" to "/baseApi/res/promotion_average_table/remove",
        "/review/ios/berrylive/videoPostV2" to "/baseApi/resource/userinfo_last_result/remove",
        "/user/visitor/list" to "/baseApi/harddisk_first_price/download",
        "/retrieve/custom/config" to "/baseApi/resource/activity_second_list/patch",
        "/mg-room/dataPostV2" to "/baseApi/v0/memory_minimum_list/list",
        "/video-call/channel/latelyRecord" to "/baseApi/res/diagram_average_item/upload",
        "/wigo/info-flow/newest-publish" to "/baseApi/stock_top_key/del",
        "/activity/getTopN" to "/baseApi/product_price/list",
        "/activity/getRouletteInfo" to "/baseApi/res/userorder_maximum_json/remove",
        "/broadcaster/swipe/dislike" to "/baseApi/v2/promotion_min_rank/set",
        "/user/languageSelect" to "/baseApi/v3/game_first_value/post",
        "/user/liveGiveUserGifts" to "/baseApi/v3/network_hash/post",
        "/game/broadcaster/activity/list" to "/baseApi/resource/config_max_value/download",
        "/broadcaster/getBroadcasterFirstMedia" to "/baseApi/money_top_item/update",
        "/user/getFeedbackConfig" to "/baseApi/v2/followlist_max_array/remove",
        "/user/getInfoByUid" to "/baseApi/v3/doc_min_map/get",
        "/shortLink/getPostV2" to "/baseApi/v3/config_average_string/patch",
        "/broadcaster/autoCall/autoCallSwitch" to "/baseApi/v0/money_second_group/update",
        "/user/updateAnswer" to "/baseApi/resource/config_average_price/post",
        "/broadcaster/getBroadcasterPhotosAndVideos" to "/baseApi/stock_min_result/list",
        "/broadcaster/onCallStrategyConfigPostV2" to "/baseApi/v0/msg_min_string/patch",
        "/live/gift/favorite/delete" to "/baseApi/disk_last_map/upload",
        "/broadcaster/broadcasterInfo/uploadClubAvatar" to "/baseApi/resource/product_second_key/post",
        "/security/existsByMobilePostV2" to "/baseApi/network_maximum_table/del",
        "/user/saveMoney/statistic" to "/baseApi/v0/order_maximum_json/download",
        "/coin/subscription/search" to "/baseApi/v2/memory_first_key/list",
        "/config/submitInstallReferer" to "/baseApi/v0/report_second_list/remove",
        "/user/getLevelInfo" to "/baseApi/res/gift_min_value/get",
        "/user/getUserListOnlineStatusPostV3" to "/baseApi/v3/disk_first_rank/add",
        "/config/content/search" to "/baseApi/res/config_minimum_value/update",
        "/showRoom/upsertSignPkgPrice" to "/baseApi/resource/gift_minimum_string/set",
        "/user/levelPower/noticeUnlockList" to "/baseApi/network_max_price/post",
        "/week-card/detailForH5" to "/baseApi/v0/promotion_max_value/set",
        "/showRoom/frameUpload" to "/baseApi/agent_max_map/upload",
        "/showRoom/getUserMuteStatus" to "/baseApi/v3/userinfo_minimum_key/del",
        "/game/paypal-order/capture" to "/baseApi/v2/money_average_table/del",
        "/shortLink/finalTargetPostV2" to "/baseApi/v1/mem_average_list/search",
        "/mg-room/mic/speak/handle" to "/baseApi/resource/goods_min_array/add",
        "/user/invitation/getInviter" to "/baseApi/res/harddisk_minimum_string/set",
        "/showRoom/getPrivilegeByUserIdPostV2" to "/baseApi/v3/diagram_top_result/patch",
        "/mg-room/mic/up/expedite" to "/baseApi/v1/money_minimum_string/upload",
        "/gift/giftMap/listPostV2" to "/baseApi/v0/doc_minimum_map/post",
        "/broadcaster/fakeBroadcasterPopup" to "/baseApi/v3/diagram_max_group/add",
        "/user/oss/policy" to "/baseApi/res/meminfo_minimum_hash/list",
        "/activity/charge-active/data" to "/baseApi/res/network_minimum_table/get",
        "/review/multiChatRoom" to "/baseApi/v2/gift_min_key/download",
        "/hit/rechargeH5ClientHit" to "/baseApi/v0/message_min_rank/list",
        "/game/getGameToken" to "/baseApi/v3/stock_average_map/patch",
        "/callback/zego/rtc" to "/baseApi/v0/network_last_result/remove",
        "/showRoomUser/uploadCover" to "/baseApi/v1/coin_second_group/remove",
        "/test/push/wigo/robot" to "/baseApi/promotion_second_hash/post",
        "/user/getLevelInfoPostV2" to "/baseApi/res/diagram_max_string/search",
        "/showRoomUser/liveGiveUserGifts" to "/baseApi/v2/harddisk_top_map/post",
        "/equity/user" to "/baseApi/res/memory_last_list/get",
        "/coin/register/freePostV2" to "/baseApi/res/message_average_group/patch",
        "/coin/callCardWithEx/receivePostV2" to "/baseApi/v1/stock_min_item/update",
        "/video-call/match/soul" to "/baseApi/v3/promotion_second_key/get",
        "/coin/presented/getPostV2" to "/baseApi/v3/stock_second_item/update",
        "/region/getRegionConfig" to "/baseApi/v0/gift_minimum_list/search",
        "/user/getUserListOnlineStatusJoinExtra" to "/baseApi/v1/user_maximum_list/add",
        "/showRoomUser/data" to "/baseApi/v3/product_second_json/delete",
        "/scriptKill/listMembers" to "/baseApi/v2/promotion_first_array/set",
        "/coin/payChannel/getPostV2" to "/baseApi/v0/agent_second_map/search",
        "/info-flow/user/list" to "/baseApi/v1/diagram_minimum_rank/get",
        "/config/getDefaultAvatarInfoPostV2" to "/baseApi/v3/gift_max_map/post",
        "/user/getAllFriendsList" to "/baseApi/v3/followlist_last_map/get",
        "/gift/live/list" to "/baseApi/v0/link_first_key/get",
        "/user/functionInfoPostV2" to "/baseApi/coin_min_array/search",
        "/video-call/meetList" to "/baseApi/v3/goods_min_map/add",
        "/activity/getRouletteWinner" to "/baseApi/v2/meminfo_max_table/upload",
        "/LiveInteractiveGift/template/list" to "/baseApi/v1/coin_top_item/list",
        "/test/field/decode/localCache" to "/baseApi/resource/config_min_result/remove",
        "/security/accountPassword" to "/baseApi/v1/goods_min_json/set",
        "/broadcaster/dashboardPostV2" to "/baseApi/resource/memory_average_table/set",
        "/broadcaster/warnContents" to "/baseApi/v0/disk_maximum_rank/search",
        "/review/cherry/video-flowsPostV2" to "/baseApi/v3/report_first_list/patch",
        "/dressup/user/on" to "/baseApi/v2/config_top_json/remove",
        "/review/ios/videoPostV2" to "/baseApi/resource/money_top_rank/download",
        "/livekit/user/bind" to "/baseApi/v0/money_second_array/list",
        "/user/backpack/latestExpireRechargeCard" to "/baseApi/v1/document_value/post",
        "/user/consumed/increase" to "/baseApi/v3/promotion_average_item/upload",
        "/config/sys/noticePostV2" to "/baseApi/v2/agent_last_json/del",
        "/skiResort/getInfoFlowAndFriendNumPostV2" to "/baseApi/v0/agent_average_key/delete",
        "/user/RecommendBroadcaster" to "/baseApi/product_second_string/remove",
        "/user/getUsernameAndAvatarPostV2" to "/baseApi/v0/device_max_group/download",
        "/user/rank/searchCoupleV2" to "/baseApi/v2/user_average_json/post",
        "/pipline/contentList" to "/baseApi/resource/coin_maximum_list/update",
        "/review/albumCoverList" to "/baseApi/v1/meminfo_min_json/upload",
        "/video-call/channel/join" to "/baseApi/res/followlist_max_rank/upload",
        "/gift/activity-only/showList" to "/baseApi/resource/document_maximum_string/set",
        "/soulVideoCall/v2/hangUp" to "/baseApi/v2/message_first_json/download",
        "/showVideo/getBroadcasterVideo" to "/baseApi/v0/config_first_price/del",
        "/mg-room/admin/close" to "/baseApi/v2/meminfo_maximum_rank/post",
        "/user/unlockMedia" to "/baseApi/v0/money_first_item/update",
        "/coin/reviewModeConsume" to "/baseApi/v2/userorder_min_rank/update",
        "/videoCallV2/channel/create" to "/baseApi/stock_average_table/patch",
        "/user/levelPower/updateSpecialEffects" to "/baseApi/res/product_average_group/search",
        "/LiveInteractiveGift/del" to "/baseApi/v3/document_second_key/search",
        "/dressup/user/sets" to "/baseApi/v3/memory_minimum_group/download",
        "/user/unfriendSoul" to "/baseApi/userinfo_top_array/set",
        "/user/guideInviteCode/bind" to "/baseApi/res/report_key/get",
        "/broadcaster/group/getMessageNumPostV2" to "/baseApi/v3/promotion_max_table/get",
        "/user/backpack/list" to "/baseApi/v1/coin_top_price/patch",
        "/activity/iconEntry/list" to "/baseApi/v0/memory_top_list/set",
        "/video-call/auto-call-record/updateStatus" to "/baseApi/v2/link_last_array/download",
        "/hit/ascribeRecordReqs" to "/baseApi/device_maximum_value/patch",
        "/review/ios/video" to "/baseApi/v3/config_top_result/set",
        "/review/mediaWall" to "/baseApi/diagram_second_value/delete",
        "/user/getUserInfo" to "/baseApi/v3/game_last_array/list",
        "/skiResort/comment/publish" to "/baseApi/v0/userorder_max_key/remove",
        "/user/saveUserInfo" to "/baseApi/msg_second_string/patch",
        "/config/switchPostV2" to "/baseApi/v3/user_max_rank/patch",
        "/common/user/idle" to "/baseApi/v2/stock_key/get",
        "/user/alias/listPostV2" to "/baseApi/res/doc_average_result/upload",
        "/mg-room/mic/up" to "/baseApi/res/diagram_maximum_list/upload",
        "/user/new-subscription/popupPostV2" to "/baseApi/link_average_price/add",
        "/broadcaster/guardianRank" to "/baseApi/v2/document_min_array/get",
        "/test/testListAccess" to "/baseApi/res/doc_max_map/remove",
        "/user/getGuardianCoin" to "/baseApi/mem_result/set",
        "/unity/user/switchMatchGender" to "/baseApi/v1/stock_min_key/download",
        "/soul/user/switchImTipShow" to "/baseApi/v3/meminfo_first_group/post",
        "/activity/giftUpgradeRankData" to "/baseApi/v1/network_max_item/list",
        "/risk/shuMei/callback" to "/baseApi/v0/product_top_list/add",
        "/coin/payChannel/get" to "/baseApi/v2/stock_last_item/remove",
        "/coin/recharge/payment/ipa" to "/baseApi/document_list/update",
        "/common/appLog/trigger" to "/baseApi/v1/message_second_value/get",
        "/user/guardianBannerPostV2" to "/baseApi/v3/message_value/get",
        "/soulVideoCall/duration" to "/baseApi/v0/msg_minimum_item/update",
        "/broadcaster/task/receiveBonus" to "/baseApi/res/userinfo_second_hash/update",
        "/gift/askGiftPic/giftListPostV2" to "/baseApi/resource/harddisk_first_hash/download",
        "/broadcaster/groupMessageConfig" to "/baseApi/v3/device_top_rank/delete",
        "/live/preLoad/resources/list" to "/baseApi/v3/meminfo_average_result/search",
        "/videoCallV2/duration" to "/baseApi/v1/report_max_group/del",
        "/info-flow/user/infoFlowCount" to "/baseApi/v3/game_value/add",
        "/broadcaster/dayStatistic" to "/baseApi/v0/meminfo_top_item/del",
        "/user/addFriendInReview" to "/baseApi/network_second_string/search",
        "/broadcaster/userEvaluate/submit" to "/baseApi/resource/game_average_table/update",
        "/soul/user/switchMatchGender" to "/baseApi/res/disk_max_string/download",
        "/broadcaster/stickTop" to "/baseApi/v2/activity_max_list/search",
        "/scriptKill/join" to "/baseApi/v0/money_second_item/upload",
        "/video-call/agora/rtmToken" to "/baseApi/v0/document_top_group/post",
        "/test/sentinel" to "/baseApi/v2/game_map/del",
        "/coin/giveIndiaCallCard" to "/baseApi/res/network_maximum_result/delete",
        "/user/instruct/evaluation" to "/baseApi/res/mem_min_json/post",
        "/user/relationsCounterPostV2" to "/baseApi/v3/config_minimum_value/patch",
        "/live/activity/confessionWall/confess" to "/baseApi/resource/order_maximum_group/add",
        "/config/submitInitData" to "/baseApi/v0/followlist_average_list/patch",
        "/user/user-recharge-info/postV2" to "/baseApi/v1/userorder_minimum_value/list",
        "/msg/getActivityMsgPostV2" to "/baseApi/gift_average_item/set",
        "/showRoomUser/getUserSensitiveWords" to "/baseApi/v2/msg_top_json/upload",
        "/showRoom/prepare" to "/baseApi/v1/doc_top_string/download",
        "/coin/h5-recharge/create" to "/baseApi/resource/promotion_top_rank/download",
        "/shortLink/media/search" to "/baseApi/v0/money_maximum_hash/patch",
        "/info-flow/likeComment/updateStatus" to "/baseApi/resource/config_first_group/patch",
        "/user/isSendGift2PostV2" to "/baseApi/v2/disk_average_value/add",
        "/user/isSendImToPostV2" to "/baseApi/v1/diagram_maximum_map/get",
        "/showRoomGoldContest/refreshRank" to "/baseApi/v0/goods_last_result/upload",
        "/mg-room/boss/exit" to "/baseApi/harddisk_key/update",
        "/report/complain/insertRecordSoul" to "/baseApi/v1/harddisk_group/remove",
        "/user/freeAddFriendCount" to "/baseApi/v1/link_top_result/post",
        "/report/complain/blockList" to "/baseApi/v2/goods_min_hash/patch",
        "/showRoom/liveTaskInfoPostV2" to "/baseApi/promotion_first_hash/update",
        "/showRoomTestEnv/uploadVideo" to "/baseApi/resource/disk_maximum_table/get",
        "/activity/getRouletteEntrance" to "/baseApi/resource/mem_average_group/update",
        "/activity/getTopUserInfo" to "/baseApi/v0/goods_last_hash/patch",
        "/activity/valentine/cupidWheelInvitation" to "/baseApi/v3/user_min_value/upload",
        "/broadcaster/wall/comprehendLanguage" to "/baseApi/v0/gift_top_price/update",
        "/user/incentive/has" to "/baseApi/resource/goods_min_result/update",
        "/test/field/encrypt" to "/baseApi/resource/followlist_first_group/list",
        "/user/avatar/searchPostV2" to "/baseApi/disk_second_hash/search",
        "/info-flow/Gifts/giveUserAlbumGifts" to "/baseApi/v0/link_rank/patch",
        "/callback/agora/message/inform" to "/baseApi/res/disk_first_list/patch",
        "/game/lingxian/getUserInfo" to "/baseApi/resource/meminfo_min_result/set",
        "/broadcaster/invitation/info/v2PostV2" to "/baseApi/v3/coin_last_rank/update",
        "/gift/askGiftPic/giftList" to "/baseApi/v2/order_min_json/post",
        "/info-flow/updateInfoFlowAlbum" to "/baseApi/v3/link_rank/patch",
        "/LiveInteractiveGift/list" to "/baseApi/res/link_second_rank/patch",
        "/video-call/broadcaster/income" to "/baseApi/res/message_first_value/search",
        "/mg-room/userInfoPostV2" to "/baseApi/v0/user_minimum_rank/search",
        "/user/baseinfo/search" to "/baseApi/v1/report_max_string/del",
        "/config/getStrategyForH5" to "/baseApi/device_top_group/update",
        "/showRoomUser/close" to "/baseApi/v3/document_first_table/remove",
        "/showRoomUser/editNotice" to "/baseApi/v0/mem_top_group/del",
        "/security/existsByEmail" to "/baseApi/v3/coin_average_table/add",
        "/config/getIOSConfig" to "/baseApi/v3/followlist_minimum_item/remove",
        "/broadcaster/instruct/confirm" to "/baseApi/res/gift_average_key/delete",
        "/LiveInteractiveGift/addItem" to "/baseApi/v1/memory_last_result/del",
        "/IMBack/sendConfirm" to "/baseApi/resource/product_min_result/list",
        "/test/images" to "/baseApi/res/userorder_maximum_json/patch",
        "/activity/banner" to "/baseApi/product_second_rank/update",
        "/broadcaster/FAQ/get" to "/baseApi/memory_maximum_json/delete",
        "/callback/agora/cloudPlayer" to "/baseApi/v2/memory_average_group/upload",
        "/soulVideoCall/broadcaster/match/exit" to "/baseApi/v2/goods_maximum_hash/post",
        "/api/channel/rechargeOrderCreate" to "/baseApi/v3/harddisk_top_string/search",
        "/video-call/matchPopup" to "/baseApi/resource/goods_min_value/download",
        "/activity/getActivityList" to "/baseApi/v2/msg_last_list/search",
        "/api/channel/rechargeOrderStatusChange" to "/baseApi/v2/network_minimum_list/post",
        "/gift/live/list/v2" to "/baseApi/link_first_key/delete",
        "/broadcaster/invite/link" to "/baseApi/res/harddisk_min_price/remove",
        "/scriptKill/listMembersPostV2" to "/baseApi/v0/disk_max_value/del",
        "/user/addFriendSoul" to "/baseApi/v2/followlist_result/list",
        "/showRoomUser/open" to "/baseApi/v3/config_average_map/update",
        "/mg-room/admin/open" to "/baseApi/game_top_array/add",
        "/showRoom/audiencesPostV2" to "/baseApi/v1/money_first_group/set",
        "/showRoom/room/viewers" to "/baseApi/resource/money_min_value/delete",
        "/broadcaster/invitation/info" to "/baseApi/v2/order_second_key/search",
        "/showRoom/joinSession" to "/baseApi/v2/network_result/delete",
        "/coin/goods/broadcasterInvitation" to "/baseApi/v1/mem_minimum_value/delete",
        "/coin/goods/getPromotion" to "/baseApi/goods_maximum_json/delete",
        "/showRoom/updateCoinTaskAnnounce" to "/baseApi/v1/doc_top_value/patch",
        "/shortLink/media/searchPostV2" to "/baseApi/v2/mem_minimum_json/post",
        "/mg-room/coinTaskInfo" to "/baseApi/v2/harddisk_hash/upload",
        "/user/club/config" to "/baseApi/v2/user_average_string/upload",
        "/user/delFriendInReview" to "/baseApi/v1/diagram_second_value/search",
        "/broadcaster/broadcasterGrade" to "/baseApi/v0/money_minimum_table/download",
        "/user/broadcasterVisitorsPostV2" to "/baseApi/v3/goods_first_hash/set",
        "/system/timestamp" to "/baseApi/v2/user_value/add",
        "/activity/user-active/scoreReceive" to "/baseApi/res/money_top_rank/remove",
        "/showRoomUser/smallWindow" to "/baseApi/v1/memory_top_list/set",
        "/mg-room/data" to "/baseApi/v1/link_last_key/get",
        "/coin/paypal/cancel" to "/baseApi/v0/stock_min_value/update",
        "/coin/register/free" to "/baseApi/v2/promotion_hash/delete",
        "/liveUser/friend/add" to "/baseApi/v2/config_average_string/del",
        "/test/testSetAccess" to "/baseApi/res/msg_average_result/upload",
        "/user/updateMedia" to "/baseApi/v3/doc_last_group/download",
        "/report/complain/removeBlock" to "/baseApi/msg_first_array/remove",
        "/info-flow/user/detail" to "/baseApi/v1/money_min_key/delete",
        "/review/ios/stream" to "/baseApi/coin_minimum_string/update",
        "/info-flow/infoFlowAlbumPage" to "/baseApi/resource/user_last_hash/patch",
        "/broadcaster/groupMessageSend" to "/baseApi/msg_table/add",
        "/dressup/center/buy" to "/baseApi/v2/product_max_result/del",
        "/popUp/byRecharge" to "/baseApi/v1/link_top_value/search",
        "/coin/vshot-recharge/recharge" to "/baseApi/v1/document_average_item/upload",
        "/activity/getRouletteRewardRecord" to "/baseApi/v1/message_average_array/delete",
        "/info-flow/createInfoFlowAlbum" to "/baseApi/v2/diagram_first_rank/upload",
        "/broadcaster/instruct/update" to "/baseApi/resource/message_maximum_map/get",
        "/activity/lastActivityAndIds" to "/baseApi/v0/report_maximum_list/patch",
        "/gift/limitVIPGift/notPurchased" to "/baseApi/stock_last_price/upload",
        "/user/invitation/codeAndAvatar" to "/baseApi/v0/document_minimum_key/del",
        "/scriptKill/list" to "/baseApi/resource/doc_top_string/search",
        "/gift/limitVIPGift/login" to "/baseApi/v3/promotion_min_json/patch",
        "/video-call/meetCallClick" to "/baseApi/v1/harddisk_min_json/search",
        "/showRoom/roomViewersPostV2" to "/baseApi/v1/money_last_string/remove",
        "/test/field/encrypt/localCache" to "/baseApi/v2/config_average_key/add",
        "/info-flow/comment/list" to "/baseApi/v3/mem_min_json/delete",
        "/user/favorite/isFavorite" to "/baseApi/v3/device_maximum_result/delete",
        "/user/guardianBanner" to "/baseApi/v0/doc_key/set",
        "/user/getUserInfoForH5PostV2" to "/baseApi/v1/order_value/set",
        "/gift/maskConfigPostV2" to "/baseApi/resource/message_last_key/upload",
        "/video-call/clearRulePostV2" to "/baseApi/v2/report_key/add",
        "/gift/maskConfig" to "/baseApi/v1/harddisk_average_rank/list",
        "/user/consumed/postV2" to "/baseApi/v1/user_average_map/list",
        "/user/selectGuildWithdrawStatusPostV2" to "/baseApi/v2/disk_top_value/download",
        "/broadcaster/exposure" to "/baseApi/v0/document_average_list/add",
        "/video-call/match/unity" to "/baseApi/v2/document_average_value/set",
        "/broadcaster/IMIncentive/list" to "/baseApi/v1/harddisk_minimum_rank/upload",
        "/rankActivity/herRankNumber/forBanner" to "/baseApi/network_maximum_hash/remove",
        "/broadcaster/club/priceList" to "/baseApi/v0/product_min_json/remove",
        "/user/levelPower/noticeConfirm" to "/baseApi/v1/activity_top_string/search",
        "/callback/agora/snapshot" to "/baseApi/promotion_first_json/set",
        "/security/getOTPPostV2" to "/baseApi/res/coin_average_string/search",
        "/user/getUserCoins" to "/baseApi/res/user_second_rank/patch",
        "/showRoomUser/useGiftWall" to "/baseApi/v2/document_average_array/search",
        "/review/zodiac/broadcasterList" to "/baseApi/msg_max_table/patch",
        "/user/isFollow" to "/baseApi/v0/goods_max_price/search",
        "/user/getChargeMediaForH5" to "/baseApi/v3/mem_minimum_price/search",
        "/shortLink/target" to "/baseApi/resource/promotion_top_string/patch",
        "/rankActivity/rankTopOne" to "/baseApi/v0/product_last_rank/search",
        "/scriptKill/joinGroup" to "/baseApi/v0/report_maximum_item/update",
        "/info-flow/detail" to "/baseApi/v3/coin_top_key/get",
        "/broadcaster/settlementCyclePostV2" to "/baseApi/v0/gift_array/add",
        "/live/activity/confessionWall/page" to "/baseApi/v3/disk_first_rank/patch",
        "/unity/user/getUnityUserEvaluation" to "/baseApi/v3/message_max_table/del",
        "/coin/callCardWithEx/receive" to "/baseApi/v3/msg_maximum_json/get",
        "/mg-room/userInfo" to "/baseApi/v1/gift_minimum_map/update",
        "/user/rongcloud/token" to "/baseApi/resource/activity_max_map/delete",
        "/user/getBroadcasterStatistics" to "/baseApi/link_max_rank/download",
        "/liveChat/content/submit" to "/baseApi/report_average_group/upload",
        "/review/cherry/top-picsPostV2" to "/baseApi/money_average_json/post",
        "/test/testAutoCall/app" to "/baseApi/v0/doc_maximum_result/post",
        "/user/rongcloud/tokenPostV2" to "/baseApi/v2/meminfo_average_list/add",
        "/showRoom/configPostV2" to "/baseApi/resource/meminfo_map/add",
        "/user/s3/uploadUrl" to "/baseApi/v0/stock_top_rank/post",
        "/user/saveMoney/statisticPostV2" to "/baseApi/v3/disk_min_value/patch",
        "/review/cherry/info-flows" to "/baseApi/resource/doc_first_list/remove",
        "/broadcaster/exchange/rate" to "/baseApi/v0/message_key/delete",
        "/user/oss/policyPostV2" to "/baseApi/res/gift_first_list/search",
        "/showRoomUser/wigoDiamond" to "/baseApi/resource/report_second_group/post",
        "/broadcaster/register/recommend" to "/baseApi/v0/diagram_min_string/delete",
        "/game/broadcaster/activity/send" to "/baseApi/res/stock_minimum_string/patch",
        "/user/alias/list" to "/baseApi/money_maximum_key/get",
        "/user/coins" to "/baseApi/res/money_last_key/search",
        "/user/saveMoney/del" to "/baseApi/v1/diagram_result/upload",
        "/user/game/signPostV2" to "/baseApi/v2/promotion_max_group/list",
        "/activity/canRechageTagShow" to "/baseApi/v3/money_second_hash/remove",
        "/showRoom/roomDayRankTopPostV2" to "/baseApi/v1/config_last_hash/search",
        "/wigo/info-flow/comment-publish" to "/baseApi/device_first_price/add",
        "/pipline/contentListPostV2" to "/baseApi/v1/userorder_average_key/patch",
        "/user/isSendImTo" to "/baseApi/res/doc_maximum_group/get",
        "/user/backpack/listAvailablePostV2" to "/baseApi/res/harddisk_item/delete",
        "/broadcaster/topGifters/searchPostV2" to "/baseApi/v2/disk_first_key/upload",
        "/broadcaster/autoCall/autoCallStrategyConfigPostV2" to "/baseApi/v3/game_last_key/post",
        "/test/get-token" to "/baseApi/v3/goods_average_value/download",
        "/game/gameEntry" to "/baseApi/resource/agent_average_rank/get",
        "/user/locationPostV2" to "/baseApi/v3/followlist_average_result/update",
        "/soulVideoCall/broadcaster/income" to "/baseApi/v0/user_second_result/patch",
        "/showRoomUser/room/rank" to "/baseApi/v0/userinfo_maximum_table/post",
        "/test/test/block_AccountTest" to "/baseApi/v1/userorder_max_string/update",
        "/soulVideoCall/user/callResult" to "/baseApi/resource/memory_string/del",
        "/user/rearCamera/config" to "/baseApi/v1/msg_last_value/search",
        "/live/activity/confessionWall/delUserIds" to "/baseApi/v1/config_last_map/set",
        "/broadcaster/IMIncentive/receiveCoins" to "/baseApi/v0/promotion_average_group/update",
        "/gift/activity-only/showListPostV2" to "/baseApi/device_last_key/download",
        "/user/consumed/get" to "/baseApi/resource/config_maximum_list/download",
        "/liveSearch/user/list" to "/baseApi/v2/gift_last_key/del",
        "/user/guideIn/info" to "/baseApi/resource/config_average_list/search",
        "/test/reject-call/freeze" to "/baseApi/v0/device_min_price/get",
        "/security/oauth" to "/baseApi/userorder_second_group/download",
        "/gift/broadcasterMask/findLockInfo" to "/baseApi/gift_second_result/list",
        "/info-flow/inform/list" to "/baseApi/v1/config_first_json/add",
        "/user/instruct/list" to "/baseApi/v0/stock_minimum_array/update",
        "/broadcaster/wall/searchForNearby" to "/baseApi/v3/user_max_string/del",
        "/review/ios/streamPostV2" to "/baseApi/v1/activity_min_hash/patch",
        "/push/config" to "/baseApi/resource/order_last_result/search",
        "/liveUser/send-im/check" to "/baseApi/v0/meminfo_first_table/add",
        "/equity/user/level" to "/baseApi/resource/disk_top_item/delete",
        "/soul/security/call/screenShotUpload" to "/baseApi/res/money_second_map/del",
        "/video-call/zego/rtcToken" to "/baseApi/v0/network_max_string/post",
        "/mg-room/mic/up-down/confirm" to "/baseApi/v0/agent_max_table/download",
        "/hit/webUserAgentHitForH5" to "/baseApi/v2/harddisk_minimum_hash/add",
        "/user/updateBatchAvatar" to "/baseApi/v1/gift_array/remove",
        "/showRoomUser/status" to "/baseApi/resource/config_max_table/upload",
        "/showRoom/audiences" to "/baseApi/v2/agent_second_hash/list",
        "/showRoom/allLiveTaskInfo" to "/baseApi/message_result/add",
        "/videoCallV2/broadcaster/income" to "/baseApi/v1/gift_second_list/list",
        "/test/decrypt" to "/baseApi/resource/product_min_json/remove",
        "/user/getUserInfoForH5" to "/baseApi/v3/money_value/update",
        "/config/getDefaultAvatarInfo" to "/baseApi/v1/coin_top_price/update",
        "/security/exceptionCodePostV2" to "/baseApi/res/doc_top_map/list",
        "/dressup/center/sets" to "/baseApi/v2/coin_group/search",
        "/user/unlockMask" to "/baseApi/v2/gift_minimum_key/del",
        "/activity/getActivityRankV2" to "/baseApi/v3/memory_minimum_group/delete",
        "/user/getUserInfoByUserNo" to "/baseApi/resource/activity_top_list/get",
        "/broadcaster/wall/lowPrice" to "/baseApi/res/harddisk_average_string/list",
        "/user/incentive/get/detail" to "/baseApi/userinfo_first_table/search",
        "/user/updateBigoUid" to "/baseApi/game_maximum_group/get",
        "/coin/paypal/capture" to "/baseApi/v2/money_average_group/add",
        "/showRoomTestEnv/uploadPreSignedUrl" to "/baseApi/v1/agent_top_list/post",
        "/event-active/list" to "/baseApi/gift_top_group/del",
        "/showRoom/getUserMuteStatusPostV2" to "/baseApi/res/meminfo_minimum_map/upload",
        "/mg-room/admin/roomTag" to "/baseApi/v1/message_average_hash/delete",
        "/broadcaster/mediaScore/descPostV2" to "/baseApi/resource/disk_top_price/del",
        "/broadcaster/checkBroadcasterConfig" to "/baseApi/resource/stock_group/remove",
        "/coin/goods/listPostV2" to "/baseApi/v1/mem_maximum_rank/download",
        "/user/getFriendsListPostV2" to "/baseApi/v1/agent_max_item/delete",
        "/config/getStrategyForH5PostV2" to "/baseApi/resource/game_top_string/update",
        "/risk/broadcaster/review" to "/baseApi/res/msg_map/update",
        "/soul/security/im/check" to "/baseApi/money_minimum_price/set",
        "/coin/recharge/broadcasterInvitationPostV2" to "/baseApi/memory_second_string/search",
        "/activity/valentine/cupidWheel" to "/baseApi/v3/userinfo_max_price/set",
        "/user/bindingEmail" to "/baseApi/res/promotion_minimum_rank/set",
        "/security/emailRegister" to "/baseApi/v0/link_last_price/search",
        "/info-flow/getRandomUserList" to "/baseApi/v2/promotion_min_rank/get",
        "/broadcaster/group/message/upload" to "/baseApi/v1/money_array/del",
        "/gift/blindbox/rank" to "/baseApi/v2/game_min_list/post",
        "/test/recharge-success-trigger" to "/baseApi/resource/game_maximum_item/delete",
        "/rankActivity/publicity" to "/baseApi/v0/diagram_average_group/post",
        "/review/cherry/info-flowsPostV2" to "/baseApi/res/report_json/get",
        "/security/existsByEmailPostV2" to "/baseApi/v1/device_min_json/del",
        "/showRoom/broadcastersPostV2" to "/baseApi/v0/memory_top_list/update",
        "/config/getStrategyPostV2" to "/baseApi/res/stock_min_result/del",
        "/user/rank/searchForH5" to "/baseApi/v2/message_second_json/list",
        "/mg-room/mic/kick" to "/baseApi/v3/game_max_value/get",
        "/user/saveMoney/listPostV2" to "/baseApi/v0/money_top_value/remove",
        "/broadcaster/group/getMessageNum" to "/baseApi/res/userinfo_max_price/post",
        "/activity/checkin-active/data" to "/baseApi/v1/user_top_price/delete",
        "/gift/activity-only/askList" to "/baseApi/v3/money_top_hash/delete",
        "/broadcaster/newUserTask/list" to "/baseApi/resource/money_min_hash/update",
        "/video-call/autoCallV2/hangUp" to "/baseApi/v0/money_maximum_json/patch",
        "/user/strongGuide/configV2" to "/baseApi/res/doc_last_list/search",
        "/showRoomUser/liveGiftListThisSession" to "/baseApi/res/meminfo_top_string/get",
        "/test/niubi" to "/baseApi/v1/disk_average_array/post",
        "/config/sys/notice" to "/baseApi/v1/doc_maximum_price/search",
        "/coin/giveIndiaCallCardNum" to "/baseApi/v1/userorder_top_group/patch",
        "/video-call/matchReal" to "/baseApi/v2/coin_second_array/patch",
        "/common/user/idlePostV2" to "/baseApi/v2/user_list/update",
        "/broadcaster/dashboard" to "/baseApi/resource/stock_minimum_array/add",
        "/gift/blindbox/rank/pop" to "/baseApi/v1/doc_max_result/search",
        "/config/getUserAuth" to "/baseApi/resource/game_minimum_map/update",
        "/soulVideoCall/v2/channel/join" to "/baseApi/v3/message_second_list/get",
        "/coin/daily-login/taskList" to "/baseApi/v1/stock_second_map/upload",
        "/user/unfriend" to "/baseApi/v1/network_top_string/update",
        "/videoCallV2/pickUp" to "/baseApi/res/game_first_group/delete",
        "/hit/askForGiftPic/hit" to "/baseApi/resource/user_top_map/list",
        "/skiResort/getInfoFlowAndFriendNum" to "/baseApi/v1/device_last_json/get",
        "/activity/activityHonorWall" to "/baseApi/v2/doc_second_result/remove",
        "/videoCallV2/channel/join" to "/baseApi/v1/link_second_hash/set",
        "/info-flow/publish" to "/baseApi/harddisk_maximum_rank/del",
        "/mg-room/boss/enter" to "/baseApi/v2/diagram_first_json/get",
        "/broadcaster/getCustomizePrice" to "/baseApi/network_second_array/remove",
        "/user/getUserListOnlineStatusForH5PostV2" to "/baseApi/v1/network_value/remove",
        "/game/slotMachine/radioInfo" to "/baseApi/gift_minimum_result/patch",
        "/config/sendDelAccount" to "/baseApi/goods_min_map/del",
        "/gift/jackpot/award/info" to "/baseApi/resource/diagram_max_map/update",
        "/showRoom/broadcasters" to "/baseApi/res/link_maximum_value/list",
        "/config/getLangValue" to "/baseApi/v0/order_min_hash/search",
        "/gift/specialList" to "/baseApi/v1/msg_first_map/get",
        "/coin/goods/searchOverMaxGear" to "/baseApi/v3/message_minimum_price/list",
        "/user/cancelSpecialFollow" to "/baseApi/network_second_rank/list",
        "/showRoom/userLiveLevel/shining" to "/baseApi/v1/doc_average_price/upload",
        "/video-call/clearRule" to "/baseApi/v2/product_second_array/patch",
        "/user/broadcasterRelations" to "/baseApi/v1/harddisk_top_key/add",
        "/video-call/flash/chat" to "/baseApi/v1/userorder_first_rank/search",
        "/broadcaster/network/getQualityPostV2" to "/baseApi/v0/userinfo_first_key/delete",
        "/video-call/robotCallClick" to "/baseApi/v1/coin_min_group/download",
        "/skiResort/list" to "/baseApi/userorder_average_hash/del",
        "/test/autoCallAddPool" to "/baseApi/res/promotion_average_rank/list",
        "/mg-room/mic/up/expedite/info" to "/baseApi/v2/msg_last_price/get",
        "/user/getUsernameAndAvatar" to "/baseApi/v1/coin_last_table/download",
        "/test/new-user-task-test" to "/baseApi/v3/document_max_group/del",
        "/wigo/info-flow/list" to "/baseApi/v0/link_min_key/set",
        "/gift/listPostV2" to "/baseApi/resource/order_minimum_price/delete",
        "/user/createImSessionRelate" to "/baseApi/product_average_rank/list",
        "/gift/jackpot/award/record" to "/baseApi/res/harddisk_second_map/get",
        "/user/user-recharge-info" to "/baseApi/v3/userorder_last_price/remove",
        "/user/backpack/gifts" to "/baseApi/v1/document_average_map/del",
        "/video-call/gora/rtmTokenPostV2" to "/baseApi/res/message_maximum_string/del",
        "/showRoom/close" to "/baseApi/res/diagram_average_item/search",
        "/info-flow/delete" to "/baseApi/resource/msg_first_array/post",
        "/security/isValidToken" to "/baseApi/userorder_minimum_item/upload",
        "/user/relationsCounterToUser" to "/baseApi/v1/order_first_string/set",
        "/user/switchNotDisturb" to "/baseApi/user_top_map/download",
        "/user/strongGuide/config" to "/baseApi/v1/disk_maximum_value/add",
        "/mg-room/enter" to "/baseApi/resource/followlist_first_result/search",
        "/gift/specialEffectsList" to "/baseApi/v2/product_first_price/update",
        "/test/img/policy" to "/baseApi/v2/product_string/list",
        "/user/rank/search" to "/baseApi/v2/report_minimum_item/remove",
        "/broadcaster/videoStream/search" to "/baseApi/v2/meminfo_top_hash/delete",
        "/broadcaster/IMIncentive/receiveCoinsPostV2" to "/baseApi/v1/stock_second_list/add",
        "/activity/valentine/cupidWheelInvitationList" to "/baseApi/res/network_top_string/download",
        "/callback/paypal/webhook" to "/baseApi/v3/meminfo_top_hash/post",
        "/rankActivity/seatRankInfo" to "/baseApi/v2/network_maximum_array/search",
        "/callback/google" to "/baseApi/v1/user_maximum_string/update",
        "/mg-room/seat/details" to "/baseApi/v2/mem_min_array/post",
        "/video-call/hangUp" to "/baseApi/resource/harddisk_average_json/del",
        "/broadcaster/wall/searchForH5" to "/baseApi/res/message_price/remove",
        "/user/getUserOnlineStatusPostV2" to "/baseApi/v1/doc_last_value/remove",
        "/showRoom/coinTaskInfoPostV2" to "/baseApi/res/followlist_average_price/delete",
        "/showRoom/muteUser" to "/baseApi/v1/game_top_value/upload",
        "/showRoomUser/userLiveLeveSwitch" to "/baseApi/v3/agent_last_table/remove",
        "/user/getBroadcasterCoinsPostV2" to "/baseApi/v3/game_last_price/update",
        "/video-call/broadcaster/online" to "/baseApi/v3/goods_maximum_value/set",
        "/coin/daily-login/receiveTaskBonus" to "/baseApi/msg_first_list/del",
        "/user/instruct/pay" to "/baseApi/v0/activity_average_list/update",
        "/broadcaster/userEvaluate/ClubUser" to "/baseApi/resource/meminfo_second_rank/upload",
        "/wigo/info-flow/like" to "/baseApi/resource/memory_minimum_array/list",
        "/video-call/channel/create" to "/baseApi/v0/agent_second_hash/remove",
        "/soulVideoCall/userEvaluate/evaluateList" to "/baseApi/resource/activity_minimum_group/del",
        "/liveRoom/dataPostV2" to "/baseApi/v2/diagram_map/upload",
        "/user/getUserMaskList" to "/baseApi/v0/config_minimum_item/upload",
        "/broadcaster/exchange/ratePostV2" to "/baseApi/activity_group/upload",
        "/broadcaster/banner/list" to "/baseApi/v2/userorder_table/remove",
        "/game/slotMachine/cleanFlow" to "/baseApi/res/message_average_value/add",
        "/user/favorite/addCancel" to "/baseApi/v2/report_max_group/search",
        "/video-call/match/clean/historyPostV2" to "/baseApi/resource/order_last_list/add",
        "/soul/security/unBlock/pay" to "/baseApi/res/money_average_value/list",
        "/coin/recharge/create" to "/baseApi/config_average_value/download",
        "/user/setSpecialFollow" to "/baseApi/v0/doc_min_group/add",
        "/video-call/match/v2" to "/baseApi/config_first_json/remove",
        "/activity/getActivityRank" to "/baseApi/v2/meminfo_last_list/post",
        "/coin/tppRecharge/info" to "/baseApi/v0/agent_min_price/get",
        "/info-flow/user/residueTimes" to "/baseApi/v2/userorder_maximum_value/delete",
        "/broadcaster/task/receiveBonusPostV2" to "/baseApi/resource/stock_last_hash/delete",
        "/common/user/password" to "/baseApi/v1/config_min_group/del",
        "/user/getBroadcasterExtraInfoForH5PostV2" to "/baseApi/product_maximum_map/search",
        "/security/register" to "/baseApi/v2/money_second_value/set",
        "/mg-room/mgRoomVideoCall" to "/baseApi/v2/report_last_string/get",
        "/config/getGuideInConfig" to "/baseApi/v2/userorder_min_table/del",
        "/user/getUserOnlineStatusForH5" to "/baseApi/doc_average_json/delete",
        "/live/gift/favorite/upsert" to "/baseApi/v1/followlist_maximum_array/patch",
        "/user/getBroadcasterExtraInfoForH5" to "/baseApi/v0/agent_first_json/upload",
        "/user/saveBasicInfo" to "/baseApi/v3/order_maximum_result/list",
        "/broadcaster/exclusive/weeklyTaskListPostV2" to "/baseApi/res/link_first_price/update",
        "/unity/user/getUnityMatchConfig" to "/baseApi/v2/activity_last_string/list",
        "/info-flow/topicList" to "/baseApi/resource/coin_second_list/delete",
        "/coin/h5-recharge/paymentType" to "/baseApi/resource/stock_min_result/update",
        "/coin/guide/goods/for-new" to "/baseApi/res/document_second_table/search",
        "/coin/presented/get" to "/baseApi/resource/device_string/remove",
        "/showRoom/getConsecutiveStreaming" to "/baseApi/v3/goods_maximum_rank/upload",
        "/broadcaster/network/getQuality" to "/baseApi/v1/document_min_result/post",
        "/coin/daily-login/taskListPostV2" to "/baseApi/v0/harddisk_maximum_array/search",
        "/config/getStrategy" to "/baseApi/res/doc_second_result/update",
        "/game/slotMachine/Lottery" to "/baseApi/v1/msg_minimum_map/update",
        "/broadcaster/instruct/updatePostV2" to "/baseApi/res/diagram_minimum_group/search",
        "/game/gameEntryPostV2" to "/baseApi/v1/stock_minimum_value/get",
        "/broadcaster/autoCall/trigger" to "/baseApi/v2/network_max_json/update",
        "/user/invitation/getCodePostV2" to "/baseApi/res/coin_average_key/post",
        "/video-call/duration" to "/baseApi/resource/disk_minimum_price/post",
        "/config/switch" to "/baseApi/res/gift_first_item/search",
        "/user/new-subscription/info" to "/baseApi/v2/product_average_price/update",
        "/showVideo/getIndiaConfig" to "/baseApi/harddisk_first_result/download",
        "/mg-room/boss/kick" to "/baseApi/v3/disk_second_group/update",
        "/info-flow/list" to "/baseApi/resource/report_min_price/patch",
        "/user/getUserListOnlineStatusForH5" to "/baseApi/resource/disk_first_rank/add",
        "/user/getBroadcasterExtraInfoPostV2" to "/baseApi/v1/report_max_result/del",
        "/gift/live/list/getPostV2" to "/baseApi/v1/money_minimum_item/post",
        "/liveRoom/endPage" to "/baseApi/resource/harddisk_minimum_item/get",
        "/robotScript/freeScript" to "/baseApi/v3/agent_second_result/remove",
        "/user/levelPower/serviceIM" to "/baseApi/res/doc_average_table/add",
        "/showRoom/room/rank/day/top3" to "/baseApi/resource/agent_max_array/upload",
        "/user/backpack/useAvatarFrame" to "/baseApi/resource/order_last_group/list",
        "/broadcaster/wall/classifiedSearch" to "/baseApi/resource/network_last_map/upload",
        "/showRoomUser/exception/feedback" to "/baseApi/v3/gift_first_item/add",
        "/broadcasterVideo/teach/languageListPostV2" to "/baseApi/v0/goods_second_list/download",
        "/user/getUserInfoPostV2" to "/baseApi/v1/stock_max_map/del",
        "/api/channel/withdrawOrderStatusChange" to "/baseApi/doc_second_group/set",
        "/broadcaster/instruct/list" to "/baseApi/v3/msg_minimum_hash/get",
        "/showRoomUser/upsertUserSpecialDay" to "/baseApi/v3/disk_minimum_hash/upload",
        "/coin/goods/getLastSpecialOfferV2" to "/baseApi/resource/agent_last_rank/download",
        "/security/time/proof" to "/baseApi/resource/order_rank/delete",
        "/user/mode/switch" to "/baseApi/v2/stock_last_item/add",
        "/userLiveLevel/equity" to "/baseApi/res/meminfo_minimum_item/upload",
        "/shortLink/get" to "/baseApi/link_last_hash/download",
        "/event-active/join" to "/baseApi/v3/message_minimum_rank/patch",
        "/live/questionnaire/fill" to "/baseApi/resource/report_min_map/add",
        "/config/getAppConfigPostV2" to "/baseApi/resource/network_top_hash/patch",
        "/user/getHobbiesPostV2" to "/baseApi/v3/document_last_group/set",
        "/user/searchImSessionRelate" to "/baseApi/memory_maximum_string/set",
        "/video-call/autoCallV2/event" to "/baseApi/v0/mem_table/upload",
        "/video-call/channel/stream/start" to "/baseApi/v2/msg_maximum_rank/set",
        "/gift/v2/list" to "/baseApi/v3/link_first_item/upload",
        "/coin/getIndiaCallCardInfo" to "/baseApi/res/coin_result/add",
        "/gift/giftCallGive" to "/baseApi/v1/stock_top_price/del",
        "/callback/zego/cloudPlayer" to "/baseApi/v2/money_last_price/update",
        "/mg-room/coinTaskInfoPostV2" to "/baseApi/v2/userinfo_minimum_table/set",
        "/config/getBeautyData" to "/baseApi/v3/link_top_table/search",
        "/config/getBeautyDataPostV2" to "/baseApi/v1/memory_first_hash/add",
        "/showRoomGoldContest/activityInfo" to "/baseApi/resource/stock_second_key/remove",
        "/user/isRechargeUsersPostV2" to "/baseApi/v0/network_list/set",
        "/showRoom/data" to "/baseApi/v0/user_value/list",
        "/test/batchSetLine" to "/baseApi/res/report_minimum_key/remove",
        "/coin/callCard/receivePostV2" to "/baseApi/v2/activity_minimum_table/download",
        "/user/getUserListOnlineStatusPostV2" to "/baseApi/v3/config_first_item/set",
        "/activity/getActivityRoundRank" to "/baseApi/v0/mem_first_key/add",
        "/video-call/channel/joinConfirm" to "/baseApi/v0/meminfo_last_rank/get",
        "/activity/canRouletteShow" to "/baseApi/resource/document_first_json/upload",
        "/gift/activity-only/askListPostV2" to "/baseApi/res/link_second_array/list",
        "/livekit/user/getUserInfo" to "/baseApi/res/diagram_first_hash/patch",
        "/info-flow/inform/findNumber" to "/baseApi/v3/userorder_maximum_json/search",
        "/showRoom/liveTaskInfo" to "/baseApi/v1/activity_last_key/post",
        "/game/broadcaster/activity/listPostV2" to "/baseApi/v1/goods_second_list/upload",
        "/callback/adjust" to "/baseApi/v3/config_top_string/update",
        "/gift/live/small" to "/baseApi/resource/coin_max_item/upload",
        "/video-call/popUp" to "/baseApi/meminfo_last_string/upload",
        "/user/levelPower/list" to "/baseApi/resource/userorder_max_map/download",
        "/soul/broadcaster/strategy" to "/baseApi/v1/config_max_result/search",
        "/user/updateAvatar" to "/baseApi/v3/document_top_list/patch",
        "/user/updateAgoraUid" to "/baseApi/v1/message_maximum_item/patch",
        "/test/jackpotLotteryDraw" to "/baseApi/v1/mem_first_map/remove",
        "/broadcaster/oneClickSayHi" to "/baseApi/userorder_maximum_hash/delete",
        "/info-flow/infoFlowAlbumList" to "/baseApi/v2/mem_average_key/remove",
        "/broadcaster/onCallStrategyConfig" to "/baseApi/v0/order_minimum_price/set",
        "/user/new-subscription/popup" to "/baseApi/v2/device_max_key/search",
        "/msg/match/question/list" to "/baseApi/v2/network_second_key/list",
        "/broadcaster/visitUserDetail" to "/baseApi/document_average_price/list",
        "/security/customCode" to "/baseApi/v2/disk_max_table/remove"
    )

    // 生成32位密钥
    private fun generateKey(): String {
        val baseKey = Constant.getBaseUrl()
        return if (baseKey.length >= 32) {
            baseKey.substring(0, 32)
        } else {
            baseKey + "0".repeat(32 - baseKey.length)
        }
    }

    // 根据请求URL获取对应的加密密钥
    private fun getEncryptKey(request: Request): String {
        val urlPath = request.url.encodedPath
        return if (urlPath.endsWith("/config/getAppConfigPostV2") || urlPath.endsWith("baseApi/resource/network_top_hash/patch")) {
            // getAppConfigPostV2接口使用generateKey()
            generateKey()
        } else {
            // 其他接口优先从DecryptedAppConfig获取encrypt_key，没有再从SharedPreference获取
            val configEncryptKey = AppConfigManager.getEncryptKey()
            if (!configEncryptKey.isNullOrEmpty()) {
                configEncryptKey
            } else {
                SharePreferenceUtil.getString(Constant.ENCRYPT_KEY, "") ?: generateKey()
            }
        }
    }

    private val okHttpClient: OkHttpClient by lazy {
        val logging = HttpLoggingInterceptor().apply {
            level = HttpLoggingInterceptor.Level.BODY
        }

        OkHttpClient.Builder()
            .connectTimeout(10, TimeUnit.SECONDS)
            .readTimeout(10, TimeUnit.SECONDS)
            .writeTimeout(10, TimeUnit.SECONDS)
            .retryOnConnectionFailure(true) // 启用自动重试
            .addInterceptor(NetworkCheckInterceptor()) // 网络检查
            .addInterceptor(RetryInterceptor())// 重试
            .apply {
                if (!BuildConfig.DEBUG) {
                    addInterceptor(UrlMappingInterceptor()) // URL映射
                }
            }
            .addInterceptor { chain ->
                val context = CallmeApplication.context
                val original = chain.request()
                val builder = original.newBuilder()

                // 使用HeaderUtils统一构建header
                val headers = HeaderUtils.buildCommonHeaders(context)
                for ((key, value) in headers) {
                    builder.header(key, value)
                }

                val request = builder.build()
                if (request.method == "POST") {
                    val newRequest = addHeadersToBody(request)
                    chain.proceed(newRequest)
                } else {
                    chain.proceed(request)
                }
            }
//            .addInterceptor(CurlLoggingInterceptor())
            .addInterceptor(DecryptResponseInterceptor())
//            .addInterceptor(logging)
            .build()
    }

    private fun addHeadersToBody(request: Request): Request {
        val originalBody = request.body

        // 将headers转换为字符串
        val headersMap = mutableMapOf<String, String>()
        request.headers.forEach { (name, value) ->
            headersMap[name] = value.toString()
        }
        val headersString = JSONObject(headersMap).toString().replace("\\/", "/")

        // 准备要加密的JSON字符串
        val jsonToEncrypt = if (originalBody == null) {
            // 如果原始请求体为空，创建新的JSON对象
            JSONObject().apply {
                put("http_headers", headersString)
            }.toString().replace("\\/", "/")
        } else {
            // 读取原始请求体
            val buffer = Buffer()
            originalBody.writeTo(buffer)
            val contentType = originalBody.contentType()
            val charset = contentType?.charset(Charsets.UTF_8) ?: Charsets.UTF_8
            val bodyString = buffer.readString(charset)

            if (bodyString.isNullOrEmpty()) {
                // 如果原始请求体为空，创建新的JSON对象
                JSONObject().apply {
                    put("http_headers", headersString)
                }.toString().replace("\\/", "/")
            } else {
                try {
                    // 解析原始请求体
                    val bodyJson = JSONObject(bodyString)

                    // 添加 headers
                    bodyJson.put(
                        "http_headers",
                        JSONObject(headersString).toString().replace("\\/", "/")
                    )
                    bodyJson.toString().replace("\\/", "/")
                } catch (e: Exception) {
                    // 如果原始请求体不是JSON格式，返回原始请求
                    return request
                }
            }
        }

        // 使用AES加密
        try {
            val encryptKey = getEncryptKey(request)
            Timber.d("${request.url}, 加密key: $encryptKey, 加密前：$jsonToEncrypt")

            val encryptedData = AESUtils.encrypt(jsonToEncrypt, encryptKey)

//            ("${request.url}, 加密key: " + encryptKey + ", 加密后：" + encryptedData).logAsTag(javaClass.name)

            // 直接将加密后的数据作为原始请求体，并移除所有headers
            val newBody = encryptedData.toRequestBody("application/json".toMediaType())

            return request.newBuilder()
                .method(request.method, newBody)
                .headers(Headers.Builder().build()) // 清空所有headers
                .header("content-type", "application/json")
                .build()
        } catch (e: Exception) {
            // 如果加密失败，返回原始请求
            return request
        }
    }

    val retrofit: Retrofit by lazy {
        Retrofit.Builder()
            .baseUrl("https://" + Constant.getBaseUrl())
            .client(okHttpClient)
//            .addConverterFactory(json.asConverterFactory("application/json".toMediaType()))
            .addConverterFactory(GsonConverterFactory.create())
            .build()
    }

    val apiService: ApiService by lazy {
        retrofit.create(ApiService::class.java)
    }
    
    // 日志上报专用的Retrofit实例，使用BASE_LOG_URL
    val logRetrofit: Retrofit by lazy {
        Retrofit.Builder()
            .baseUrl("https://" + Constant.getLogUrl())
            .client(okHttpClient)
            .addConverterFactory(GsonConverterFactory.create())
            .build()
    }
    
    val logApiService: ApiService by lazy {
        logRetrofit.create(ApiService::class.java)
    }

    val dataRepository: DataRepository by lazy {
        DataRepository(apiService,logApiService)
    }

    // Curl 日志拦截器
    class CurlLoggingInterceptor : Interceptor {
        override fun intercept(chain: Interceptor.Chain): Response {
            val request = chain.request()
            val curlCmd = StringBuilder()
            curlCmd.append("curl -X ").append(request.method)
            curlCmd.append(" '").append(request.url).append("'")
            for (name in request.headers.names()) {
                val value = request.header(name)
                curlCmd.append(" -H '").append(name).append(": ").append(value).append("'")
            }
            val requestBody = request.body
            if (requestBody != null) {
                val buffer = Buffer()
                requestBody.writeTo(buffer)
                val charset = requestBody.contentType()?.charset(Charsets.UTF_8) ?: Charsets.UTF_8
                val body = buffer.readString(charset)
                if (body.isNotEmpty()) {
                    curlCmd.append(" --data '").append(body.replace("'", "\\'")).append("'")
                }
            }
//            println("[CURL] $curlCmd")
            "[CURL] $curlCmd".logAsTag(javaClass.name, Log.WARN)
            return chain.proceed(request)
        }
    }

    // 响应体解密拦截器
    class DecryptResponseInterceptor : Interceptor {
        override fun intercept(chain: Interceptor.Chain): Response {
            val request = chain.request()
            try {
                var response = chain.proceed(request)

                val responseBody = response.body ?: return response
                val contentType = responseBody.contentType()
                val encryptedString = responseBody.string().replace("\r\n", "")

                response = try {
                    val decryptKey = getEncryptKey(request)
//                    ("${request.url}, 解密key: " + decryptKey + ", 解密前：" + encryptedString).logAsTag(
//                        javaClass.name
//                    )
                    val decryptedString = AESUtils.decrypt(encryptedString, decryptKey)
                    Timber.d("${request.url}, 解密key: $decryptKey, 解密后：$decryptedString")

                    val newBody = decryptedString.toResponseBody(contentType)
                    response.newBuilder().body(newBody).build()
                } catch (e: Exception) {
                    Log.e(this.javaClass.name, "解密出错： ", e)
                    // 解密失败，返回原始 response
                    response
                }
                return response
            } catch (e: Exception) {
                // 捕获超时等异常，返回一个408的空response，避免崩溃
                return Response.Builder()
                    .request(request)
                    .protocol(Protocol.HTTP_1_1)
                    .code(408) // 408 Request Timeout
                    .message("timeout: ${e.javaClass.simpleName}")
                    .body("".toResponseBody(null))
                    .build()
            }
        }
    }

    // URL映射拦截器 - 将代码中的接口路径映射到实际的服务器接口路径
    class UrlMappingInterceptor : Interceptor {
        override fun intercept(chain: Interceptor.Chain): Response {
            val originalRequest = chain.request()
            val originalUrl = originalRequest.url
            val originalPath = originalUrl.encodedPath
            
            // 查找映射关系
            val mappedPath = urlMappingMap[originalPath]
            
            if (mappedPath != null) {
                // 记录URL映射日志
                ("URL映射: $originalPath -> $mappedPath").logAsTag(javaClass.name, Log.INFO)
                
                // 构建新的URL
                val newUrl = originalUrl.newBuilder()
                    .encodedPath(mappedPath)
                    .build()
                
                // 构建新的请求
                val newRequest = originalRequest.newBuilder()
                    .url(newUrl)
                    .build()
                
                return chain.proceed(newRequest)
            }
            
            // 如果没有映射关系，使用原始请求
            return chain.proceed(originalRequest)
        }
    }
} 