package com.score.callmetest.util

import android.view.View
import com.score.callmetest.R
import timber.log.Timber

/**
 * 点击事件防抖工具类
 */
object ClickUtils {
    // 默认防抖间隔时间（毫秒）
    private const val DEFAULT_DEBOUNCE_INTERVAL = 500L
    
    // 全局最后一次点击时间
    private var sLastClickTime = 0L
    
    /**
     * 判断是否快速点击（全局，不区分View）
     * @param interval 防抖间隔时间，默认500毫秒
     * @return 是否是快速点击
     */
    @JvmStatic
    fun isFastClickGlobal(interval: Long = DEFAULT_DEBOUNCE_INTERVAL): Boolean {
        val currentTime = System.currentTimeMillis()
        val lastClickTime = sLastClickTime
        
        if (currentTime - lastClickTime < interval) {
            // 是快速点击，记录日志
            Timber.tag("dsc--").d("Fast global click detected")
            return true
        }
        
        // 不是快速点击，更新全局点击时间
        sLastClickTime = currentTime
        return false
    }
    
    /**
     * 设置全局防抖点击监听器（不区分View ID）
     * @param view 需要设置防抖的View
     * @param interval 防抖间隔，默认500毫秒
     * @param onClick 点击回调
     */
    @JvmStatic
    fun setOnGlobalDebounceClickListener(
        view: View,
        interval: Long = DEFAULT_DEBOUNCE_INTERVAL,
        onClick: (View) -> Unit
    ) {
        view.setOnClickListener { v ->
            if (!isFastClickGlobal(interval)) {
                Timber.tag("dsc--").d("Global click processed for view id: ${v.id}")
                onClick(v)
            }
        }
    }
    
    /**
     * 设置单独的点击监听器，不共享时间戳
     * @param view 需要设置防抖的View
     * @param interval 防抖间隔，默认500毫秒
     * @param onClick 点击回调
     */
    @JvmStatic
    fun setOnIsolatedClickListener(
        view: View,
        interval: Long = DEFAULT_DEBOUNCE_INTERVAL,
        onClick: (View) -> Unit
    ) {
        // 使用View的tag来存储上次点击时间，每个View独立计时
        view.setOnClickListener { v ->
            val currentTime = System.currentTimeMillis()
            val lastClickTime = v.getTag(R.id.click_time_tag) as? Long ?: 0L
            
            if (currentTime - lastClickTime >= interval) {
                // 不是快速点击，更新点击时间并执行回调
                v.setTag(R.id.click_time_tag, currentTime)
                Timber.tag("dsc--").d("Isolated click processed for view id: ${v.id}")
                onClick(v)
            } else {
                // 是快速点击，记录日志
                Timber.tag("dsc--").d("Fast isolated click detected on view id: ${v.id}")
            }
        }
    }

    
    /**
     * 清除所有点击记录
     */
    @JvmStatic
    fun clearAllClickRecords() {
        sLastClickTime = 0L
        Timber.tag("dsc--").d("Cleared all click records")
    }
} 