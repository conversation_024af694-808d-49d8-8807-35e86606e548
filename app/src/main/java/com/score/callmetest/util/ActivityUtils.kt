package com.score.callmetest.util

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import com.score.callmetest.manager.AppLifecycleManager
import timber.log.Timber

/**
 * Activity工具类，提供页面跳转与相关辅助方法
 */
object ActivityUtils {
    /**
     * 跳转到指定Activity
     * @param context 上下文
     * @param targetClass 目标Activity类
     * @param bundle 可选参数，传递数据
     * @param finish 是否关闭当前Activity
     */
    @JvmStatic
    fun <T : Activity> startActivity(
        context: Context,
        targetClass: Class<T>,
        bundle: Bundle? = null,
        finish: Boolean = false
    ) {
        val intent = Intent(context, targetClass)
        bundle?.let { intent.putExtras(it) }
        if (intent.resolveActivity(context.packageManager) != null) {
            context.startActivity(intent)
            if (finish && context is Activity) {
                context.finish()
            }
        } else {
            Timber.e("cannot startActivity: $targetClass")
        }
    }

    /**
     * 跳转到指定Activity并清空任务栈
     */
    @JvmStatic
    fun <T : Activity> startActivityClearTask(
        context: Context,
        targetClass: Class<T>,
        bundle: Bundle? = null
    ) {
        val intent = Intent(context, targetClass)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        bundle?.let { intent.putExtras(it) }

        if (intent.resolveActivity(context.packageManager) != null) {
            context.startActivity(intent)
        } else {
            Timber.e("cannot startActivity: $targetClass")
        }
    }

    /**
     * 跳转到指定Activity并等待结果
     */
    @JvmStatic
    fun <T : Activity> startActivityForResult(
        context: Context,
        targetClass: Class<T>,
        requestCode: Int,
        bundle: Bundle? = null
    ) {
        val intent = Intent(context, targetClass)
        bundle?.let { intent.putExtras(it) }

        if (context is Activity && intent.resolveActivity(context.packageManager) != null) {
            context.startActivityForResult(intent, requestCode)
        } else {
            Timber.e("cannot startActivity: $targetClass")
        }
    }

    /**
     * 关闭当前Activity
     */
    @JvmStatic
    fun finish(activity: Activity) {
        activity.finish()
    }

    /**
     * 关闭当前Activity并带结果返回
     */
    @JvmStatic
    fun finishWithResult(activity: Activity, resultCode: Int, data: Intent? = null) {
        activity.setResult(resultCode, data)
        activity.finish()
    }

    /**
     * 获取当前最顶层Activity
     */
    @JvmStatic
    fun getTopActivity(): Activity? {
        return AppLifecycleManager.getTopActivity()
    }

    /**
     * 打开外部网页
     * @param [context] 活动
     * @param [h5Url] h5 uri
     */
    fun openExternalWeb(context: Context, h5Url: Uri) {
        try {
            val intent = Intent(Intent.ACTION_VIEW, h5Url).apply {
                // 关键安全设置
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                addCategory(Intent.CATEGORY_BROWSABLE)
                // 阻止应用内 WebView 处理
                `package` = null
            }
            if (intent.resolveActivity(context.packageManager) != null) {
                context.startActivity(intent)
            } else {
                Timber.e("No browser found to handle URL: $h5Url")
            }
            context.startActivity(intent)
        } catch (e: Exception) {
            Timber.e(e)
        }
    }
} 