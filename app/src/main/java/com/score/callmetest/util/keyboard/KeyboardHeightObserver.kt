package com.score.callmetest.util.keyboard

/**
 * The observer that will be notified when the height of the keyboard has changed
 */
interface KeyboardHeightObserver {
    
    /**
     * Called when the keyboard height has changed
     * @param orientation The current screen orientation
     * @param isOpen Whether the keyboard is open
     * @param keyboardHeight The height of the keyboard in pixels
     */
    fun onKeyboardHeightChanged(orientation: Int, isOpen: <PERSON><PERSON><PERSON>, keyboardHeight: Int)
}
