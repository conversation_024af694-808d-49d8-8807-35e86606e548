import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale
import java.util.concurrent.TimeUnit

object DateUtils {

    private const val TAG = "DateUtils"


    fun getCurrentDate(): String {
        val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
        return dateFormat.format(Date())
    }

    /**
     * 获取当前日期和时间的标准格式字符串。
     * 格式: "yyyy-MM-dd HH:mm:ss"
     *
     * @return 当前日期和时间的字符串表示形式。
     */
    fun getCurrentDateTime(): String {
        val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
        return dateFormat.format(Date())
    }

    /**
     * 将时间戳（毫秒）格式化为指定的日期时间格式。
     *
     * @param timestamp 要格式化的时间戳（毫秒）。
     * @param pattern   日期时间格式，例如 "yyyy-MM-dd HH:mm:ss" 或 "dd/MM/yyyy"。
     * @return 格式化后的日期时间字符串。
     */
    fun formatTimestamp(timestamp: Long, pattern: String = "yyyy-MM-dd HH:mm:ss"): String {
        val dateFormat = SimpleDateFormat(pattern, Locale.getDefault())
        return dateFormat.format(Date(timestamp))
    }

    /**
     * 将日期字符串从一种格式解析为另一种格式。
     *
     * @param dateString      要解析的日期字符串。
     * @param inputPattern    输入日期字符串的格式。
     * @param outputPattern   输出日期字符串的格式。
     * @return 转换格式后的日期字符串，如果解析失败则返回 null。
     */
    fun reformatDateString(dateString: String, inputPattern: String, outputPattern: String): String? {
        return try {
            val inputFormat = SimpleDateFormat(inputPattern, Locale.getDefault())
            val date = inputFormat.parse(dateString)
            if (date != null) {
                val outputFormat = SimpleDateFormat(outputPattern, Locale.getDefault())
                outputFormat.format(date)
            } else {
                null
            }
        } catch (e: Exception) {
            // Log.e(TAG, "Error reformatting date string: $dateString", e) // Consider adding logging
            null
        }
    }

    /**
     * 将日期字符串解析为 Date 对象。
     *
     * @param dateString 要解析的日期字符串。
     * @param pattern    日期字符串的格式。
     * @return 解析后的 Date 对象，如果解析失败则返回 null。
     */
    fun parseDateString(dateString: String, pattern: String = "yyyy-MM-dd HH:mm:ss"): Date? {
        return try {
            val dateFormat = SimpleDateFormat(pattern, Locale.getDefault())
            dateFormat.parse(dateString)
        } catch (e: Exception) {
            // Log.e(TAG, "Error parsing date string: $dateString", e) // Consider adding logging
            null
        }
    }

    /**
     * 获取给定时间戳是星期几。
     *
     * @param timestamp 时间戳（毫秒）。
     * @return 星期几的名称 (例如, "星期一", "星期二").
     */
    fun getDayOfWeek(timestamp: Long): String {
        val calendar = Calendar.getInstance()
        calendar.timeInMillis = timestamp
        // 使用 Locale.getDefault() 来获取本地化的星期名称
        return SimpleDateFormat("EEEE", Locale.getDefault()).format(calendar.time)
    }

    /**
     * 检查给定的时间戳是否是今天。
     *
     * @param timestamp 要检查的时间戳（毫秒）。
     * @return 如果时间戳是今天则返回 true，否则返回 false。
     */
    fun isToday(timestamp: Long): Boolean {
        val calendar = Calendar.getInstance()
        val todayDayOfYear = calendar.get(Calendar.DAY_OF_YEAR)
        val todayYear = calendar.get(Calendar.YEAR)

        calendar.timeInMillis = timestamp
        val givenDayOfYear = calendar.get(Calendar.DAY_OF_YEAR)
        val givenYear = calendar.get(Calendar.YEAR)

        return todayDayOfYear == givenDayOfYear && todayYear == givenYear
    }

    /**
     * 检查给定的时间戳是否是昨天。
     *
     * @param timestamp 要检查的时间戳（毫秒）。
     * @return 如果时间戳是昨天则返回 true，否则返回 false。
     */
    fun isYesterday(timestamp: Long): Boolean {
        val calendar = Calendar.getInstance()
        calendar.add(Calendar.DAY_OF_YEAR, -1) // 将日历设置为昨天
        val yesterdayDayOfYear = calendar.get(Calendar.DAY_OF_YEAR)
        val yesterdayYear = calendar.get(Calendar.YEAR)

        calendar.timeInMillis = timestamp
        val givenDayOfYear = calendar.get(Calendar.DAY_OF_YEAR)
        val givenYear = calendar.get(Calendar.YEAR)

        return yesterdayDayOfYear == givenDayOfYear && yesterdayYear == givenYear
    }


    /**
     * 获取两个时间戳之间的天数差异。
     *
     * @param startTimestamp 开始时间戳（毫秒）。
     * @param endTimestamp   结束时间戳（毫秒）。
     * @return 两个时间戳之间的天数差异。
     */
    fun getDaysDifference(startTimestamp: Long, endTimestamp: Long): Long {
        val diffInMillis = kotlin.math.abs(endTimestamp - startTimestamp)
        return TimeUnit.MILLISECONDS.toDays(diffInMillis)
    }

    /**
     * 在给定日期上添加指定的天数。
     *
     * @param date   原始日期。
     * @param days   要添加的天数（可以是负数以减去天数）。
     * @return 修改后的 Date 对象。
     */
    fun addDaysToDate(date: Date, days: Int): Date {
        val calendar = Calendar.getInstance()
        calendar.time = date
        calendar.add(Calendar.DAY_OF_YEAR, days)
        return calendar.time
    }

    /**
     * 获取给定月份的天数。
     *
     * @param year  年份 (例如, 2023).
     * @param month 月份 (1-12).
     * @return 指定月份的天数。
     */
    fun getDaysInMonth(year: Int, month: Int): Int {
        val calendar = Calendar.getInstance()
        // Calendar 中的月份是从 0 开始的 (0 = 一月, 11 = 十二月)
        calendar.set(year, month - 1, 1)
        return calendar.getActualMaximum(Calendar.DAY_OF_MONTH)
    }
}
