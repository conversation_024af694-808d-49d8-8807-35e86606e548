package com.score.callmetest.util
import java.io.*
import java.nio.charset.Charset

/**
 * 文件操作工具类，提供文件读写、复制、删除等功能（兼容 Android API 21+）
 */
object FileUtils {
    /**
     * 读取文件内容为字符串
     * @param file 要读取的文件
     * @param charset 字符集，默认为 UTF-8
     * @return 文件内容字符串，如果出错则返回 null
     */
    @JvmStatic
    @JvmOverloads
    fun readFileToString(file: File, charset: Charset = Charsets.UTF_8): String? {
        var reader: BufferedReader? = null
        try {
            reader = BufferedReader(InputStreamReader(FileInputStream(file), charset))
            val content = StringBuilder()
            var line: String?
            while (reader.readLine().also { line = it } != null) {
                content.append(line).append("\n")
            }
            // 移除最后一个多余的换行符
            if (content.isNotEmpty()) {
                content.deleteCharAt(content.length - 1)
            }
            return content.toString()
        } catch (e: Exception) {
            e.printStackTrace()
            return null
        } finally {
            closeQuietly(reader)
        }
    }

    /**
     * 读取文件内容为字节数组
     * @param file 要读取的文件
     * @return 文件内容字节数组，如果出错则返回 null
     */
    @JvmStatic
    fun readFileToByteArray(file: File): ByteArray? {
        var fis: FileInputStream? = null
        try {
            fis = FileInputStream(file)
            val length = file.length().toInt()
            if (length <= 0) return ByteArray(0)

            val buffer = ByteArray(length)
            var offset = 0
            var bytesRead = 0
            while (offset < length && fis.read(buffer, offset, length - offset).also { bytesRead = it } >= 0) {
                offset += bytesRead
            }
            return buffer
        } catch (e: Exception) {
            e.printStackTrace()
            return null
        } finally {
            closeQuietly(fis)
        }
    }

    /**
     * 将字符串写入文件
     * @param file 目标文件
     * @param content 要写入的内容
     * @param charset 字符集，默认为 UTF-8
     * @param append 是否追加，默认为 false
     * @return 是否写入成功
     */
    @JvmStatic
    @JvmOverloads
    fun writeStringToFile(file: File, content: String, charset: Charset = Charsets.UTF_8, append: Boolean = false): Boolean {
        var writer: BufferedWriter? = null
        try {
            file.parentFile?.mkdirs()
            writer = BufferedWriter(OutputStreamWriter(FileOutputStream(file, append), charset))
            writer.write(content)
            return true
        } catch (e: Exception) {
            e.printStackTrace()
            return false
        } finally {
            closeQuietly(writer)
        }
    }

    /**
     * 将字节数组写入文件
     * @param file 目标文件
     * @param content 要写入的字节数组
     * @param append 是否追加，默认为 false
     * @return 是否写入成功
     */
    @JvmStatic
    @JvmOverloads
    fun writeByteArrayToFile(file: File, content: ByteArray, append: Boolean = false): Boolean {
        var fos: FileOutputStream? = null
        try {
            file.parentFile?.mkdirs()
            fos = FileOutputStream(file, append)
            fos.write(content)
            return true
        } catch (e: Exception) {
            e.printStackTrace()
            return false
        } finally {
            closeQuietly(fos)
        }
    }

    /**
     * 复制文件
     * @param source 源文件
     * @param destination 目标文件
     * @return 是否复制成功
     */
    @JvmStatic
    fun copyFile(source: File, destination: File): Boolean {
        var inputStream: FileInputStream? = null
        var outputStream: FileOutputStream? = null
        try {
            destination.parentFile?.mkdirs()
            inputStream = FileInputStream(source)
            outputStream = FileOutputStream(destination)

            val buffer = ByteArray(8192)
            var bytesRead: Int
            while (inputStream.read(buffer).also { bytesRead = it } != -1) {
                outputStream.write(buffer, 0, bytesRead)
            }
            return true
        } catch (e: Exception) {
            e.printStackTrace()
            return false
        } finally {
            closeQuietly(inputStream)
            closeQuietly(outputStream)
        }
    }

    /**
     * 移动文件
     * @param source 源文件
     * @param destination 目标文件
     * @return 是否移动成功
     */
    @JvmStatic
    fun moveFile(source: File, destination: File): Boolean {
        return try {
            if (source.renameTo(destination)) {
                true
            } else {
                // 如果重命名失败，则尝试先复制再删除
                val success = copyFile(source, destination)
                if (success) {
                    source.delete()
                }
                success
            }
        } catch (e: Exception) {
            e.printStackTrace()
            false
        }
    }

    /**
     * 删除文件或目录
     * @param file 要删除的文件或目录
     * @return 是否删除成功
     */
    @JvmStatic
    fun delete(file: File): Boolean {
        return try {
            if (!file.exists()) return true

            if (file.isDirectory) {
                val children = file.listFiles()
                if (children != null) {
                    for (child in children) {
                        delete(child)
                    }
                }
            }

            file.delete()
        } catch (e: Exception) {
            e.printStackTrace()
            false
        }
    }

    /**
     * 检查文件或目录是否存在
     * @param path 文件或目录路径
     * @return 是否存在
     */
    @JvmStatic
    fun exists(path: String): Boolean {
        return File(path).exists()
    }

    /**
     * 创建目录，如果不存在的话
     * @param dir 目录文件对象
     * @return 是否创建成功或已存在
     */
    @JvmStatic
    fun createDirIfNotExists(dir: File): Boolean {
        return try {
            if (!dir.exists()) {
                dir.mkdirs()
            }
            true
        } catch (e: Exception) {
            e.printStackTrace()
            false
        }
    }

    /**
     * 获取文件大小
     * @param file 文件对象
     * @return 文件大小（字节），如果出错则返回 -1
     */
    @JvmStatic
    fun getFileSize(file: File): Long {
        return try {
            if (!file.exists()) return -1
            if (file.isDirectory) return -1

            file.length()
        } catch (e: Exception) {
            e.printStackTrace()
            -1
        }
    }

    /**
     * 列出目录下的所有文件和文件夹
     * @param dir 目录文件对象
     * @return 文件和文件夹列表，如果出错或目录不存在则返回空列表
     */
    @JvmStatic
    fun listFiles(dir: File): List<File> {
        return try {
            if (!dir.exists() || !dir.isDirectory) return emptyList()

            val files = dir.listFiles()
            files?.toList() ?: emptyList()
        } catch (e: Exception) {
            e.printStackTrace()
            emptyList()
        }
    }

    /**
     * 列出目录下的所有文件（不包含文件夹）
     * @param dir 目录文件对象
     * @return 文件列表，如果出错或目录不存在则返回空列表
     */
    @JvmStatic
    fun listAllFiles(dir: File): List<File> {
        val result = mutableListOf<File>()
        try {
            if (!dir.exists() || !dir.isDirectory) return emptyList()

            val files = dir.listFiles()
            if (files != null) {
                for (file in files) {
                    if (file.isDirectory) {
                        result.addAll(listAllFiles(file))
                    } else {
                        result.add(file)
                    }
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return result
    }

    /**
     * 获取文件扩展名
     * @param file 文件对象
     * @return 文件扩展名（不带点），如果没有扩展名则返回空字符串
     */
    @JvmStatic
    fun getFileExtension(file: File): String {
        val name = file.name
        val lastIndex = name.lastIndexOf('.')
        return if (lastIndex == -1) "" else name.substring(lastIndex + 1)
    }

    /**
     * 安静地关闭可关闭对象，忽略异常
     */
    private fun closeQuietly(closeable: Closeable?) {
        try {
            closeable?.close()
        } catch (ignored: Exception) {
            // 忽略异常
        }
    }
}