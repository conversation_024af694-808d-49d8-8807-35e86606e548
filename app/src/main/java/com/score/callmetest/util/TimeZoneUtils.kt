package com.score.callmetest.util

import android.annotation.SuppressLint
import java.util.TimeZone
import java.util.Locale
import java.text.SimpleDateFormat
import java.util.Date

/**
 * 时区工具类，提供获取当前时区信息的方法
 */
object TimeZoneUtils {
    /** 获取当前时区ID（如 Asia/Shanghai） */
    fun getCurrentTimeZoneId(): String = TimeZone.getDefault().id

    /** 获取当前时区偏移（单位：毫秒） */
    fun getCurrentTimeZoneOffset(): Int = TimeZone.getDefault().rawOffset

    /** 获取当前时区偏移（格式：+08:00） */
    @SuppressLint("DefaultLocale")
    fun getCurrentTimeZoneOffsetString(): String {
        val offsetMillis = TimeZone.getDefault().rawOffset
        val hours = offsetMillis / (1000 * 60 * 60)
        val minutes = Math.abs((offsetMillis / (1000 * 60)) % 60)
        return String.format("%+03d:%02d", hours, minutes)
    }

    /** 获取当前时区显示名（如 中国标准时间） */
    fun getCurrentTimeZoneDisplayName(locale: Locale = Locale.getDefault()): String =
        TimeZone.getDefault().getDisplayName(locale)

    /** 获取当前时间的时区格式化字符串（如 2024-06-01 12:00:00 GMT+08:00） */
    fun getCurrentTimeWithTimeZone(): String {
        val sdf = SimpleDateFormat("yyyy-MM-dd HH:mm:ss z", Locale.getDefault())
        return sdf.format(Date())
    }
} 