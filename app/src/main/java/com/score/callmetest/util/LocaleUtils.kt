package com.score.callmetest.util

import android.content.Context
import android.os.Build
import java.util.Locale

/**
 * 语言和地区工具类
 * 统一处理不同Android版本的语言获取，解决版本适配问题
 */
object LocaleUtils {
    
    /**
     * 获取系统语言
     * @return 系统语言代码，如 "en", "zh"
     */
    fun getSystemLanguage(): String {
        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                // Android 7.0 (API 24) 及以上版本使用新的API
                val locale = Locale.getDefault()
                locale.language
            } else {
                // Android 7.0 以下版本使用旧的API
                @Suppress("DEPRECATION")
                Locale.getDefault().language
            }
        } catch (e: Exception) {
            "en" // 默认返回英语
        }
    }
    
    /**
     * 获取系统地区
     * @return 系统地区代码，如 "US", "CN"
     */
    fun getSystemCountry(): String {
        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                // Android 7.0 (API 24) 及以上版本使用新的API
                val locale = Locale.getDefault()
                locale.country
            } else {
                // Android 7.0 以下版本使用旧的API
                @Suppress("DEPRECATION")
                Locale.getDefault().country
            }
        } catch (e: Exception) {
            "" // 默认返回空字符串
        }
    }
    
    /**
     * 获取系统语言和地区的组合
     * @return 语言-地区组合，如 "en-US", "zh-CN"
     */
    fun getSystemLanguageCountry(): String {
        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                // Android 7.0 (API 24) 及以上版本使用新的API
                val locale = Locale.getDefault()
                "${locale.language}-${locale.country}"
            } else {
                // Android 7.0 以下版本使用旧的API
                @Suppress("DEPRECATION")
                val locale = Locale.getDefault()
                "${locale.language}-${locale.country}"
            }
        } catch (e: Exception) {
            "en-US" // 默认返回英语-美国
        }
    }
    
    /**
     * 获取当前Locale对象
     * @return 当前系统的Locale对象
     */
    fun getCurrentLocale(): Locale {
        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                // Android 7.0 (API 24) 及以上版本使用新的API
                Locale.getDefault()
            } else {
                // Android 7.0 以下版本使用旧的API
                @Suppress("DEPRECATION")
                Locale.getDefault()
            }
        } catch (e: Exception) {
            Locale.US // 默认返回美国英语
        }
    }
} 