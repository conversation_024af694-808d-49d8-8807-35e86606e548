package com.score.callmetest.util

import kotlinx.coroutines.*
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors

/**
 * 线程切换工具类，使用 Kotlin 协程实现
 */
object ThreadUtils {
    // 主线程调度器
    private val mainDispatcher: CoroutineDispatcher = Dispatchers.Main

    // 子线程调度器
    private val ioDispatcher: CoroutineDispatcher = Dispatchers.IO

    // 自定义线程池，用于长时间运行的任务
    private val backgroundExecutor: ExecutorService = Executors.newFixedThreadPool(5)
    private val backgroundDispatcher: CoroutineDispatcher = backgroundExecutor.asCoroutineDispatcher()

    // 协程作用域
    private val scope = CoroutineScope(SupervisorJob() + mainDispatcher)

    /**
     * 在主线程执行任务
     * @param block 要执行的任务
     */
    fun runOnMain(block: suspend () -> Unit): Job {
        return scope.launch {
            block()
        }
    }

    /**
     * 在主线程延迟执行任务
     * @param delayMillis 延迟时间（毫秒）
     * @param block 要执行的任务
     */
    fun runOnMainDelayed(delayMillis: Long, block: suspend () -> Unit): Job {
        return scope.launch {
            delay(delayMillis)
            block()
        }
    }

    /**
     * 在子线程执行任务
     * @param block 要执行的任务
     */
    fun runOnIO(block: suspend () -> Unit): Job {
        return scope.launch(ioDispatcher) {
            block()
        }
    }

    /**
     * 在子线程延迟执行任务
     * @param delayMillis 延迟时间（毫秒）
     * @param block 要执行的任务
     */
    fun runOnIODelayed(delayMillis: Long, block: suspend () -> Unit): Job {
        return scope.launch(ioDispatcher) {
            delay(delayMillis)
            block()
        }
    }

    /**
     * 在后台线程池执行长时间运行的任务
     * @param block 要执行的任务
     */
    fun runOnBackground(block: suspend () -> Unit): Job {
        return scope.launch(backgroundDispatcher) {
            block()
        }
    }

    /**
     * 在后台线程池延迟执行长时间运行的任务
     * @param delayMillis 延迟时间（毫秒）
     * @param block 要执行的任务
     */
    fun runOnBackgroundDelayed(delayMillis: Long, block: suspend () -> Unit): Job {
        return scope.launch(backgroundDispatcher) {
            delay(delayMillis)
            block()
        }
    }

    /**
     * 从子线程切换到主线程
     * @param block 要执行的任务
     */
    suspend fun switchToMain(block: suspend () -> Unit) {
        withContext(mainDispatcher) {
            block()
        }
    }

    /**
     * 从主线程切换到子线程
     * @param block 要执行的任务
     */
    suspend fun switchToIO(block: suspend () -> Unit) {
        withContext(ioDispatcher) {
            block()
        }
    }

    /**
     * 倒计时工具方法
     * @param totalSeconds 总秒数
     * @param onTick 每秒回调，参数为剩余秒数
     * @param onFinish 结束回调
     * @return Job 可用于取消倒计时
     */
    fun startCountdown(
        totalSeconds: Long,
        onTick: (remainingSeconds: Long) -> Unit,
        onFinish: () -> Unit
    ): Job {
        return scope.launch {
            var remaining = totalSeconds
            while (remaining > 0) {
                onTick(remaining)
                delay(1000)
                remaining--
            }
            onFinish()
        }
    }

    /**
     * 倒计时工具方法（毫秒版本）
     * @param totalMilliseconds 总毫秒数
     * @param intervalMillis 间隔毫秒数，默认1000ms
     * @param onTick 每次回调，参数为剩余毫秒数
     * @param onFinish 结束回调
     * @return Job 可用于取消倒计时
     */
    fun startCountdownMillis(
        totalMilliseconds: Long,
        intervalMillis: Long = 1000L,
        onTick: (remainingMilliseconds: Long) -> Unit,
        onFinish: () -> Unit
    ): Job {
        return scope.launch {
            var remaining = totalMilliseconds
            while (remaining > 0) {
                onTick(remaining)
                delay(intervalMillis)
                remaining -= intervalMillis
            }
            onFinish()
        }
    }

    /**
     * 关闭线程池，释放资源
     */
    fun shutdown() {
        scope.cancel()
        backgroundExecutor.shutdown()
    }
}