package com.score.callmetest.util

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Bitmap
import android.widget.ImageView
import android.os.Handler
import android.os.Looper
import androidx.annotation.DrawableRes
import com.bumptech.glide.Glide
import com.bumptech.glide.load.Transformation
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.resource.bitmap.CircleCrop
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.bumptech.glide.request.RequestOptions
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.request.target.BitmapImageViewTarget
import com.bumptech.glide.request.target.Target
import timber.log.Timber

object GlideUtils {

    data class DoubleUrl(
        val primary: Any?,
        val fallback: Any? = null,
        var useFallback: Boolean = false
    )

    fun load(
        imageView: ImageView,
        doubleUrl: DoubleUrl,
        @DrawableRes placeholder: Int? = null,
        @DrawableRes error: Int? = null,
        radius: Int = 0,
        isCircle: Boolean = false,
        onResourceReady: (() -> Unit)? = null
    ) {
        if (doubleUrl.primary != null) {
            load(
                context = imageView.context,
                url = doubleUrl.primary,
                imageView = imageView,
                placeholder = placeholder,
                error = error,
                radius = radius,
                isCircle = isCircle,
                onRequestListener = object : RequestListener<Bitmap> {
                    override fun onLoadFailed(
                        e: GlideException?,
                        model: Any?,
                        target: Target<Bitmap?>,
                        isFirstResource: Boolean
                    ): Boolean {
                        Handler(Looper.getMainLooper()).post {
                            if (!doubleUrl.useFallback && doubleUrl.fallback != null && doubleUrl.fallback != doubleUrl.primary) {
                                doubleUrl.useFallback = true
                                load(
                                    imageView.context,
                                    doubleUrl.fallback,
                                    imageView,
                                    placeholder,
                                    error,
                                    radius,
                                    isCircle,
                                    onRequestListener = object : RequestListener<Bitmap> {
                                        override fun onLoadFailed(
                                            e: GlideException?,
                                            model: Any?,
                                            target: Target<Bitmap?>,
                                            isFirstResource: Boolean
                                        ): Boolean {
                                            return false
                                        }

                                        override fun onResourceReady(
                                            resource: Bitmap,
                                            model: Any,
                                            target: com.bumptech.glide.request.target.Target<Bitmap>?,
                                            dataSource: DataSource,
                                            isFirstResource: Boolean
                                        ): Boolean {
                                            Handler(Looper.getMainLooper()).post {
                                                onResourceReady?.invoke()
                                            }
                                            return false
                                        }
                                    }
                                )
                            }
                        }
                        return false
                    }

                    override fun onResourceReady(
                        resource: Bitmap,
                        model: Any,
                        target: com.bumptech.glide.request.target.Target<Bitmap>?,
                        dataSource: DataSource,
                        isFirstResource: Boolean
                    ): Boolean {
                        Handler(Looper.getMainLooper()).post {
                            onResourceReady?.invoke()
                        }
                        return false
                    }
                }
            )
        } else if (doubleUrl.fallback != null) {
            load(
                imageView.context,
                doubleUrl.fallback,
                imageView,
                placeholder,
                error,
                radius,
                isCircle,
                onRequestListener = object : RequestListener<Bitmap> {
                    override fun onLoadFailed(
                        e: GlideException?,
                        model: Any?,
                        target: Target<Bitmap?>,
                        isFirstResource: Boolean
                    ): Boolean {
                        return false
                    }

                    override fun onResourceReady(
                        resource: Bitmap,
                        model: Any,
                        target: com.bumptech.glide.request.target.Target<Bitmap>?,
                        dataSource: DataSource,
                        isFirstResource: Boolean
                    ): Boolean {
                        Handler(Looper.getMainLooper()).post {
                            onResourceReady?.invoke()
                        }
                        return false
                    }
                }
            )
        }
    }

    /**
     * 加载图片（支持url、资源id、文件），可选圆角、圆形、占位图、错误图
     * 新增：自动补足图片为目标ImageView的宽高比，避免内容被裁剪
     */
    @SuppressLint("CheckResult")
    fun load(
        context: Context,
        url: Any?,
        imageView: ImageView,
        @DrawableRes placeholder: Int? = null,
        @DrawableRes error: Int? = null,
        radius: Int = 0,
        isCircle: Boolean = false,
        onRequestListener: RequestListener<Bitmap>? = null,
        vararg extraTransform: Transformation<Bitmap>
    ) {
        val request = Glide.with(context).asBitmap().load(url)
        placeholder?.let { request.placeholder(it) }
        error?.let { request.error(it) }
        request.diskCacheStrategy(DiskCacheStrategy.ALL)
        val options = when {
            isCircle -> RequestOptions().transform(CircleCrop(), *extraTransform)
            radius > 0 -> RequestOptions().transform(RoundedCorners(radius), *extraTransform)
            else -> RequestOptions().transform(*extraTransform) // 不做任何裁剪或缩放变换
        }
        request.apply(options).listener(onRequestListener).into(imageView)
    }

    fun load(
        view: ImageView,
        url: Any?,
        @DrawableRes placeholder: Int? = null,
        @DrawableRes error: Int? = null,
        radius: Int = 0,
        isCircle: Boolean = false
    ) = load(view.context, url, view, placeholder, error, radius, isCircle)

    /**
     * 清除图片缓存
     */
    fun clear(context: Context, imageView: ImageView) {
        Glide.with(context).clear(imageView)
    }
} 