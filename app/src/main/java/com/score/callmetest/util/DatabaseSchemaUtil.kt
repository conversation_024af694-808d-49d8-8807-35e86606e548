package com.score.callmetest.util

import android.content.Context
import com.score.callmetest.db.AppDatabase
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.io.File
import java.io.FileOutputStream
import kotlin.system.measureTimeMillis

/**
 * 数据库模式工具类
 * 用于数据库模式的验证和维护
 */
object DatabaseSchemaUtil {
    /**
     * 验证数据库模式
     * 在应用启动时调用，用于验证数据库模式是否与当前版本一致
     * 如果数据库文件不存在，将会创建一个空的数据库
     * @param context 上下文
     * @return 验证结果，true表示验证通过，false表示验证失败
     */
    suspend fun verifyDatabaseSchema(context: Context): Boolean = withContext(Dispatchers.IO) {
        try {
            val time = measureTimeMillis {
                // 获取数据库实例，这将触发数据库创建或验证过程
                val db = AppDatabase.getInstance(context)
                // 执行一个简单的查询，确保数据库可以正常工作
            }
            
            Timber.d("Database schema verification completed in $time ms")
            true
        } catch (e: Exception) {
            Timber.e(e, "Database schema verification failed")
            false
        }
    }
    
    /**
     * 导出数据库文件
     * 用于调试和问题排查
     * @param context 上下文
     * @param outputDir 输出目录
     * @return 导出的数据库文件路径，如果导出失败则返回null
     */
    suspend fun exportDatabase(context: Context, outputDir: File): String? = withContext(Dispatchers.IO) {
        try {
            val dbFile = context.getDatabasePath(AppDatabase.DATABASE_NAME)
            if (!dbFile.exists()) {
                Timber.e("Database file does not exist")
                return@withContext null
            }
            
            if (!outputDir.exists()) {
                outputDir.mkdirs()
            }
            
            val outputFile = File(outputDir, "exported_${AppDatabase.DATABASE_NAME}_${System.currentTimeMillis()}.db")
            dbFile.inputStream().use { input ->
                FileOutputStream(outputFile).use { output ->
                    input.copyTo(output)
                }
            }
            
            Timber.d("Database exported to ${outputFile.absolutePath}")
            outputFile.absolutePath
        } catch (e: Exception) {
            Timber.e(e, "Failed to export database")
            null
        }
    }
    
    /**
     * 清除数据库文件
     * 谨慎使用，这将删除所有数据
     * @param context 上下文
     * @return 清除结果，true表示清除成功，false表示清除失败
     */
    suspend fun clearDatabase(context: Context): Boolean = withContext(Dispatchers.IO) {
        try {
            // 先关闭现有的数据库连接
            AppDatabase.destroyInstance()
            
            // 删除数据库文件
            val dbFile = context.getDatabasePath(AppDatabase.DATABASE_NAME)
            if (dbFile.exists()) {
                val deleted = dbFile.delete()
                if (!deleted) {
                    Timber.e("Failed to delete database file")
                    return@withContext false
                }
            }
            
            // 删除数据库相关文件
            val dbFileWal = File(dbFile.path + "-wal")
            if (dbFileWal.exists()) {
                dbFileWal.delete()
            }
            
            val dbFileShm = File(dbFile.path + "-shm")
            if (dbFileShm.exists()) {
                dbFileShm.delete()
            }
            
            Timber.d("Database cleared successfully")
            true
        } catch (e: Exception) {
            Timber.e(e, "Failed to clear database")
            false
        }
    }
} 