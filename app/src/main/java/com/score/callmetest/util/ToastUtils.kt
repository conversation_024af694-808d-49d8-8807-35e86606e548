package com.score.callmetest.util

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Color
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.view.Gravity
import android.view.LayoutInflater
import android.widget.ImageView
import android.widget.TextView
import android.widget.Toast
import androidx.core.content.ContextCompat
import com.score.callmetest.CallmeApplication
import com.score.callmetest.R

@SuppressLint("StaticFieldLeak")
object ToastUtils {
    private var toast: Toast? = null

    fun showToast(msg: String, duration: Int = Toast.LENGTH_SHORT) {
        showCustomToast(msg, duration)
    }

    fun showLongToast(msg: String) {
        showToast(msg, Toast.LENGTH_LONG)
    }

    fun showShortToast(msg: String) {
        showToast(msg, Toast.LENGTH_SHORT)
    }

    /**
     * 自定义Toast弹窗，居中弹窗
     * @param msg 显示消息
     * @param duration 显示时长 Toast.LENGTH_SHORT
     */
    fun showCustomToast(
        msg: String,
        duration: Int = Toast.LENGTH_SHORT,
    ) {
        // 确保在主线程执行
        Handler(Looper.getMainLooper()).post {
            // 先取消上一个Toast的显示，避免重叠和内存泄漏
            toast?.cancel()

            if (Build.VERSION.SDK_INT <= Build.VERSION_CODES.VANILLA_ICE_CREAM) {
                // 创建自定义View
                val customView = LayoutInflater.from(CallmeApplication.context).inflate(R.layout.toast, null)
                val textView = customView.findViewById<TextView>(R.id.tv_toast)
                val imageView = customView.findViewById<ImageView>(R.id.image_view_logo)
                textView.text = msg
                textView.setTextColor(Color.WHITE)
                imageView.setImageResource(R.drawable.logo)

                customView.background = DrawableUtils.createRoundRectDrawable(
                    ContextCompat.getColor(CallmeApplication.context, R.color.toast_bg),
                    DisplayUtils.dp2pxInternalFloat(24f)
                )

                // 创建新的Toast实例并显示
                val newToast = Toast(CallmeApplication.context).apply {
                    view = customView
                    setGravity(Gravity.CENTER, 0, 0) // 居中显示
                    this.duration = duration
                }
                newToast.show()
                toast = newToast // 更新静态引用
            } else {
                // 对于Android R及以上版本，使用系统默认样式
                val newToast = Toast.makeText(CallmeApplication.context, msg, duration).apply {
                    setGravity(Gravity.TOP or Gravity.CENTER, 0, 100) // 居中显示
                }
                newToast.show()
                toast = newToast // 更新静态引用
            }
        }
    }

    fun clear() {
        toast?.cancel()
        toast = null
    }
}