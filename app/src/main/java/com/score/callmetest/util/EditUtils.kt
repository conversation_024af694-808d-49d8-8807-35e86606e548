package com.score.callmetest.util

import android.text.Editable
import android.text.InputFilter
import android.text.TextWatcher
import android.widget.EditText

class SimpleTextWatcher(val after: (String) -> Unit) : TextWatcher {
    override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
    override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
    override fun afterTextChanged(s: Editable?) { after(s?.toString() ?: "") }
}

object EditUtils {
    /**
     * 设置最大长度
     */
    fun setMaxLength(editText: EditText, maxLength: Int) {
        editText.filters = arrayOf(InputFilter.LengthFilter(maxLength))
    }

    /**
     * 禁止输入表情符号
     */
    fun forbidEmoji(editText: EditText) {
        editText.filters = editText.filters.plus(InputFilter { source, _, _, _, _, _ ->
            val block = source.any { Character.getType(it) == Character.SURROGATE.toInt() || Character.getType(it) == Character.OTHER_SYMBOL.toInt() }
            if (block) "" else null
        })
    }

    /**
     * 只允许输入数字
     */
    fun onlyNumber(editText: EditText) {
        editText.filters = editText.filters.plus(InputFilter { source, _, _, _, _, _ ->
            if (source.matches(Regex("[0-9]*"))) null else ""
        })
    }

    /**
     * 自动大写
     */
    fun autoUpperCase(editText: EditText) {
        editText.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable?) {
                val str = s?.toString() ?: ""
                if (str != str.uppercase()) {
                    editText.setText(str.uppercase())
                    editText.setSelection(editText.text.length)
                }
            }
        })
    }

    /**
     * 获取字符串
     */
    fun getText(editText: EditText): String = editText.text?.toString() ?: ""
} 