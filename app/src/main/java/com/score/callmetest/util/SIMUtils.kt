package com.score.callmetest.util

import android.content.Context
import android.telephony.TelephonyManager
import com.score.callmetest.CallmeApplication

object SIMUtils {
    
    /**
     * 获取SIM卡国家
     * @return 国家二字码，如cn、us，如果没有SIM卡返回"none"，异常返回null
     */
    fun getSIMCountry(): String? {
        return try {
            val telephonyManager = CallmeApplication.context.getSystemService(Context.TELEPHONY_SERVICE) as TelephonyManager
            when (telephonyManager.simState) {
                TelephonyManager.SIM_STATE_READY -> {
                    telephonyManager.simCountryIso?.lowercase() ?: "none"
                }
                TelephonyManager.SIM_STATE_ABSENT -> "none"
                else -> null
            }
        } catch (e: Exception) {
            null
        }
    }
} 