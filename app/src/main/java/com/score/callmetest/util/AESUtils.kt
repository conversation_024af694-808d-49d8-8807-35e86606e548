package com.score.callmetest.util

import android.annotation.SuppressLint
import android.util.Base64
import java.nio.charset.Charset
import javax.crypto.Cipher
import javax.crypto.spec.SecretKeySpec

/**
 * AES加解密工具类（ECB/PKCS5Padding/256位密钥）
 */
object AESUtils {
    private const val AES_ALGORITHM = "AES"
    private const val AES_TRANSFORMATION = "AES/ECB/PKCS5Padding"
    private const val KEY_LENGTH = 32 // 32字节 = 256位

    /**
     * 加密
     * @param content 明文
     * @param key 32字节密钥
     * @return Base64编码的密文
     */
    @JvmStatic
    fun encrypt(content: String, key: String, charset: Charset = Charsets.UTF_8): String {
        require(key.toByteArray().size == KEY_LENGTH) { "密钥长度必须为32字节（256位）" }
        val cipher = Cipher.getInstance(AES_TRANSFORMATION)
        val secretKey = SecretKeySpec(key.toByteArray(), AES_ALGORITHM)
        cipher.init(Cipher.ENCRYPT_MODE, secretKey)
        val encrypted = cipher.doFinal(content.toByteArray(charset))
        return Base64.encodeToString(encrypted, Base64.NO_WRAP)
    }

    /**
     * 解密
     * @param encrypted Base64编码的密文
     * @param key 32字节密钥
     * @return 明文
     */
    @JvmStatic
    fun decrypt(encrypted: String, key: String, charset: Charset = Charsets.UTF_8): String {
        require(key.toByteArray().size == KEY_LENGTH) { "密钥长度必须为32字节（256位）" }
        val cipher = Cipher.getInstance(AES_TRANSFORMATION)
        val secretKey = SecretKeySpec(key.toByteArray(), AES_ALGORITHM)
        cipher.init(Cipher.DECRYPT_MODE, secretKey)
        val decoded = Base64.decode(encrypted, Base64.NO_WRAP)
        val original = cipher.doFinal(decoded)
        return String(original, charset)
    }

    /**
     * AES-128加密（16字节密钥，Base64.URL_SAFE）
     */
    @JvmStatic
    fun encrypt128(content: String, key: String, charset: Charset = Charsets.UTF_8): String {
        require(key.toByteArray().size == 16) { "密钥长度必须为16字节（128位）" }
        val cipher = Cipher.getInstance(AES_TRANSFORMATION)
        val secretKey = SecretKeySpec(key.toByteArray(), AES_ALGORITHM)
        cipher.init(Cipher.ENCRYPT_MODE, secretKey)
        val encrypted = cipher.doFinal(content.toByteArray(charset))
        return Base64.encodeToString(encrypted, Base64.URL_SAFE).replace("\n", "").replace("\r\n", "")
    }

    /**
     * AES-128解密（16字节密钥，Base64.URL_SAFE）
     */
    @JvmStatic
    fun decrypt128(encrypted: String, key: String, charset: Charset = Charsets.UTF_8): String {
        require(key.toByteArray().size == 16) { "密钥长度必须为16字节（128位）" }
        val cipher = Cipher.getInstance(AES_TRANSFORMATION)
        val secretKey = SecretKeySpec(key.toByteArray(), AES_ALGORITHM)
        cipher.init(Cipher.DECRYPT_MODE, secretKey)
        val decoded = Base64.decode(encrypted, Base64.URL_SAFE)
        val original = cipher.doFinal(decoded)
        return String(original, charset)
    }
} 