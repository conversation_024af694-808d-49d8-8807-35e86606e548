package com.score.callmetest.util

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.provider.MediaStore
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.fragment.app.FragmentActivity

/**
 * 图片选择工具类
 * 用于处理图片选择逻辑，与UI页面解耦
 */
object ImagePickerUtils {
    
    /**
     * 图片选择回调接口
     */
    interface ImagePickerCallback {
        /**
         * 图片选择成功
         * @param imageUri 选择的图片URI
         */
        fun onImageSelected(imageUri: Uri)
        
        /**
         * 图片选择失败
         * @param error 错误信息
         */
        fun onImageSelectionFailed(error: String)
    }
    
    /**
     * 创建图片选择器
     * @param activity 当前Activity
     * @param callback 选择回调
     * @return ActivityResultLauncher
     */
    fun createImagePicker(
        activity: FragmentActivity,
        callback: ImagePickerCallback
    ): ActivityResultLauncher<Intent> {
        return activity.registerForActivityResult(
            ActivityResultContracts.StartActivityForResult()
        ) { result ->
            if (result.resultCode == Activity.RESULT_OK) {
                val data: Intent? = result.data
                val imageUri: Uri? = data?.data
                
                if (imageUri != null) {
                    android.util.Log.d("ImagePickerUtils", "Image selected: $imageUri")
                    callback.onImageSelected(imageUri)
                } else {
                    android.util.Log.e("ImagePickerUtils", "No image URI in result")
                    callback.onImageSelectionFailed("No image selected")
                }
            } else {
                android.util.Log.d("ImagePickerUtils", "Image selection cancelled")
                callback.onImageSelectionFailed("Image selection cancelled")
            }
        }
    }
    
    /**
     * 创建从相册选择图片的Intent
     * @return Intent
     */
    fun createGalleryIntent(): Intent {
        return Intent(Intent.ACTION_PICK).apply {
            type = "image/*"
            putExtra(Intent.EXTRA_MIME_TYPES, arrayOf("image/jpeg", "image/png", "image/jpg", "image/gif"))
        }
    }
    
    /**
     * 创建从相册选择图片的Intent（备用方案）
     * @return Intent
     */
    fun createGalleryIntentAlternative(): Intent {
        return Intent(Intent.ACTION_GET_CONTENT).apply {
            type = "image/*"
            addCategory(Intent.CATEGORY_OPENABLE)
        }
    }
    
    /**
     * 创建拍照Intent
     * @param context 上下文
     * @param imageUri 图片保存的URI
     * @return Intent
     */
    fun createCameraIntent(context: Context, imageUri: Uri): Intent {
        return Intent(MediaStore.ACTION_IMAGE_CAPTURE).apply {
            putExtra(MediaStore.EXTRA_OUTPUT, imageUri)
        }
    }
} 