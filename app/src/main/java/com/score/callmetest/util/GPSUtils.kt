package com.score.callmetest.util

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.location.Location
import android.location.LocationManager
import androidx.core.app.ActivityCompat
import com.score.callmetest.CallmeApplication

object GPSUtils {
    
    /**
     * 获取GPS经度
     * @return 经度字符串，如果无法获取返回null
     */
    fun getLongitude(): String? {
        return try {
            if (ActivityCompat.checkSelfPermission(CallmeApplication.context, Manifest.permission.ACCESS_FINE_LOCATION) == PackageManager.PERMISSION_GRANTED) {
                val locationManager = CallmeApplication.context.getSystemService(Context.LOCATION_SERVICE) as LocationManager
                val location = locationManager.getLastKnownLocation(LocationManager.GPS_PROVIDER)
                location?.longitude?.toString()
            } else {
                null
            }
        } catch (e: Exception) {
            null
        }
    }
    
    /**
     * 获取GPS纬度
     * @return 纬度字符串，如果无法获取返回null
     */
    fun getLatitude(): String? {
        return try {
            if (ActivityCompat.checkSelfPermission(CallmeApplication.context, Manifest.permission.ACCESS_FINE_LOCATION) == PackageManager.PERMISSION_GRANTED) {
                val locationManager = CallmeApplication.context.getSystemService(Context.LOCATION_SERVICE) as LocationManager
                val location = locationManager.getLastKnownLocation(LocationManager.GPS_PROVIDER)
                location?.latitude?.toString()
            } else {
                null
            }
        } catch (e: Exception) {
            null
        }
    }
} 