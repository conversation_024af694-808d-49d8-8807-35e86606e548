package com.score.callmetest.util

import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.channels.BufferOverflow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import java.util.concurrent.ConcurrentHashMap

/**
 * 线程安全、支持粘性事件、自动生命周期管理的事件总线
 */
object EventBus {
    // 每种事件类型一个 SharedFlow，支持粘性事件
    private val flows = ConcurrentHashMap<Class<*>, MutableSharedFlow<Any>>()
    private val stickyEvents = ConcurrentHashMap<Class<*>, Any>()

    @Suppress("UNCHECKED_CAST")
    private fun <T : Any> getFlow(eventType: Class<T>, sticky: Boolean = false): MutableSharedFlow<T> {
        return flows.getOrPut(eventType) {
            MutableSharedFlow(
                replay = if (sticky) 1 else 0, // 1: 支持粘性事件
                extraBufferCapacity = 64,
                onBufferOverflow = BufferOverflow.DROP_OLDEST
            )
        } as MutableSharedFlow<T>
    }

    /**
     * 发送事件
     */
    fun <T : Any> post(event: T, sticky: Boolean = false) {
        val eventType = event.javaClass
        if (sticky) stickyEvents[eventType] = event
        getFlow(eventType, sticky).tryEmit(event)
    }

    /**
     * 订阅事件，自动跟随 LifecycleOwner 生命周期，可指定 dispatcher（常用简洁API）
     */
    fun <T : Any> observe(
        owner: LifecycleOwner,
        eventType: Class<T>,
        sticky: Boolean = false,
        dispatcher: CoroutineDispatcher = Dispatchers.Main,
        onEvent: (T) -> Unit
    ) {
        val flow = getFlow(eventType, sticky)
        owner.lifecycleScope.launch(dispatcher) {
            if (sticky) {
                (stickyEvents[eventType] as? T)?.let { onEvent(it) }
            }
            flow.collectLatest { event ->
                owner.lifecycleScope.launch(dispatcher) {
                    onEvent(event)
                }
            }
        }
    }

    /**
     * 高级用法：允许自定义 CoroutineScope 和 dispatcher，适合特殊场景
     */
    fun <T : Any> observe(
        scope: kotlinx.coroutines.CoroutineScope,
        eventType: Class<T>,
        sticky: Boolean = false,
        dispatcher: CoroutineDispatcher = Dispatchers.Main,
        onEvent: (T) -> Unit
    ) {
        val flow = getFlow(eventType, sticky)
        scope.launch(dispatcher) {
            if (sticky) {
                (stickyEvents[eventType] as? T)?.let { onEvent(it) }
            }
            flow.collectLatest { event ->
                scope.launch(dispatcher) {
                    onEvent(event)
                }
            }
        }
    }

    /**
     * 移除某类型的粘性事件
     */
    fun <T : Any> removeSticky(eventType: Class<T>) {
        stickyEvents.remove(eventType)
    }

    /**
     * 清空所有事件
     */
    fun clearAll() {
        flows.clear()
        stickyEvents.clear()
    }
} 