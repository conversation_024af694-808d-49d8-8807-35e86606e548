package com.score.callmetest.util.keyboard

import android.app.Activity
import android.content.Context
import android.os.Build
import android.provider.Settings
import timber.log.Timber
import java.lang.reflect.Method

/**
 * The keyboard height provider, this class uses a PopupWindow to calculate the window height when
 * the floating keyboard is opened and closed.
 */
class KeyboardHeightProvider {

    companion object {
        private const val TAG = "KeyboardHeightProvider"
    }

    private var floatKeyboardPresenter: KeyboardHeightPresenter? = null
    private val keyboardPresenter: KeyboardHeightPresenter
    private var openStatus: Boolean? = null
    private var keyboardHeight = 0
    
    private val internalObserver = object : KeyboardHeightObserver {
        override fun onKeyboardHeightChanged(orientation: Int, isOpen: Boolean, keyboardHeight: Int) {
            observer?.let { obs ->
                if (openStatus == null || 
                    isOpen != openStatus || 
                    keyboardHeight != <EMAIL>) {
                    openStatus = isOpen
                    <EMAIL> = keyboardHeight
                    obs.onKeyboardHeightChanged(orientation, isOpen, keyboardHeight)
                }
            }
        }
    }
    
    private var observer: KeyboardHeightObserver? = null

    constructor(activity: Activity){
        if (Build.VERSION.SDK_INT <= Build.VERSION_CODES.Q && checkOverLayPermission(activity)) {
            // android 10 以下如果存在悬浮窗，且悬浮窗的 flag 包含 FLAG_ALT_FOCUSABLE_IM 时，通过 PopupWindow
            // 的方式测量键盘高度会失效，
            // 此时采用添加悬浮窗的形式来测量
            floatKeyboardPresenter = KeyboardHeightFloatImpl(activity).apply {
                setKeyboardHeightObserver(internalObserver)
            }
        }
        keyboardPresenter = KeyboardHeightPopupImpl(activity).apply {
            setKeyboardHeightObserver(internalObserver)
        }
    }

    /**
     * Start monitoring keyboard height changes
     */
    fun start() {
        keyboardPresenter.start()
        floatKeyboardPresenter?.start()
    }

    /**
     * Stop monitoring keyboard height changes
     */
    fun stop() {
        keyboardPresenter.stop()
        floatKeyboardPresenter?.stop()
    }

    /**
     * Set the keyboard height observer
     * @param observer The observer to be notified of keyboard height changes
     */
    fun setKeyboardHeightObserver(observer: KeyboardHeightObserver?) {
        this.observer = observer
    }

    /**
     * Check if overlay permission is granted
     * @param context The application context
     * @return true if overlay permission is granted, false otherwise
     */
    private fun checkOverLayPermission(context: Context): Boolean {
        var result = true
        
        if (Build.VERSION.SDK_INT >= 23) {
            try {
                /*val method = Settings::class.java.getDeclaredMethod("canDrawOverlays", Context::class.java)
                val booleanValue = method.invoke(null, context) as Boolean*/
                val booleanValue = Settings.canDrawOverlays(context)
                Timber.tag(TAG).i("isFloatWindowOpAllowed allowed: $booleanValue")
                return booleanValue
            } catch (e: NullPointerException) {
                return true
            } catch (e: Exception) {
                Timber.tag(TAG).e(
                    "getDeclaredMethod:canDrawOverlays! Error:${e.message}, etype:${e.javaClass.canonicalName}"
                )
                return true
            }
        } else if (Build.BRAND.lowercase().contains("xiaomi")) {
            val systemService = context.getSystemService(Context.APP_OPS_SERVICE)
            val method: Method? = try {
                Class.forName("android.app.AppOpsManager")
                    .getMethod("checkOp", Int::class.javaPrimitiveType, Int::class.javaPrimitiveType, String::class.java)
            } catch (e: NoSuchMethodException) {
                Timber.tag(TAG).e("NoSuchMethodException method:checkOp! Error:${e.message}")
                null
            } catch (e: ClassNotFoundException) {
                Timber.tag(TAG).e(e, "canDrawOverlays")
                null
            }
            
            method?.let { m ->
                try {
                    val tmp = m.invoke(
                        systemService,
                        24,
                        context.applicationInfo.uid,
                        context.packageName
                    ) as? Int
                    result = tmp != null && tmp == 0
                } catch (e: Exception) {
                    Timber.tag(TAG).e(
                        "call checkOp failed: ${e.message} etype:${e.javaClass.canonicalName}"
                    )
                }
            }
            Timber.tag(TAG).i("isFloatWindowOpAllowed allowed: $result")
            return result
        }
        return true
    }
}
