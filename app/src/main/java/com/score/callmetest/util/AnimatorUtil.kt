package com.score.callmetest.util

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.animation.TimeInterpolator
import android.annotation.SuppressLint
import android.view.View
import android.view.animation.AccelerateDecelerateInterpolator
import android.view.animation.LinearInterpolator
import kotlin.math.max

/**
 * 动画工具类
 * 注意：所有通过本工具类创建的 Animator/AnimatorSet，使用完后请调用 [releaseAnimator] 释放，防止 target 泄漏。
 */
object AnimatorUtil {

    /**
     * 位移动画
     */
    fun translate(
        view: View,
        fromX: Float = 0f,
        toX: Float = 0f,
        fromY: Float = 0f,
        toY: Float = 0f,
        translateDuration: Long = 500,
        onEnd: (() -> Unit)? = null
    ): AnimatorSet {
        // 先终止旧动画，防止多次调用叠加
        view.animate().cancel()
        view.clearAnimation()
        view.visibility = View.VISIBLE
        view.alpha = 1f

        val translateX = ObjectAnimator.ofFloat(view, "translationX", fromX, toX)
        val translateY = ObjectAnimator.ofFloat(view, "translationY", fromY, toY)
        val translateSet = AnimatorSet().apply {
            playTogether(translateX, translateY)
            duration = translateDuration
            interpolator = LinearInterpolator()
        }

        translateSet.addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationEnd(animation: Animator) {
                onEnd?.invoke()
            }
        })
        translateSet.start()
        return translateSet
    }


    /**
     * scale动画
     * @param view 目标View
     * @param scaleFrom 起始缩放（默认0f）
     * @param scaleTo 目标缩放（默认1f）
     * @param scaleDuration 缩放动画时长ms（默认800）
     * @param onEnd 动画全部结束回调
     */
    fun scale(
        view: View,
        scaleFrom: Float = 0f,
        scaleTo: Float = 1f,
        scaleDuration: Long = 800,
        onEnd: (() -> Unit)? = null
    ): AnimatorSet {
        // 先终止旧动画，防止多次调用叠加
        view.animate().cancel()
        view.clearAnimation()
        view.visibility = View.VISIBLE
        view.scaleX = scaleFrom
        view.scaleY = scaleFrom
        view.alpha = 1f

        val scaleX = ObjectAnimator.ofFloat(view, "scaleX", scaleFrom, scaleTo)
        val scaleY = ObjectAnimator.ofFloat(view, "scaleY", scaleFrom, scaleTo)
        val scaleSet = AnimatorSet().apply {
            playTogether(scaleX, scaleY)
            duration = scaleDuration
            interpolator = LinearInterpolator()
        }

        scaleSet.addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationEnd(animation: Animator) {
                view.alpha = 1f // 恢复alpha，便于下次动画
                onEnd?.invoke()
            }
        })
        scaleSet.start()
        return scaleSet
    }

    /**
     * 开始淡出
     * @param [view] 视图
     * @param [fadeDuration] 消失时间
     * @param [onEnd] 在结束
     */
    fun fadeOut(
        view: View,
        fadeDuration: Long,
        onEnd: (() -> Unit)? = null
    ): ObjectAnimator {
        // 先终止旧动画，防止多次调用叠加
        view.animate().cancel()
        view.clearAnimation()
        view.visibility = View.VISIBLE
        view.alpha = 1f

        val fadeOut = ObjectAnimator.ofFloat(view, "alpha", 1f, 0f).apply {
            interpolator = LinearInterpolator()
            duration = fadeDuration
            addListener(object : AnimatorListenerAdapter() {
                override fun onAnimationEnd(animation: Animator) {
                    view.visibility = View.GONE
                    view.alpha = 1f // 恢复alpha，便于下次动画
                    onEnd?.invoke()
                }
            })
        }
        fadeOut.start()
        return fadeOut
    }

    /**
     * 原地转动动画
     * @param view 目标View
     * @param rotationFrom 起始角度（默认0f）
     * @param rotationTo 目标角度（默认360f）
     * @param duration 动画时长ms（默认1000）
     * @param repeatCount 重复次数，默认为无限循环
     * @param onEnd 动画结束回调
     */
    fun rotate(
        view: View,
        rotationFrom: Float = 0f,
        rotationTo: Float = 360f,
        duration: Long = 1000,
        repeatCount: Int = ObjectAnimator.INFINITE,
        onEnd: (() -> Unit)? = null
    ): ObjectAnimator {
        // 先终止旧动画，防止多次调用叠加
        view.animate().cancel()
        view.clearAnimation()
        view.visibility = View.VISIBLE

        val rotation = ObjectAnimator.ofFloat(view, "rotation", rotationFrom, rotationTo).apply {
            interpolator = LinearInterpolator()
            this.duration = duration
            this.repeatCount = repeatCount
            addListener(object : AnimatorListenerAdapter() {
                override fun onAnimationEnd(animation: Animator) {
                    onEnd?.invoke()
                }
            })
        }
        rotation.start()
        return rotation
    }

    /**
     * 旋转并淡出动画
     *
     * ；例🌰：视图以“缓慢启动”的旋转加速度开始转动，同时淡出，在 600ms 后突然停止，但旋转的“感觉”来自 3000ms 的运动设计
     *
     * @param view 目标View
     * @param rotationFrom 起始角度（默认0f）
     * @param rotationTo 目标角度（默认360f）
     * @param rotateDuration 旋转动画时长ms（默认1000）
     * @param fadeDuration 淡出动画时长ms（默认1000）
     * @param onEnd 动画结束回调
     */
    fun rotateAndFadeOut(
        view: View,
        rotationFrom: Float = 0f,
        rotationTo: Float = 360f,
        rotateDuration: Long = 1000,
        fadeDuration: Long = 1000,
        onEnd: (() -> Unit)? = null
    ): AnimatorSet {
        // 先终止旧动画，防止多次调用叠加
        view.animate().cancel()
        view.clearAnimation()
        view.visibility = View.VISIBLE

        val rotation = ObjectAnimator.ofFloat(view, "rotation", rotationFrom, rotationTo)
        val fadeOut = ObjectAnimator.ofFloat(view, "alpha", 1f, 0f)

        // 配置旋转动画
        // 获取一个 rotateDuration 的插值器（比如 AccelerateDecelerate）
        val interpolator = AccelerateDecelerateInterpolator()
        // 创建一个自定义的 TimeInterpolator
        // 它模拟：如果动画运行 rotateDuration，那么在 fadeDuration 时应该转到哪一帧
        rotation.interpolator = TimeInterpolator { input ->
            // input: 0~1，表示当前动画进度（fadeDuration 内）
            // 我们把它映射到 rotateDuration 动画的进度
            val scaledInput = input * fadeDuration / rotateDuration
            // 但 scaledInput 可能 > 1 → 截断
            if (scaledInput > 1f) 1f else interpolator.getInterpolation(scaledInput)
        }

        // 配置淡出动画
        fadeOut.interpolator = LinearInterpolator()

        val animatorSet = AnimatorSet().apply {
            playTogether(rotation, fadeOut)
            this.duration = fadeDuration
            addListener(object : AnimatorListenerAdapter() {
                override fun onAnimationEnd(animation: Animator) {
                    view.visibility = View.GONE
                    view.alpha = 1f // 恢复alpha，便于下次动画
                    onEnd?.invoke()
                }
            })
        }
        animatorSet.start()
        return animatorSet
    }

    /**
     * 安全释放 Animator 及其 target，防止内存泄漏
     * @param animator 需要释放的 Animator 或 AnimatorSet
     */
    fun releaseAnimator(animator: Animator?) {
        animator?.cancel()
        animator?.removeAllListeners()
        if (animator is ObjectAnimator) {
            animator.setTarget(null)
        } else if (animator is AnimatorSet) {
            animator.childAnimations.forEach {
                if (it is ObjectAnimator) {
                    it.setTarget(null)
                }
            }
        }
    }
}