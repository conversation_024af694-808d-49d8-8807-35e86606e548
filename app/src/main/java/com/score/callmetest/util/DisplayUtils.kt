package com.score.callmetest.util

import android.app.Application
import android.content.Context
import android.util.TypedValue

object DisplayUtils {
    // 全局Context
    private var appContext: Context? = null

    fun init(context: Context) {
        appContext = context.applicationContext
    }

    /**
     * dp转px
     */
    fun dp2px(dp: Float): Int {
        val ctx = appContext ?: throw IllegalStateException("DisplayUtils not initialized")
        return TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, dp, ctx.resources.displayMetrics).toInt()
    }

    /**
     * px转dp
     */
    fun px2dp(px: Float): Float {
        val ctx = appContext ?: throw IllegalStateException("DisplayUtils not initialized")
        return px / ctx.resources.displayMetrics.density
    }

    /**
     * sp转px
     */
    fun sp2px(sp: Float): Int {
        val ctx = appContext ?: throw IllegalStateException("DisplayUtils not initialized")
        return TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_SP, sp, ctx.resources.displayMetrics).toInt()
    }

    /**
     * px转sp
     */
    fun px2sp(px: Float): Float {
        val ctx = appContext ?: throw IllegalStateException("DisplayUtils not initialized")
        return px / ctx.resources.displayMetrics.scaledDensity
    }

    // 便于工具类调用的内部dp转px（不抛异常，未初始化返回原值）
    fun dp2pxInternal(dp: Float): Int {
        val ctx = appContext ?: return dp.toInt()
        return TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, dp, ctx.resources.displayMetrics).toInt()
    }

    fun dp2pxInternalFloat(dp: Float): Float {
        val ctx = appContext ?: return dp
        return TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, dp, ctx.resources.displayMetrics)
    }
}

// Kotlin扩展函数
val Float.dp: Int get() = DisplayUtils.dp2px(this)
val Float.sp: Int get() = DisplayUtils.sp2px(this)
val Int.dp: Int get() = DisplayUtils.dp2px(this.toFloat())
val Int.sp: Int get() = DisplayUtils.sp2px(this.toFloat()) 