package com.score.callmetest.util

import android.graphics.drawable.ColorDrawable
import android.graphics.drawable.Drawable
import android.graphics.drawable.GradientDrawable
import android.graphics.drawable.StateListDrawable
import android.view.View
import androidx.annotation.ColorInt
import androidx.core.graphics.drawable.DrawableCompat
import androidx.core.graphics.drawable.toDrawable

object DrawableUtils {
    /**
     * 创建圆角纯色Drawable
     */
    fun createRoundRectDrawable(@ColorInt color: Int, radius: Float): GradientDrawable {
        return GradientDrawable().apply {
            setColor(color)
            cornerRadius = radius
        }
    }

    /**
     * 创建带不同圆角的Drawable
     * @param radii 8个float，分别为左上、右上、右下、左下的x/y
     */
    fun createRoundRectDrawable(@ColorInt color: Int, radii: FloatArray): GradientDrawable {
        return GradientDrawable().apply {
            setColor(color)
            cornerRadii = radii
        }
    }

    /**
     * 创建线性渐变Drawable
     * @param colors 渐变色数组
     * @param orientation 渐变方向
     * @param radius 圆角
     */
    fun createGradientDrawable(
        colors: IntArray,
        orientation: GradientDrawable.Orientation = GradientDrawable.Orientation.LEFT_RIGHT,
        radius: Float = 0f
    ): GradientDrawable {
        return GradientDrawable(orientation, colors).apply {
            cornerRadius = radius
        }
    }

    /**
     * 创建状态选择器Drawable
     * @param normal 正常状态
     * @param pressed 按下状态
     * @param disabled 禁用状态
     */
    fun createStateListDrawable(
        normal: Drawable,
        pressed: Drawable? = null,
        disabled: Drawable? = null
    ): StateListDrawable {
        return StateListDrawable().apply {
            if (pressed != null) addState(intArrayOf(android.R.attr.state_pressed), pressed)
            if (disabled != null) addState(intArrayOf(-android.R.attr.state_enabled), disabled)
            addState(intArrayOf(), normal)
        }
    }

    /**
     * 修改Drawable颜色
     */
    fun tintDrawable(drawable: Drawable, @ColorInt color: Int): Drawable {
        val wrapped = DrawableCompat.wrap(drawable.mutate())
        DrawableCompat.setTint(wrapped, color)
        return wrapped
    }

    /**
     * 将颜色int转为ColorDrawable
     */
    fun colorToDrawable(@ColorInt color: Int): ColorDrawable {
        return color.toDrawable()
    }

    /**
     * 设置View的背景为圆角Drawable
     */
    fun setRoundRectBackground(view: View, @ColorInt color: Int, radius: Float) {
        view.background = createRoundRectDrawable(color, radius)
    }

    /**
     * 创建带描边的圆角纯色Drawable
     */
    fun createRoundRectDrawableWithStroke(
        @ColorInt fillColor: Int,
        radius: Float,
        @ColorInt strokeColor: Int,
        strokeWidth: Int
    ): GradientDrawable {
        return GradientDrawable().apply {
            setColor(fillColor)
            cornerRadius = radius
            setStroke(strokeWidth, strokeColor)
        }
    }

    /**
     * 创建带描边的渐变圆角Drawable
     */
    fun createGradientDrawableWithStroke(
        colors: IntArray,
        orientation: GradientDrawable.Orientation = GradientDrawable.Orientation.LEFT_RIGHT,
        radius: Float = 0f,
        @ColorInt strokeColor: Int,
        strokeWidth: Int
    ): GradientDrawable {
        return GradientDrawable(orientation, colors).apply {
            cornerRadius = radius
            setStroke(strokeWidth, strokeColor)
        }
    }

    /**
     * 创建带虚线描边的圆角Drawable
     */
    fun createRoundRectDashedDrawable(
        @ColorInt fillColor: Int,
        radius: Float,
        @ColorInt strokeColor: Int,
        strokeWidth: Int,
        dashWidth: Float,
        dashGap: Float
    ): GradientDrawable {
        return GradientDrawable().apply {
            setColor(fillColor)
            cornerRadius = radius
            setStroke(strokeWidth, strokeColor, dashWidth, dashGap)
        }
    }

    /**
     * 创建带阴影的圆角Drawable（仅限View背景，需配合setLayerType使用）
     * 注意：Android原生Drawable不直接支持阴影，推荐用CardView/MaterialShapeDrawable或自定义View实现。
     * 这里提供一个简单的方案：设置外部阴影色和偏移，实际阴影效果需配合View属性。
     */
    fun setShadowBackground(
        view: View,
        @ColorInt fillColor: Int,
        radius: Float,
        @ColorInt shadowColor: Int,
        shadowRadius: Float,
        dx: Float = 0f,
        dy: Float = 0f
    ) {
        val drawable = GradientDrawable().apply {
            setColor(fillColor)
            cornerRadius = radius
        }
        // 需关闭硬件加速才能显示阴影（但Drawable本身不支持setShadowLayer）
        view.setLayerType(View.LAYER_TYPE_SOFTWARE, null)
        view.background = drawable
        // 注意：Drawable没有setShadowLayer方法。要实现阴影效果，请使用CardView、MaterialShapeDrawable或View的elevation属性。
    }

    /**
     * 设置Drawable透明度（0-255）
     */
    fun setDrawableAlpha(drawable: Drawable, alpha: Int): Drawable {
        drawable.alpha = alpha
        return drawable
    }

    /**
     * 创建圆角纯色Drawable，支持透明度
     */
    fun createRoundRectDrawableWithAlpha(
        @ColorInt color: Int,
        radius: Float,
        alpha: Int
    ): GradientDrawable {
        return GradientDrawable().apply {
            setColor(color)
            cornerRadius = radius
            this.alpha = alpha
        }
    }

    /**
     * 创建渐变Drawable，支持透明度
     */
    fun createGradientDrawableWithAlpha(
        colors: IntArray,
        orientation: GradientDrawable.Orientation = GradientDrawable.Orientation.LEFT_RIGHT,
        radius: Float = 0f,
        alpha: Int = 255
    ): GradientDrawable {
        return GradientDrawable(orientation, colors).apply {
            cornerRadius = radius
            this.alpha = alpha
        }
    }

    /**
     * 创建圆角纯色Drawable，支持传入dp单位圆角
     */
    fun createRoundRectDrawableDp(@ColorInt color: Int, radiusDp: Float): GradientDrawable {
        val radiusPx = DisplayUtils.dp2pxInternal(radiusDp).toFloat()
        return createRoundRectDrawable(color, radiusPx)
    }

    /**
     * 创建带描边的圆角纯色Drawable，支持传入dp单位圆角
     */
    fun createRoundRectDrawableWithStrokeDp(
        @ColorInt fillColor: Int,
        radiusDp: Float,
        @ColorInt strokeColor: Int,
        strokeWidth: Int
    ): GradientDrawable {
        val radiusPx = DisplayUtils.dp2pxInternalFloat(radiusDp)
        return createRoundRectDrawableWithStroke(fillColor, radiusPx, strokeColor, strokeWidth)
    }
} 