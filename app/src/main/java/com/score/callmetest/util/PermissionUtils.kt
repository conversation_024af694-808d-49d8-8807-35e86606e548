package com.score.callmetest.util

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.provider.Settings
import androidx.activity.result.ActivityResultLauncher
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import java.util.*

/**
 * Android 权限工具类，用于处理运行时权限请求和检查
 */
object PermissionUtils {
    private const val TAG = "PermissionUtils"
    private val permissionCallbacks = mutableMapOf<String, PermissionCallback>()

    /**
     * 检查单个权限是否已授予
     */
    @JvmStatic
    fun checkPermission(context: Context, permission: String): Boolean {
        return ContextCompat.checkSelfPermission(context, permission) == PackageManager.PERMISSION_GRANTED
    }

    /**
     * 检查多个权限是否已授予
     * @return 未被授予的权限列表，如果全部已授予则返回空列表
     */
    @JvmStatic
    fun checkPermissions(context: Context, permissions: Array<String>): List<String> {
        val missingPermissions = mutableListOf<String>()
        for (permission in permissions) {
            if (ContextCompat.checkSelfPermission(context, permission) != PackageManager.PERMISSION_GRANTED) {
                missingPermissions.add(permission)
            }
        }
        return missingPermissions
    }

    /**
     * 请求单个权限
     */
    @JvmStatic
    fun requestPermission(
        activity: AppCompatActivity,
        permission: String,
        requestCode: Int,
        callback: PermissionCallback? = null
    ) {
        if (callback != null) {
            permissionCallbacks["$requestCode"] = callback
        }
        ActivityCompat.requestPermissions(activity, arrayOf(permission), requestCode)
    }

    /**
     * 使用 ActivityResultLauncher 请求单个权限
     */
    @JvmStatic
    fun requestPermission(
        launcher: ActivityResultLauncher<String>,
        permission: String,
        callback: PermissionCallback? = null
    ) {
        launcher.launch(permission)
    }

    /**
     * 请求多个权限
     */
    @JvmStatic
    fun requestPermissions(
        activity: AppCompatActivity,
        permissions: Array<String>,
        requestCode: Int,
        callback: PermissionCallback? = null
    ) {
        if (callback != null) {
            permissionCallbacks["$requestCode"] = callback
        }
        ActivityCompat.requestPermissions(activity, permissions, requestCode)
    }

    /**
     * 使用 ActivityResultLauncher 请求多个权限
     */
    @JvmStatic
    fun requestPermissions(
        launcher: ActivityResultLauncher<Array<String>>,
        permissions: Array<String>,
        callback: PermissionCallback? = null
    ) {
        launcher.launch(permissions)
    }

    /**
     * 处理权限请求结果
     */
    @JvmStatic
    fun handleRequestPermissionsResult(
        activity: Activity,
        requestCode: Int,
        permissions: Array<String>,
        grantResults: IntArray
    ) {
        val callback = permissionCallbacks.remove("$requestCode") ?: return

        val grantedPermissions = mutableListOf<String>()
        val deniedPermissions = mutableListOf<String>()
        val permanentlyDeniedPermissions = mutableListOf<String>()

        for (i in permissions.indices) {
            val permission = permissions[i]
            if (grantResults[i] == PackageManager.PERMISSION_GRANTED) {
                grantedPermissions.add(permission)
            } else {
                deniedPermissions.add(permission)
            }
        }

        // 检查是否有永久拒绝的权限
        for (permission in deniedPermissions) {
            if (!ActivityCompat.shouldShowRequestPermissionRationale(activity, permission)) {
                permanentlyDeniedPermissions.add(permission)
            }
        }

        // 回调结果
        if (deniedPermissions.isEmpty()) {
            callback.onPermissionsGranted(grantedPermissions)
        } else {
            callback.onPermissionsDenied(deniedPermissions, permanentlyDeniedPermissions)
        }
    }

    /**
     * 检查是否应该显示权限解释
     */
    @JvmStatic
    fun shouldShowRequestPermissionRationale(activity: Activity, permission: String): Boolean {
        return ActivityCompat.shouldShowRequestPermissionRationale(activity, permission)
    }

    /**
     * 打开应用设置页面
     */
    @JvmStatic
    fun openAppSettings(context: Context) {
        val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
            // 基础设置页跳转
            data = Uri.fromParts("package", context.packageName, null)
            addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        }
        context.startActivity(intent)
    }

    /**
     * 检查是否有所有需要的权限
     */
    @SuppressLint("LogNotTimber")
    @JvmStatic
    fun hasAllPermissions(context: Context, permissions: Array<String>): Boolean {
        val missingPermissions = checkPermissions(context, permissions)
        android.util.Log.d("PermissionUtils", "Checking permissions: ${permissions.joinToString()}")
        android.util.Log.d("PermissionUtils", "Missing permissions: ${missingPermissions.joinToString()}")
        return missingPermissions.isEmpty()
    }

    /**
     * 检查是否有任意一个权限
     */
    @JvmStatic
    fun hasAnyPermission(context: Context, permissions: Array<String>): Boolean {
        for (permission in permissions) {
            if (checkPermission(context, permission)) {
                return true
            }
        }
        return false
    }

    /**
     * 检查是否是危险权限
     */
    @JvmStatic
    fun isDangerousPermission(activity: Activity, permission: String): Boolean {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.M) return false
        val permissionGroup = getPermissionGroup(activity, permission)
        return permissionGroup != null && permissionGroup != ""
    }

    /**
     * 获取权限所属的权限组
     */
    @JvmStatic
    fun getPermissionGroup(activity: Activity, permission: String): String? {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.M) return null
        return try {
            val pm = activity.packageManager
            val info = pm.getPermissionInfo(permission, 0)
            info.group
        } catch (e: PackageManager.NameNotFoundException) {
            null
        }
    }

    /**
     * 权限回调接口
     */
    interface PermissionCallback {
        /**
         * 权限已授予
         */
        fun onPermissionsGranted(permissions: List<String>)

        /**
         * 权限被拒绝
         * @param deniedPermissions 被拒绝的权限列表
         * @param permanentlyDeniedPermissions 被永久拒绝的权限列表（用户勾选了"不再询问"）
         */
        fun onPermissionsDenied(deniedPermissions: List<String>, permanentlyDeniedPermissions: List<String>)
    }

    /**
     * 简单的权限回调实现，默认空实现
     */
    open class SimplePermissionCallback : PermissionCallback {
        override fun onPermissionsGranted(permissions: List<String>) {}
        override fun onPermissionsDenied(deniedPermissions: List<String>, permanentlyDeniedPermissions: List<String>) {}
    }
}