package com.score.callmetest.util

import android.content.Context
import android.net.Proxy
import com.score.callmetest.CallmeApplication

object ProxyUtils {
    
    /**
     * 检测是否启用代理
     * @return true: 启用代理, false: 未启用代理
     */
    fun isProxyEnabled(): <PERSON><PERSON><PERSON> {
        return try {
            val proxyHost = Proxy.getDefaultHost()
            val proxyPort = Proxy.getDefaultPort()
            !proxyHost.isNullOrEmpty() && proxyPort != -1
        } catch (e: Exception) {
            false
        }
    }
} 