package com.score.callmetest.util

import android.content.Context
import android.net.ConnectivityManager
import android.net.Network
import android.net.NetworkCapabilities
import android.os.Build
import android.util.Log
import com.score.callmetest.CallmeApplication

/**
 * 网络工具类
 * 统一处理不同Android版本的网络API，解决版本适配问题
 * 
 * 网络类型常量：
 * - NETWORK_TYPE_NONE = 0: 无网络
 * - NETWORK_TYPE_WIFI = 1: WiFi网络
 * - NETWORK_TYPE_MOBILE = 2: 移动数据网络
 * - NETWORK_TYPE_UNKNOWN = 3: 未知网络
 */
object NetworkUtils {
    private const val TAG = "dsc--NetworkUtils"
    
    // 网络类型常量
    const val NETWORK_TYPE_NONE = 0
    const val NETWORK_TYPE_WIFI = 1
    const val NETWORK_TYPE_MOBILE = 2
    const val NETWORK_TYPE_UNKNOWN = 3
    
    // 详细网络类型常量（兼容旧版本）
    const val NETWORK_TYPE_INVALID = 0
    const val NETWORK_TYPE_WAP = 1
    const val NETWORK_TYPE_2G = 2
    const val NETWORK_TYPE_3G = 3
    const val NETWORK_TYPE_WIFI_DETAILED = 4
    
    /**
     * 获取ConnectivityManager实例
     */
    private fun getConnectivityManager(context: Context = CallmeApplication.context): ConnectivityManager? {
        return try {
            context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        } catch (e: Exception) {
            Log.e(TAG, "获取ConnectivityManager失败", e)
            null
        }
    }
    
    /**
     * 获取当前活跃网络
     * @return 当前活跃的网络对象，如果无法获取则返回null
     */
    fun getActiveNetwork(context: Context = CallmeApplication.context): Network? {
        return try {
            val connectivityManager = getConnectivityManager(context) ?: return null
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                connectivityManager.activeNetwork
            } else {
                null
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取活跃网络失败", e)
            null
        }
    }
    
    /**
     * 获取网络能力
     * @param network 网络对象
     * @return 网络能力对象，如果无法获取则返回null
     */
    fun getNetworkCapabilities(network: Network?, context: Context = CallmeApplication.context): NetworkCapabilities? {
        return try {
            if (network == null) return null
            
            val connectivityManager = getConnectivityManager(context) ?: return null
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                connectivityManager.getNetworkCapabilities(network)
            } else {
                null
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取网络能力失败", e)
            null
        }
    }
    
    /**
     * 检查网络是否连接
     * @return true: 网络已连接, false: 网络未连接
     */
    fun isNetworkConnected(context: Context = CallmeApplication.context): Boolean {
        return try {
            val connectivityManager = getConnectivityManager(context) ?: return false
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                val network = connectivityManager.activeNetwork
                val capabilities = connectivityManager.getNetworkCapabilities(network)
                capabilities?.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET) ?: false
            } else {
                @Suppress("DEPRECATION")
                val networkInfo = connectivityManager.activeNetworkInfo
                networkInfo?.isConnected ?: false
            }
        } catch (e: Exception) {
            Log.e(TAG, "检查网络连接状态失败", e)
            false
        }
    }
    
    /**
     * 检查是否为WiFi连接
     * @return true: WiFi连接, false: 非WiFi连接
     */
    fun isWifiConnected(context: Context = CallmeApplication.context): Boolean {
        return try {
            val connectivityManager = getConnectivityManager(context) ?: return false
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                val network = connectivityManager.activeNetwork
                val capabilities = connectivityManager.getNetworkCapabilities(network)
                capabilities?.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) ?: false
            } else {
                @Suppress("DEPRECATION")
                val networkInfo = connectivityManager.activeNetworkInfo
                networkInfo?.type == ConnectivityManager.TYPE_WIFI
            }
        } catch (e: Exception) {
            Log.e(TAG, "检查WiFi连接状态失败", e)
            false
        }
    }
    
    /**
     * 检查是否为移动数据连接
     * @return true: 移动数据连接, false: 非移动数据连接
     */
    fun isMobileConnected(context: Context = CallmeApplication.context): Boolean {
        return try {
            val connectivityManager = getConnectivityManager(context) ?: return false
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                val network = connectivityManager.activeNetwork
                val capabilities = connectivityManager.getNetworkCapabilities(network)
                capabilities?.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) ?: false
            } else {
                @Suppress("DEPRECATION")
                val networkInfo = connectivityManager.activeNetworkInfo
                networkInfo?.type == ConnectivityManager.TYPE_MOBILE
            }
        } catch (e: Exception) {
            Log.e(TAG, "检查移动数据连接状态失败", e)
            false
        }
    }
    
    /**
     * 检查是否为VPN连接
     * @return true: VPN连接, false: 非VPN连接
     */
    fun isVpnConnected(context: Context = CallmeApplication.context): Boolean {
        return try {
            val connectivityManager = getConnectivityManager(context) ?: return false
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                val network = connectivityManager.activeNetwork
                val capabilities = connectivityManager.getNetworkCapabilities(network)
                capabilities?.hasTransport(NetworkCapabilities.TRANSPORT_VPN) ?: false
            } else {
                false
            }
        } catch (e: Exception) {
            Log.e(TAG, "检查VPN连接状态失败", e)
            false
        }
    }
    
    /**
     * 获取网络类型
     * @return 网络类型：NETWORK_TYPE_WIFI=WiFi, NETWORK_TYPE_MOBILE=移动数据, NETWORK_TYPE_UNKNOWN=未知, NETWORK_TYPE_NONE=无网络
     */
    fun getNetworkType(context: Context = CallmeApplication.context): Int {
        return try {
            if (!isNetworkConnected(context)) {
                return NETWORK_TYPE_NONE
            }
            
            if (isWifiConnected(context)) {
                return NETWORK_TYPE_WIFI
            }
            
            if (isMobileConnected(context)) {
                return NETWORK_TYPE_MOBILE
            }
            
            NETWORK_TYPE_UNKNOWN
        } catch (e: Exception) {
            Log.e(TAG, "获取网络类型失败", e)
            NETWORK_TYPE_UNKNOWN
        }
    }
    
    /**
     * 获取网络类型描述
     * @return 网络类型描述字符串
     */
    fun getNetworkTypeDescription(context: Context = CallmeApplication.context): String {
        return when (getNetworkType(context)) {
            NETWORK_TYPE_WIFI -> "WiFi"
            NETWORK_TYPE_MOBILE -> "移动数据"
            NETWORK_TYPE_NONE -> "无网络连接"
            else -> "未知网络"
        }
    }
    
    /**
     * 检查网络质量（简单判断）
     * @return true: 网络质量良好, false: 网络质量较差或无网络
     */
    fun isNetworkQualityGood(context: Context = CallmeApplication.context): Boolean {
        return isNetworkConnected(context) && !isVpnConnected(context)
    }
    
    // ==================== 兼容旧版本的方法 ====================
    
    /**
     * 判断网络是否连接（兼容旧版本）
     */
    fun isConnected(context: Context = CallmeApplication.context): Boolean {
        return isNetworkConnected(context)
    }
    
    /**
     * 判断是否是wifi连接（兼容旧版本）
     */
    fun isWifi(context: Context = CallmeApplication.context): Boolean {
        return isWifiConnected(context)
    }
    
    /**
     * 获取详细网络状态，wifi,wap,2g,3g（兼容旧版本）
     *
     * @param context 上下文
     * @return int 网络状态 [.NETWORK_TYPE_2G],[.NETWORK_TYPE_3G],
     * [.NETWORK_TYPE_INVALID],[.NETWORK_TYPE_WAP],[.NETWORK_TYPE_WIFI_DETAILED]
     */
    fun getNetWorkType(context: Context = CallmeApplication.context): Int {
        return when (getNetworkType(context)) {
            NETWORK_TYPE_WIFI -> NETWORK_TYPE_WIFI_DETAILED
            NETWORK_TYPE_MOBILE -> {
                val proxyHost = android.net.Proxy.getDefaultHost()
                if (android.text.TextUtils.isEmpty(proxyHost)) {
                    if (isFastMobileNetwork(context)) NETWORK_TYPE_3G else NETWORK_TYPE_2G
                } else {
                    NETWORK_TYPE_WAP
                }
            }
            NETWORK_TYPE_NONE -> NETWORK_TYPE_INVALID
            else -> NETWORK_TYPE_INVALID
        }
    }
    
    /**
     * 判断是否为快速移动网络（3G及以上）
     * 需要READ_PHONE_STATE权限
     */
    @android.annotation.SuppressLint("MissingPermission")
    private fun isFastMobileNetwork(context: Context = CallmeApplication.context): Boolean {
        return try {
            val telephonyManager = context.getSystemService(Context.TELEPHONY_SERVICE) as android.telephony.TelephonyManager
            when (telephonyManager.networkType) {
                android.telephony.TelephonyManager.NETWORK_TYPE_1xRTT -> false // ~ 50-100 kbps
                android.telephony.TelephonyManager.NETWORK_TYPE_CDMA -> false // ~ 14-64 kbps
                android.telephony.TelephonyManager.NETWORK_TYPE_EDGE -> false // ~ 50-100 kbps
                android.telephony.TelephonyManager.NETWORK_TYPE_EVDO_0 -> true // ~ 400-1000 kbps
                android.telephony.TelephonyManager.NETWORK_TYPE_EVDO_A -> true // ~ 600-1400 kbps
                android.telephony.TelephonyManager.NETWORK_TYPE_GPRS -> false // ~ 100 kbps
                android.telephony.TelephonyManager.NETWORK_TYPE_HSDPA -> true // ~ 2-14 Mbps
                android.telephony.TelephonyManager.NETWORK_TYPE_HSPA -> true // ~ 700-1700 kbps
                android.telephony.TelephonyManager.NETWORK_TYPE_HSUPA -> true // ~ 1-23 Mbps
                android.telephony.TelephonyManager.NETWORK_TYPE_UMTS -> true // ~ 400-7000 kbps
                android.telephony.TelephonyManager.NETWORK_TYPE_EHRPD -> true // ~ 1-2 Mbps
                android.telephony.TelephonyManager.NETWORK_TYPE_EVDO_B -> true // ~ 5 Mbps
                android.telephony.TelephonyManager.NETWORK_TYPE_HSPAP -> true // ~ 10-20 Mbps
                android.telephony.TelephonyManager.NETWORK_TYPE_IDEN -> false // ~25 kbps
                android.telephony.TelephonyManager.NETWORK_TYPE_LTE -> true // ~ 10+ Mbps
                android.telephony.TelephonyManager.NETWORK_TYPE_UNKNOWN -> false
                else -> false
            }
        } catch (e: Exception) {
            Log.e(TAG, "判断快速移动网络失败", e)
            false
        }
    }
    
    // ==================== VPN相关功能 ====================
    
    /**
     * 检测是否启用VPN
     * @return true: 启用VPN, false: 未启用VPN
     */
    fun isVPNEnabled(context: Context = CallmeApplication.context): Boolean {
        return isVpnConnected(context)
    }
    
    /**
     * 检测是否启用VPN（详细版本）
     * @return VPN状态信息
     */
    fun getVPNStatus(context: Context = CallmeApplication.context): VPNStatus {
        return try {
            val isConnected = isVpnConnected(context)
            val networkType = getNetworkType(context)
            val networkDescription = getNetworkTypeDescription(context)
            
            VPNStatus(
                isConnected = isConnected,
                networkType = networkType,
                networkDescription = networkDescription,
                timestamp = System.currentTimeMillis()
            )
        } catch (e: Exception) {
            Log.e(TAG, "获取VPN状态失败", e)
            VPNStatus(
                isConnected = false,
                networkType = NETWORK_TYPE_UNKNOWN,
                networkDescription = "检测失败",
                timestamp = System.currentTimeMillis()
            )
        }
    }
    
    /**
     * 检查VPN连接质量
     * @return VPN连接质量评估
     */
    fun checkVPNQuality(context: Context = CallmeApplication.context): VPNQuality {
        return try {
            val vpnStatus = getVPNStatus(context)
            
            if (!vpnStatus.isConnected) {
                return VPNQuality.NOT_CONNECTED
            }
            
            // 简单的VPN质量检测逻辑
            // 这里可以根据实际需求添加更复杂的检测逻辑
            when (vpnStatus.networkType) {
                NETWORK_TYPE_WIFI -> VPNQuality.GOOD
                NETWORK_TYPE_MOBILE -> VPNQuality.MEDIUM
                else -> VPNQuality.POOR
            }
        } catch (e: Exception) {
            Log.e(TAG, "检查VPN质量失败", e)
            VPNQuality.UNKNOWN
        }
    }
    
    /**
     * 获取VPN连接信息
     * @return VPN连接详细信息
     */
    fun getVPNConnectionInfo(context: Context = CallmeApplication.context): VPNConnectionInfo? {
        return try {
            if (!isVPNEnabled(context)) {
                return null
            }
            
            val connectivityManager = getConnectivityManager(context) ?: return null
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                val network = connectivityManager.activeNetwork
                val capabilities = connectivityManager.getNetworkCapabilities(network)
                
                if (capabilities?.hasTransport(NetworkCapabilities.TRANSPORT_VPN) == true) {
                    VPNConnectionInfo(
                        isConnected = true,
                        hasInternet = capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET),
                        hasValidated = capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED),
                        networkType = getNetworkTypeDescription(context),
                        timestamp = System.currentTimeMillis()
                    )
                } else {
                    null
                }
            } else {
                null
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取VPN连接信息失败", e)
            null
        }
    }
    
    /**
     * VPN状态数据类
     */
    data class VPNStatus(
        val isConnected: Boolean,
        val networkType: Int,
        val networkDescription: String,
        val timestamp: Long
    )
    
    /**
     * VPN连接信息数据类
     */
    data class VPNConnectionInfo(
        val isConnected: Boolean,
        val hasInternet: Boolean,
        val hasValidated: Boolean,
        val networkType: String,
        val timestamp: Long
    )
    
    /**
     * VPN质量枚举
     */
    enum class VPNQuality {
        NOT_CONNECTED,  // 未连接
        POOR,           // 质量差
        MEDIUM,         // 质量中等
        GOOD,           // 质量好
        UNKNOWN         // 未知
    }
} 