package com.score.callmetest.util.keyboard

/**
 * Interface for keyboard height presenter implementations
 * <AUTHOR>
 */
internal interface KeyboardHeightPresenter {
    
    /**
     * Start monitoring keyboard height changes
     */
    fun start()
    
    /**
     * Stop monitoring keyboard height changes
     */
    fun stop()
    
    /**
     * Set the observer to be notified of keyboard height changes
     * @param observer The keyboard height observer
     */
    fun setKeyboardHeightObserver(observer: KeyboardHeightObserver?)
}
