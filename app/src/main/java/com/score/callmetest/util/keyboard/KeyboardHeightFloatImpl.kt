package com.score.callmetest.util.keyboard

import android.app.Activity
import android.content.Context
import android.graphics.PixelFormat
import android.os.Build
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import timber.log.Timber
import kotlin.math.abs

/**
 * Keyboard height implementation using floating window
 * <AUTHOR>
 */
class KeyboardHeightFloatImpl : KeyboardHeightPresenter, View.OnLayoutChangeListener {

    companion object {
        private const val TAG = "KeyboardHeightFloatImpl"
    }

    private val windowManager: WindowManager

    private val view: View
    private var keyboardHeightObserver: KeyboardHeightObserver? = null

    private val activity: Activity

    constructor(activity: Activity) {
        Timber.tag(TAG).d("KeyboardHeightFloatImpl initialized")
        this.activity = activity
        windowManager = activity.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        view = View(activity)
    }

    override fun start() {
        try {
            val parent = view.parent
            view.addOnLayoutChangeListener(this)
            
            when {
                parent is ViewGroup -> {
                    parent.removeView(view)
                    windowManager.addView(view, createLayoutParams())
                }
                parent == null -> {
                    windowManager.addView(view, createLayoutParams())
                }
            }
        } catch (e: Exception) {
            Timber.tag(TAG).e("start: ${e.message}")
            view.removeOnLayoutChangeListener(this)
        }
    }

    override fun stop() {
        try {
            view.removeOnLayoutChangeListener(this)
            if (view.isAttachedToWindow) {
                windowManager.removeViewImmediate(view)
            }
        } catch (e: Exception) {
            Timber.tag(TAG).e("stop: ${e.message}")
        }
    }

    override fun setKeyboardHeightObserver(observer: KeyboardHeightObserver?) {
        keyboardHeightObserver = observer
    }

    /**
     * Create layout parameters for the floating window
     */
    private fun createLayoutParams(): WindowManager.LayoutParams {
        val params = WindowManager.LayoutParams()

        val type = when {
            Build.VERSION.SDK_INT < 24 -> WindowManager.LayoutParams.TYPE_TOAST
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.O -> WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
            else -> WindowManager.LayoutParams.TYPE_PHONE
        }
        
        params.type = type
        params.flags = WindowManager.LayoutParams.FLAG_ALT_FOCUSABLE_IM or
                WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE

        params.format = PixelFormat.TRANSLUCENT
        params.width = 0
        params.height = ViewGroup.LayoutParams.MATCH_PARENT
        params.gravity = Gravity.START
        
        return params
    }

    override fun onLayoutChange(
        v: View,
        left: Int,
        top: Int,
        right: Int,
        bottom: Int,
        oldLeft: Int,
        oldTop: Int,
        oldRight: Int,
        oldBottom: Int
    ) {
        val orientation = getScreenOrientation()
        val oldHeight = oldBottom - oldTop
        val height = bottom - top
        
        if (oldBottom == 0) {
            notifyKeyboardHeightChanged(0, orientation, false)
        } else {
            notifyKeyboardHeightChanged(
                abs(height - oldHeight),
                orientation,
                bottom < oldBottom
            )
        }
    }

    /**
     * Get current screen orientation
     */
    private fun getScreenOrientation(): Int {
        return activity.resources.configuration.orientation
    }

    /**
     * Notify observer about keyboard height changes
     */
    private fun notifyKeyboardHeightChanged(height: Int, orientation: Int, isOpen: Boolean) {
        keyboardHeightObserver?.onKeyboardHeightChanged(orientation, isOpen, height)
    }
}
