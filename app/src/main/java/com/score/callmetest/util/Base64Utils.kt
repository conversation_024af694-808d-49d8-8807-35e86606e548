package com.score.callmetest.util

import android.util.Base64

object Base64Utils {
    /**
     * 编码字符串为Base64
     */
    fun encode(input: String): String =
        Base64.encodeToString(input.toByteArray(Charsets.UTF_8), Base64.NO_WRAP)

    /**
     * 编码字节数组为Base64
     */
    fun encode(input: ByteArray): String =
        Base64.encodeToString(input, Base64.NO_WRAP)

    /**
     * 解码Base64为字符串
     */
    fun decodeToString(base64: String): String =
        String(Base64.decode(base64, Base64.NO_WRAP), Charsets.UTF_8)

    /**
     * 解码Base64为字节数组
     */
    fun decode(base64: String): ByteArray =
        Base64.decode(base64, Base64.NO_WRAP)
} 