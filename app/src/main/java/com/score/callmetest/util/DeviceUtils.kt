package com.score.callmetest.util

import android.Manifest
import android.annotation.SuppressLint
import android.app.ActivityManager
import android.content.Context
import android.content.pm.PackageManager
import android.content.res.Configuration
import android.net.wifi.WifiManager
import android.os.Build
import android.os.Environment
import android.os.StatFs
import android.provider.Settings
import android.telephony.TelephonyManager
import android.util.Log
import androidx.annotation.RequiresPermission
import androidx.core.app.ActivityCompat
import com.score.callmetest.CallmeApplication
import java.io.BufferedReader
import java.io.File
import java.io.FileReader
import java.io.IOException
// 移除 import com.scottyab.rootbeer.RootBeer

/**
 * Android 设备信息工具类
 * 提供设备信息获取、存储信息、网络信息等功能
 */
object DeviceUtils {
    private const val TAG = "DeviceUtils"

    // 调试模式相关常量
    //todo 调试生成随机androidID登录
    private const val DEBUG_DEVICE_ID_ENABLED_KEY = "debug_device_id_enabled"
    private const val DEBUG_DEVICE_ID_VALUE_KEY = "debug_device_id_value"

    
    // 屏幕方向常量
    const val ORIENTATION_UNKNOWN = 0
    const val ORIENTATION_PORTRAIT = 1
    const val ORIENTATION_LANDSCAPE = 2
    
    // ==================== 基础设备信息 ====================

    /**
     * 获取设备唯一标识（Android ID）
     * 支持调试模式下使用随机设备ID
     */
    @SuppressLint("HardwareIds")
    @JvmStatic
    fun getAndroidId(): String {
        return try {
            // 检查是否启用了调试模式的随机设备ID
            //todo 双击logo显示调试弹窗
            if (isDebugDeviceIdEnabled()) {
                getDebugDeviceId()
            } else {
                Settings.Secure.getString(CallmeApplication.context.contentResolver, Settings.Secure.ANDROID_ID)
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取Android ID失败", e)
            ""
        }
    }
    //todo 双击logo显示调试弹窗
    /**
     * 检查是否启用了调试模式的随机设备ID
     */
    @JvmStatic
    fun isDebugDeviceIdEnabled(): Boolean {
        return SharePreferenceUtil.getBoolean(DEBUG_DEVICE_ID_ENABLED_KEY, false)
    }

    /**
     * 设置调试模式的随机设备ID开关
     */
    @JvmStatic
    fun setDebugDeviceIdEnabled(enabled: Boolean) {
        SharePreferenceUtil.putBoolean(DEBUG_DEVICE_ID_ENABLED_KEY, enabled)
        if (enabled && getDebugDeviceId().isEmpty()) {
            // 如果启用调试模式但没有设备ID，则生成一个新的
            generateNewDebugDeviceId()
        }
    }

    /**
     * 获取调试模式的设备ID
     */
    @JvmStatic
    fun getDebugDeviceId(): String {
        return SharePreferenceUtil.getString(DEBUG_DEVICE_ID_VALUE_KEY, "") ?: ""
    }

    /**
     * 生成新的调试设备ID
     */
    @JvmStatic
    fun generateNewDebugDeviceId(): String {
        val random = java.util.Random()
        val bytes = ByteArray(8)
        random.nextBytes(bytes)
        val newDeviceId = bytes.joinToString("") { "%02x".format(it) }
        SharePreferenceUtil.putString(DEBUG_DEVICE_ID_VALUE_KEY, newDeviceId)
        Log.d(TAG, "生成新的调试设备ID: $newDeviceId")
        return newDeviceId
    }
    /**
     * 获取真实的Android ID（不受调试模式影响）
     */
    @SuppressLint("HardwareIds")
    @JvmStatic
    fun getRealAndroidId(): String {
        return try {
            Settings.Secure.getString(CallmeApplication.context.contentResolver, Settings.Secure.ANDROID_ID)
        } catch (e: Exception) {
            Log.e(TAG, "获取真实Android ID失败", e)
            ""
        }
    }
    // =======================

    /**
     * 获取设备型号（如：Redmi K30）
     */
    @JvmStatic
    fun getDeviceModel(): String {
        return Build.MODEL.trim()
    }

    /**
     * 获取设备厂商（如：Xiaomi）
     */
    @JvmStatic
    fun getDeviceManufacturer(): String {
        return Build.MANUFACTURER.trim()
    }

    /**
     * 获取系统版本（如：11）
     */
    @JvmStatic
    fun getSystemVersion(): String {
        return Build.VERSION.RELEASE
    }

    /**
     * 获取系统API级别
     */
    @JvmStatic
    fun getSystemApiLevel(): Int {
        return Build.VERSION.SDK_INT
    }
    
    /**
     * 获取设备指纹
     */
    @JvmStatic
    fun getDeviceFingerprint(): String {
        return Build.FINGERPRINT
    }
    
    /**
     * 获取设备硬件信息
     */
    @JvmStatic
    fun getDeviceHardware(): String {
        return Build.HARDWARE
    }
    
    // ==================== 应用信息 ====================
    
    /**
     * 获取应用版本名称
     */
    @JvmStatic
    fun getAppVersionName(context: Context = CallmeApplication.context): String {
        return try {
            val packageManager = context.packageManager
            val packageInfo = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                packageManager.getPackageInfo(context.packageName, PackageManager.PackageInfoFlags.of(0L))
            } else {
                @Suppress("DEPRECATION")
                packageManager.getPackageInfo(context.packageName, 0)
            }
            packageInfo.versionName ?: "1.0.0"
        } catch (e: Exception) {
            Log.e(TAG, "获取应用版本名称失败", e)
            "1.0.0"
        }
    }

    /**
     * 获取应用版本号
     */
    @JvmStatic
    fun getAppVersionCode(context: Context = CallmeApplication.context): Long {
        return try {
            val packageManager = context.packageManager
            val packageInfo = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                packageManager.getPackageInfo(context.packageName, PackageManager.PackageInfoFlags.of(0L))
            } else {
                @Suppress("DEPRECATION")
                packageManager.getPackageInfo(context.packageName, 0)
            }

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                packageInfo.longVersionCode
            } else {
                @Suppress("DEPRECATION")
                packageInfo.versionCode.toLong()
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取应用版本号失败", e)
            1L
        }
    }
    
    // ==================== 屏幕信息 ====================
    
    /**
     * 获取手机屏幕宽度（像素）
     */
    @JvmStatic
    fun getScreenWidth(context: Context = CallmeApplication.context): Int {
        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                val wm = context.getSystemService(Context.WINDOW_SERVICE) as android.view.WindowManager
                val bounds = wm.currentWindowMetrics.bounds
                bounds.width()
            } else {
                val metrics = context.resources.displayMetrics
                metrics.widthPixels
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取屏幕宽度失败", e)
            0
        }
    }

    /**
     * 获取手机屏幕高度（像素）
     */
    @JvmStatic
    fun getScreenHeight(context: Context = CallmeApplication.context): Int {
        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                val wm = context.getSystemService(Context.WINDOW_SERVICE) as android.view.WindowManager
                val bounds = wm.currentWindowMetrics.bounds
                bounds.height()
            } else {
                val metrics = context.resources.displayMetrics
                metrics.heightPixels
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取屏幕高度失败", e)
            0
        }
    }

    /**
     * 获取屏幕密度（如：2.0）
     */
    @JvmStatic
    fun getScreenDensity(context: Context = CallmeApplication.context): Float {
        return try {
            val metrics = context.resources.displayMetrics
            metrics.density
        } catch (e: Exception) {
            Log.e(TAG, "获取屏幕密度失败", e)
            1.0f
        }
    }

    /**
     * 获取屏幕密度DPI（如：320）
     */
    @JvmStatic
    fun getScreenDensityDpi(context: Context = CallmeApplication.context): Int {
        return try {
            val metrics = context.resources.displayMetrics
            metrics.densityDpi
        } catch (e: Exception) {
            Log.e(TAG, "获取屏幕密度DPI失败", e)
            160
        }
    }

    /**
     * 获取屏幕方向（0:未知 1:竖屏 2:横屏）
     */
    @JvmStatic
    fun getScreenOrientation(context: Context = CallmeApplication.context): Int {
        return try {
            val orientation = context.resources.configuration.orientation
            when (orientation) {
                Configuration.ORIENTATION_PORTRAIT -> ORIENTATION_PORTRAIT
                Configuration.ORIENTATION_LANDSCAPE -> ORIENTATION_LANDSCAPE
                else -> ORIENTATION_UNKNOWN
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取屏幕方向失败", e)
            ORIENTATION_UNKNOWN
        }
    }
    
    // ==================== 内存信息 ====================
    
    /**
     * 获取设备总内存（字节）
     */
    @JvmStatic
    fun getTotalMemory(): Long {
        return try {
            val reader = BufferedReader(FileReader("/proc/meminfo"))
            val line = reader.readLine()
            reader.close()
            val totalMemory = line.substring(line.indexOf(':') + 1).trim()
                .split("\\s+".toRegex()).firstOrNull { it.isNotBlank() }?.toLongOrNull() ?: 0
            totalMemory * 1024 // KB转Byte
        } catch (e: IOException) {
            Log.e(TAG, "获取设备总内存失败", e)
            0
        }
    }

    /**
     * 获取可用内存（字节）
     */
    @JvmStatic
    fun getAvailableMemory(context: Context = CallmeApplication.context): Long {
        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
                val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
                val memoryInfo = ActivityManager.MemoryInfo()
                activityManager.getMemoryInfo(memoryInfo)
                memoryInfo.availMem
            } else {
                // 对于API级别低于16的设备，使用旧方法
                val reader = BufferedReader(FileReader("/proc/meminfo"))
                var line: String?
                var availableMemory = 0L
                while (reader.readLine().also { line = it } != null) {
                    if (line!!.startsWith("MemFree:")) {
                        availableMemory = line.substring(line.indexOf(':') + 1).trim()
                            .split("\\s+".toRegex()).firstOrNull { it.isNotBlank() }?.toLongOrNull() ?: 0
                        break
                    }
                }
                reader.close()
                availableMemory * 1024 // KB转Byte
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取可用内存失败", e)
            0
        }
    }
    
    /**
     * 获取内存使用率
     */
    @JvmStatic
    fun getMemoryUsagePercent(context: Context = CallmeApplication.context): Float {
        return try {
            val total = getTotalMemory()
            val available = getAvailableMemory(context)
            if (total > 0) {
                ((total - available) * 100.0f / total)
            } else {
                0f
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取内存使用率失败", e)
            0f
        }
    }
    
    // ==================== 存储信息 ====================
    
    /**
     * 获取内部存储总空间（字节）
     */
    @JvmStatic
    fun getInternalStorageTotal(): Long {
        return try {
            val path = Environment.getDataDirectory()
            val statFs = StatFs(path.path)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR2) {
                statFs.totalBytes
            } else {
                @Suppress("DEPRECATION")
                (statFs.blockCount.toLong() * statFs.blockSize.toLong())
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取内部存储总空间失败", e)
            0
        }
    }

    /**
     * 获取内部存储可用空间（字节）
     */
    @JvmStatic
    fun getInternalStorageAvailable(): Long {
        return try {
            val path = Environment.getDataDirectory()
            val statFs = StatFs(path.path)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR2) {
                statFs.availableBytes
            } else {
                @Suppress("DEPRECATION")
                (statFs.availableBlocks.toLong() * statFs.blockSize.toLong())
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取内部存储可用空间失败", e)
            0
        }
    }

    /**
     * 获取外部存储总空间（字节）
     */
    @JvmStatic
    fun getExternalStorageTotal(): Long {
        return if (!isExternalStorageAvailable()) 0 else try {
            val path = Environment.getExternalStorageDirectory()
            val statFs = StatFs(path.path)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR2) {
                statFs.totalBytes
            } else {
                @Suppress("DEPRECATION")
                (statFs.blockCount.toLong() * statFs.blockSize.toLong())
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取外部存储总空间失败", e)
            0
        }
    }

    /**
     * 获取外部存储可用空间（字节）
     */
    @JvmStatic
    fun getExternalStorageAvailable(): Long {
        return if (!isExternalStorageAvailable()) 0 else try {
            val path = Environment.getExternalStorageDirectory()
            val statFs = StatFs(path.path)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR2) {
                statFs.availableBytes
            } else {
                @Suppress("DEPRECATION")
                (statFs.availableBlocks.toLong() * statFs.blockSize.toLong())
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取外部存储可用空间失败", e)
            0
        }
    }

    /**
     * 检查外部存储是否可用
     */
    @JvmStatic
    fun isExternalStorageAvailable(): Boolean {
        return try {
            val state = Environment.getExternalStorageState()
            state == Environment.MEDIA_MOUNTED
        } catch (e: Exception) {
            Log.e(TAG, "检查外部存储状态失败", e)
            false
        }
    }
    
    // ==================== 网络信息 ====================
    
    /**
     * 获取网络连接类型
     * @return 网络类型：0-未知 1-WIFI 2-移动数据 3-无网络
     */
    @RequiresPermission(Manifest.permission.ACCESS_NETWORK_STATE)
    @JvmStatic
    fun getNetworkType(context: Context = CallmeApplication.context): Int {
        return NetworkUtils.getNetworkType(context)
    }

    /**
     * 获取网络连接状态描述
     */
    @RequiresPermission(Manifest.permission.ACCESS_NETWORK_STATE)
    @JvmStatic
    fun getNetworkStateDescription(context: Context = CallmeApplication.context): String {
        return NetworkUtils.getNetworkTypeDescription(context)
    }

    /**
     * 获取WiFi MAC地址
     */
    @JvmStatic
    @SuppressLint("HardwareIds")
    fun getWifiMacAddress(context: Context = CallmeApplication.context): String {
        if (ActivityCompat.checkSelfPermission(
                context,
                Manifest.permission.ACCESS_FINE_LOCATION
            ) != PackageManager.PERMISSION_GRANTED
        ) {
            return "需要位置权限"
        }

        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                // Android Q及以上版本无法获取真实MAC地址
                "Android Q及以上版本限制获取MAC地址"
            } else {
                val wifiManager = context.getSystemService(Context.WIFI_SERVICE) as WifiManager
                val wifiInfo = wifiManager.connectionInfo
                wifiInfo.macAddress ?: "未知"
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取WiFi MAC地址失败", e)
            "未知"
        }
    }
    
    // ==================== 设备标识信息 ====================
    
    /**
     * 获取设备IMEI（需要READ_PHONE_STATE权限）
     */
    @RequiresPermission("android.permission.READ_PRIVILEGED_PHONE_STATE")
    @JvmStatic
    @SuppressLint("HardwareIds")
    fun getIMEI(context: Context = CallmeApplication.context): String {
        if (ActivityCompat.checkSelfPermission(
                context,
                Manifest.permission.READ_PHONE_STATE
            ) != PackageManager.PERMISSION_GRANTED
        ) {
            return "需要读取手机状态权限"
        }

        return try {
            val telephonyManager = context.getSystemService(Context.TELEPHONY_SERVICE) as TelephonyManager

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                telephonyManager.imei ?: "未知"
            } else {
                @Suppress("DEPRECATION")
                telephonyManager.deviceId ?: "未知"
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取IMEI失败", e)
            "未知"
        }
    }

    /**
     * 获取设备IMSI（需要READ_PHONE_STATE权限）
     */
    @RequiresPermission("android.permission.READ_PRIVILEGED_PHONE_STATE")
    @JvmStatic
    @SuppressLint("HardwareIds")
    fun getIMSI(context: Context = CallmeApplication.context): String {
        if (ActivityCompat.checkSelfPermission(
                context,
                Manifest.permission.READ_PHONE_STATE
            ) != PackageManager.PERMISSION_GRANTED
        ) {
            return "需要读取手机状态权限"
        }

        return try {
            val telephonyManager = context.getSystemService(Context.TELEPHONY_SERVICE) as TelephonyManager
            @Suppress("DEPRECATION")
            telephonyManager.subscriberId ?: "未知"
        } catch (e: Exception) {
            Log.e(TAG, "获取IMSI失败", e)
            "未知"
        }
    }

    /**
     * 获取手机号（需要READ_PHONE_STATE权限）
     */
    @RequiresPermission(anyOf = [Manifest.permission.READ_SMS, Manifest.permission.READ_PHONE_NUMBERS, Manifest.permission.READ_PHONE_STATE])
    @JvmStatic
    @SuppressLint("HardwareIds")
    fun getPhoneNumber(context: Context = CallmeApplication.context): String {
        if (ActivityCompat.checkSelfPermission(
                context,
                Manifest.permission.READ_PHONE_STATE
            ) != PackageManager.PERMISSION_GRANTED
        ) {
            return "需要读取手机状态权限"
        }

        return try {
            val telephonyManager = context.getSystemService(Context.TELEPHONY_SERVICE) as TelephonyManager
            @Suppress("DEPRECATION")
            telephonyManager.line1Number ?: "未知"
        } catch (e: Exception) {
            Log.e(TAG, "获取手机号失败", e)
            "未知"
        }
    }
    
    // ==================== CPU信息 ====================
    
    /**
     * 获取CPU型号
     */
    @JvmStatic
    fun getCpuModel(): String {
        return try {
            val reader = BufferedReader(FileReader("/proc/cpuinfo"))
            var line: String?
            while (reader.readLine().also { line = it } != null) {
                if (line!!.startsWith("Hardware")) {
                    reader.close()
                    return line.substring(line.indexOf(':') + 1).trim()
                }
            }
            reader.close()
            "未知"
        } catch (e: IOException) {
            Log.e(TAG, "获取CPU型号失败", e)
            "未知"
        }
    }

    /**
     * 获取CPU核心数
     */
    @JvmStatic
    fun getCpuCores(): Int {
        return try {
            // 方法1：通过文件系统获取
            val dir = File("/sys/devices/system/cpu/")
            val files = dir.listFiles() ?: return 1

            var count = 0
            for (file in files) {
                if (file.name.matches(Regex("cpu[0-9]+"))) {
                    count++
                }
            }

            if (count > 0) return count

            // 方法2：通过Runtime获取
            Runtime.getRuntime().availableProcessors()
        } catch (e: Exception) {
            Log.e(TAG, "获取CPU核心数失败", e)
            1
        }
    }
    
    // ==================== 设备检测 ====================
    
    /**
     * 判断是否是模拟器
     */
    @JvmStatic
    fun isEmulator(): Boolean {
        return (Build.BRAND.startsWith("generic") && Build.DEVICE.startsWith("generic"))
                || Build.FINGERPRINT.startsWith("generic")
                || Build.FINGERPRINT.startsWith("unknown")
                || Build.HARDWARE.contains("goldfish")
                || Build.HARDWARE.contains("ranchu")
                || Build.MODEL.contains("google_sdk")
                || Build.MODEL.contains("Emulator")
                || Build.MODEL.contains("Android SDK built for x86")
                || Build.MANUFACTURER.contains("Genymotion")
                || System.getProperty("ro.kernel.qemu") == "1"
    }
    
    /**
     * 检测是否 Root（本地实现，不依赖RootBeer）
     */
    @JvmStatic
    fun isRooted(): Boolean {
        return try {
            val paths = arrayOf(
                "/system/app/Superuser.apk",
                "/sbin/su",
                "/system/bin/su",
                "/system/xbin/su",
                "/data/local/xbin/su",
                "/data/local/bin/su",
                "/system/sd/xbin/su",
                "/system/bin/failsafe/su",
                "/data/local/su"
            )
            for (path in paths) {
                if (java.io.File(path).exists()) {
                    return true
                }
            }
            // 检查是否可以执行su命令
            val process = Runtime.getRuntime().exec("su")
            process.destroy()
            true
        } catch (e: Exception) {
            false
        }
    }

    /**
     * 检测是否运行在多开/虚拟环境（ps+uid 检查/data/data/目录，参考 anti-counterfeit-android）
     */
    @JvmStatic
    fun isRunInVirtual(): Boolean {
        return try {
            val filter = android.os.Process.myUid().toString()
            val process = Runtime.getRuntime().exec("ps")
            val result = process.inputStream.bufferedReader().readText()
            val lines = result.split("\n")
            var exitDirCount = 0
            for (line in lines) {
                if (line.contains(filter)) {
                    val pkgStartIndex = line.lastIndexOf(" ")
                    val processName = line.substring(if (pkgStartIndex <= 0) 0 else pkgStartIndex + 1)
                    val dataFile = java.io.File("/data/data/$processName")
                    if (dataFile.exists()) exitDirCount++
                }
            }
            exitDirCount > 1
        } catch (e: Exception) {
            false
        }
    }

    /**
     * 检测是否被 Hook (Xposed/Substrate/Frida)
     */
    @JvmStatic
    fun isHooked(context: android.content.Context): Boolean {
        // 检查已安装的应用
        try {
            val pm = context.packageManager
            val apps = pm.getInstalledApplications(0)
            for (app in apps) {
                if (app.packageName == "de.robv.android.xposed.installer" ||
                    app.packageName == "com.saurik.substrate" ||
                    app.packageName == "io.github.vvb2060.frida") {
                    return true
                }
            }
        } catch (_: Exception) {}
        // 检查/proc/[pid]/maps
        try {
            val pid = android.os.Process.myPid()
            val reader = java.io.File("/proc/$pid/maps").bufferedReader()
            reader.forEachLine { line ->
                if (line.contains("XposedBridge.jar") || line.contains("com.saurik.substrate") || line.contains("frida")) {
                    return@forEachLine
                }
            }
        } catch (_: Exception) {}
        // 检查调用栈
        try {
            throw Exception("hook check")
        } catch (e: Exception) {
            for (stack in e.stackTrace) {
                if ((stack.className == "de.robv.android.xposed.XposedBridge" && stack.methodName == "main") ||
                    (stack.className == "de.robv.android.xposed.XposedBridge" && stack.methodName == "handleHookedMethod") ||
                    (stack.className == "com.saurik.substrate.MS$2" && stack.methodName == "invoked")) {
                    return true
                }
            }
        }
        return false
    }
    
    // ==================== 工具方法 ====================
    
    /**
     * 格式化文件大小（字节转可读字符串）
     */
    @JvmStatic
    fun formatFileSize(size: Long): String {
        if (size <= 0) return "0 B"

        val units = arrayOf("B", "KB", "MB", "GB", "TB")
        val digitGroups = (Math.log10(size.toDouble()) / Math.log10(1024.0)).toInt()

        return String.format("%.2f %s", size / Math.pow(1024.0, digitGroups.toDouble()), units[digitGroups])
    }

    /**
     * 获取设备信息摘要
     */
    @RequiresPermission(Manifest.permission.ACCESS_NETWORK_STATE)
    @JvmStatic
    fun getDeviceInfoSummary(context: Context = CallmeApplication.context): String {
        return """
            设备型号: ${getDeviceModel()}
            设备厂商: ${getDeviceManufacturer()}
            系统版本: ${getSystemVersion()} (API ${getSystemApiLevel()})
            应用版本: ${getAppVersionName(context)} (${getAppVersionCode(context)})
            屏幕尺寸: ${getScreenWidth(context)}x${getScreenHeight(context)} px
            屏幕密度: ${getScreenDensity(context)} (${getScreenDensityDpi(context)} dpi)
            总内存: ${formatFileSize(getTotalMemory())}
            可用内存: ${formatFileSize(getAvailableMemory(context))}
            内部存储: ${formatFileSize(getInternalStorageAvailable())} / ${formatFileSize(getInternalStorageTotal())}
            外部存储: ${if (isExternalStorageAvailable()) formatFileSize(getExternalStorageAvailable()) else "不可用"} / ${if (isExternalStorageAvailable()) formatFileSize(getExternalStorageTotal()) else "不可用"}
            网络状态: ${getNetworkStateDescription(context)}
            CPU: ${getCpuModel()} (${getCpuCores()}核)
            模拟器: ${if (isEmulator()) "是" else "否"}
            Root: ${if (isRooted()) "是" else "否"}
        """.trimIndent()
    }
}