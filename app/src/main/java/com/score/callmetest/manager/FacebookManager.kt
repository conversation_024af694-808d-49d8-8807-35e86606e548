package com.score.callmetest.manager

import android.content.Context
import android.os.Bundle
import com.facebook.FacebookSdk
import com.facebook.appevents.AppEventsLogger
import com.facebook.internal.FacebookInitProvider
import com.score.callmetest.CallmeApplication
import com.score.callmetest.util.ThreadUtils
import timber.log.Timber
import java.math.BigDecimal
import java.util.Currency

/**
 * Facebook SDK 管理器
 * - 手动初始化，关闭自动初始化（Manifest 中也已关闭）
 * - 不在 ViewModel 中持有 Context；只在初始化时使用 applicationContext
 * - 线程安全、可重复调用但只初始化一次
 */
object FacebookManager {


    /**
     * 手动初始化 Facebook SDK。
     * 注意：需在拉取到远端配置后调用（依赖 app_fb_id 与 app_fb_client_token）。
     * 此方法可多次调用，但仅首次会执行真正的初始化。
     */
    fun init(context: Context = CallmeApplication.context) {
        if(FacebookSdk.isInitialized()) return

        val appId = (AppConfigManager.getConfigValue("app_fb_id") ?: "").trim()
        val clientToken = (AppConfigManager.getConfigValue("app_fb_client_token") ?: "").trim()
        if (appId.isEmpty() || clientToken.isEmpty()) {
            Timber.w("FacebookManager init skipped: missing app_fb_id or app_fb_client_token. appIdEmpty=${appId.isEmpty()} clientTokenEmpty=${clientToken.isEmpty()}")
            return
        }

        // 保证在主线程初始化
        ThreadUtils.runOnMain {
            try {
                synchronized(FacebookManager::class.java) {

                    // 关闭自动初始化，使用手动初始化流程
                    FacebookSdk.setAutoInitEnabled(false)

                    // 设置必要的运行时参数
                    FacebookSdk.setApplicationId(appId)
                    FacebookSdk.setClientToken(clientToken)

                    // 是否允许自动上报 AppEvents，可按需通过服务端配置控制
                    // 当前按需求保持开启；如果需要完全手动控制，可改为 false
                    FacebookSdk.setAutoLogAppEventsEnabled(true)

                    // SDK 初始化（保持与当前依赖兼容的方式）
                    // 若使用的新版本引入了 fullyInitialize()，可替换为：FacebookSdk.fullyInitialize()
                    try {
                        FacebookSdk.sdkInitialize(context.applicationContext) {
                            Timber.d("Facebook SDK initialized via sdkInitialize callback")
                        }
                    } catch (e: Throwable) {
                        Timber.e(e, "Facebook SDK init failed via sdkInitialize callback")
                    }

                    Timber.i("FacebookManager init completed. ")
                }
            } catch (e: Throwable) {
                Timber.e(e, "FacebookManager init error")
            }
        }
    }


    /**
     * 日志购买事件
     * @param [price] 价格
     * @param [currency] 货币
     */
    fun logPurchaseEvent(
        price: Double,
        currency: String,
    ) {
        if (! FacebookSdk.isInitialized()) {
            Timber.w("FacebookManager not initialized. logPurchaseEvent failed.." )
            return
        }
        try {
            val logger = AppEventsLogger.newLogger(CallmeApplication.context)
            logger.logEvent("custom_purchase",price)
            logger.logPurchase(BigDecimal(price), Currency.getInstance(currency))

        } catch (e: Throwable) {
            Timber.e(e, "FacebookManager logPurchaseEvent error")
        }

    }
}