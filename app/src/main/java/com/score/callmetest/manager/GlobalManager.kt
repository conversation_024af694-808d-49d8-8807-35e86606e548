package com.score.callmetest.manager

import android.app.Activity
import android.graphics.Typeface
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.annotation.ColorInt
import androidx.core.content.ContextCompat
import androidx.core.graphics.toColorInt
import androidx.core.view.doOnPreDraw
import androidx.viewpager2.widget.ViewPager2
import com.score.callmetest.CallStatus
import com.score.callmetest.CallmeApplication
import com.score.callmetest.Constant
import com.score.callmetest.R
import com.score.callmetest.im.RongCloudManager
import com.score.callmetest.network.JoinChannelRequest
import com.score.callmetest.network.RetrofitUtils
import com.score.callmetest.util.AgodaUtils
import com.score.callmetest.util.DrawableUtils
import com.score.callmetest.util.ThreadUtils
import com.score.callmetest.ui.message.MessageIncomingManager
import com.score.callmetest.util.SharePreferenceUtil
import com.score.callmetest.util.StatusBarUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

object GlobalManager {

    var isLogined: Boolean = false

    fun addViewStatusBarTopMargin(activity: Activity, view: View) {
        (view.layoutParams as ViewGroup.MarginLayoutParams).topMargin =
            (view.layoutParams as ViewGroup.MarginLayoutParams).topMargin + StatusBarUtils.getStatusBarHeight(activity)
    }

    fun setNeverOverScroll(viewPager: ViewPager2) {
        try {
            (viewPager.getChildAt(0) as? androidx.recyclerview.widget.RecyclerView)?.overScrollMode =
                android.view.View.OVER_SCROLL_NEVER
        } catch (e : Exception) {

        }
    }

    fun setTextTypefaceSansSerif(tv: TextView, bold: Boolean = false) {
        tv.setTypeface(Typeface.create("sans-serif", if (bold) Typeface.BOLD else Typeface.NORMAL));
    }

    fun getStatusColor(status: String) : Int {
        return when(status) {
            CallStatus.ONLINE, CallStatus.AVAILABLE -> {
                ContextCompat.getColor(CallmeApplication.context,R.color.msg_online)
            }
            CallStatus.OFFLINE -> {
                ContextCompat.getColor(CallmeApplication.context,R.color.msg_offline)
            }
            CallStatus.BUSY, CallStatus.IN_CALL -> {
                ContextCompat.getColor(CallmeApplication.context,R.color.msg_busy)
            }
            else -> {
                ContextCompat.getColor(CallmeApplication.context,R.color.msg_offline)
            }
        }
    }

    /**
     * 获取审核模式下的主播在线状态
     * 取userId的后两位，转为数字后对3取余
     */
    fun getReviewOtherStatus(userId: String?) : String {
        return run {
            val lastTwoDigits = userId?.takeLast(2)?.toIntOrNull() ?: 0
            val value = lastTwoDigits % 3
            when (value) {
                0 -> CallStatus.IN_CALL
                1 -> CallStatus.OFFLINE
                else -> CallStatus.BUSY
            }
        }
    }

    fun getMainButtonBgGradientColors() : IntArray {
        return intArrayOf("#FFC720".toColorInt(), "#FF2FBA".toColorInt())
    }

    /**
     * 创建圆角纯色Drawable
     */
    fun setViewRoundBackground(view: View, @ColorInt color: Int) {
        view.doOnPreDraw {
            view.background = DrawableUtils.createRoundRectDrawable(color, view.height / 2f)
        }
    }

    fun onLoginSuccess() {
        isLogined = true
        ThreadUtils.runOnIO {
            AppLifecycleManager.userModeSwitch()
            SharePreferenceUtil.putBoolean(Constant.KEY_IS_FIRST_LOGIN, false)

            SocketManager.instance.initAndConnect()
            RongCloudCommandManager.instance.initialize()

            RechargeManager.login()
            // 自动补单
            RechargeManager.checkPendingOrdersWithGooglePlay()

            // 初始化双通道事件管理器
            DualChannelEventManager.initialize()
            GoodsManager.refreshAllGoods()
            GiftManager.loadAllGift()
            PaymentMethodManager.init(StrategyManager.strategyConfig)
            AdjustManager.reportLastAdjustAttribution(CallmeApplication.context, UserInfoManager.myUserInfo?.userId)

            // 审核模式下读取已经拨打过的用户数据
            StrategyManager.readReviewedUsers()

        }
        // banner
        BannerManager.init()

        TranslateManager.loadCacheAsync()
        // 主动预加载支付渠道并缓存
        PaymentMethodManager.preloadPayChannelList()
    }

    /**
     * 用户登出时清理资源
     */
    fun onLogout() {
        isLogined = false
        // 清理用户信息
        UserInfoManager.clear()
        // 充值相关
        RechargeManager.logout()
        // 断开Socket连接
        SocketManager.instance.disconnect()
        // 清理融云CommandMessage管理器
        RongCloudCommandManager.instance.destroy()
        // 清理双通道事件管理器
        DualChannelEventManager.cleanup()
        // 清理消息弹窗管理器
        MessageIncomingManager.cleanup()
        // 清理支付信息
        PaymentMethodManager.clear()
        // 声网资源释放
        AgodaUtils.release()
        // 融云
        RongCloudManager.cleanup()
        // google取消静默登录
        GoogleSignInManager.signOut()
        // banner
        BannerManager.clearCache()

    }

    /**
     * 删除用户--注销
     */
    fun deleteUser(){
        // 充值相关
        // dsc--注释--可能暂时不用，勿删
//        RechargeManager.cleanOrderInfo()
    }


    /**
     * 应用退出时清理资源
     */
    fun onAppExit() {
    }


}