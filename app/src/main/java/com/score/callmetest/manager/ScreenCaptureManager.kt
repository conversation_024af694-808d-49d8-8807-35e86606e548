package com.score.callmetest.manager

import android.app.Activity
import android.os.Build
import android.view.WindowManager

object ScreenCaptureManager {
    /**
     * 设置防截图和防录屏标志
     */
    fun setSecureFlags(activity: Activity) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.HONEYCOMB) {
            activity.window?.setFlags(
                WindowManager.LayoutParams.FLAG_SECURE,
                WindowManager.LayoutParams.FLAG_SECURE
            )
        }
    }
} 