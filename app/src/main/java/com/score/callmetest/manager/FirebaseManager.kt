package com.score.callmetest.manager

import android.content.Context
import android.os.Bundle
import com.google.firebase.FirebaseApp
import com.google.firebase.analytics.FirebaseAnalytics
import com.score.callmetest.CallmeApplication
import com.score.callmetest.util.ThreadUtils
import timber.log.Timber

/**
 * Firebase 管理器
 * - 手动初始化 Firebase App、Analytics
 * - 不在 ViewModel 中持有 Context；只在初始化时使用 applicationContext
 * - 线程安全、可重复调用但只初始化一次
 * - 提供分析事件上报功能
 */
object FirebaseManager {

    @Volatile
    private var initialized: Boolean = false

    private var firebaseAnalytics: FirebaseAnalytics? = null

    /**
     * 手动初始化 Firebase SDK
     * 此方法可多次调用，但仅首次会执行真正的初始化
     */
    fun init(context: Context = CallmeApplication.context) {
        if (initialized) {
            Timber.d("FirebaseManager already initialized, skip")
            return
        }

        // 保证在主线程初始化
        ThreadUtils.runOnMain {
            try {
                synchronized(FirebaseManager::class.java) {
                    if (initialized) return@runOnMain

                    // 初始化 Firebase App（如果尚未初始化）
                    if (FirebaseApp.getApps(context.applicationContext).isEmpty()) {
                        FirebaseApp.initializeApp(context.applicationContext)
                        Timber.d("Firebase App initialized")
                    } else {
                        Timber.d("Firebase App already initialized")
                    }

                    // 初始化 Firebase Analytics
                    try {
                        firebaseAnalytics = FirebaseAnalytics.getInstance(context.applicationContext)
                        Timber.d("Firebase Analytics initialized")
                    } catch (e: Throwable) {
                        Timber.w(e, "Firebase Analytics initialization failed")
                    }

                    // 开启分析
                    setAnalyticsCollectionEnabled(true)

                    initialized = true
                    Timber.i("FirebaseManager init completed")
                }
            } catch (e: Throwable) {
                Timber.e(e, "FirebaseManager init error")
            }
        }
    }

    /**
     * 是否已经完成初始化
     */
    fun isInitialized(): Boolean = initialized

    // ===== Firebase Analytics 相关方法 =====

    /**
     * 记录自定义事件
     * @param eventName 事件名称
     * @param params 事件参数
     */
    fun logEvent(eventName: String, params: Bundle? = null) {
        if (!initialized || firebaseAnalytics == null) {
            Timber.w("FirebaseManager not initialized or Analytics unavailable. event=%s ignored", eventName)
            return
        }
        try {
            firebaseAnalytics?.logEvent(eventName, params)
            Timber.d("Firebase Analytics event logged: %s", eventName)
        } catch (e: Throwable) {
            Timber.w(e, "Firebase Analytics logEvent failed: %s", eventName)
        }
    }


    /**
     * 设置用户ID
     * @param userId 用户ID
     */
    fun setUserId(userId: String?) {
        if (!initialized || firebaseAnalytics == null) {
            Timber.w("FirebaseManager not initialized or Analytics unavailable. setUserId ignored")
            return
        }
        try {
            firebaseAnalytics?.setUserId(userId)
            Timber.d("Firebase Analytics user ID set: %s", userId)
        } catch (e: Throwable) {
            Timber.w(e, "Firebase Analytics setUserId failed")
        }
    }


    /**
     * 设置分析数据收集是否启用
     * @param enabled 是否启用
     */
    fun setAnalyticsCollectionEnabled(enabled: Boolean) {
        if (!initialized || firebaseAnalytics == null) {
            Timber.w("FirebaseManager not initialized or Analytics unavailable. setAnalyticsCollectionEnabled ignored")
            return
        }
        try {
            firebaseAnalytics?.setAnalyticsCollectionEnabled(enabled)
            Timber.d("Firebase Analytics collection enabled: %s", enabled)
        } catch (e: Throwable) {
            Timber.w(e, "Firebase Analytics setAnalyticsCollectionEnabled failed")
        }
    }

}