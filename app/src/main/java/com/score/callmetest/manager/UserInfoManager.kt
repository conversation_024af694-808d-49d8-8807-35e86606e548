package com.score.callmetest.manager

import android.graphics.drawable.Drawable
import android.os.Handler
import android.os.Looper
import android.util.LruCache
import androidx.media3.common.util.CopyOnWriteMultiset
import com.score.callmetest.CallStatus
import com.score.callmetest.Constant
import com.score.callmetest.entity.CustomEvents
import com.score.callmetest.network.BroadcasterExtraInfo
import com.score.callmetest.network.GetGiftCountResponse
import com.score.callmetest.network.GetPresentedCoinResp
import com.score.callmetest.network.GetUserInfoPostV2Request
import com.score.callmetest.network.GetUserListOnlineStatusPostV2Request
import com.score.callmetest.network.GetUserOnlineStatusPostV2Request
import com.score.callmetest.network.NetworkResult
import com.score.callmetest.network.NotDisturbSwitchRequest
import com.score.callmetest.network.RetrofitUtils
import com.score.callmetest.network.SaveUserInfoRequest
import com.score.callmetest.network.UpdateAvatarRequest
import com.score.callmetest.network.UpdateMediaRequest
import com.score.callmetest.network.UserInfo
import com.score.callmetest.util.EventBus
import com.score.callmetest.util.SharePreferenceUtil
import com.score.callmetest.util.ThreadUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Runnable
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.ConcurrentLinkedDeque
import java.util.concurrent.ConcurrentLinkedQueue
import java.util.concurrent.CopyOnWriteArrayList
import java.util.concurrent.CopyOnWriteArraySet
import java.util.concurrent.atomic.AtomicBoolean
import kotlin.collections.contains

class UserInfoEvent(val userInfo: UserInfo?)

/**
 * 用户信息管理器
 * 负责管理用户信息、金币余额、充值历史等
 */
object UserInfoManager {

    private val userInfoCache = LruCache<String, UserInfo>(15)
    private val extraInfoCache = LruCache<String, BroadcasterExtraInfo>(15)
    private val giftCountCache = LruCache<String, GetGiftCountResponse>(15)
    private val userStatusCache = LruCache<String, String>(50)

    // 进入详情页前的缓存，理论上只存点击时的图就足够了
    private val userAvatarDrawableCache = LruCache<String, Drawable>(2)

    /**
     * {
     * 	"userId": "1941079315940769792",
     * 	"userType": 1,
     * 	"nickname": "Brian",
     * 	"isInternal": false,
     * 	"avatar": "20250623/******************************.png",
     * 	"avatarStatus": 0,
     * 	"hasIllegalAvatar": 0,
     * 	"avatarUrl": "https://test-agilecdn.livchat.me/20250623/******************************.png?Expires=1759400347&OSSAccessKeyId=LTAI5tNPNT6g1inCHkxgcUGz&Signature=GhnKajzLfous94ggWyFWD7OY5Yw%3D&x-oss-process=image%2Fresize%2Cm_fill%2Ch_1080%2Cw_1080%2Climit_1",
     * 	"avatarThumbUrl": "https://test-agilecdn.livchat.me/20250623/******************************.png?Expires=1759400347&OSSAccessKeyId=LTAI5tNPNT6g1inCHkxgcUGz&Signature=A0JwjQ%2FdGgf5Au5110QYQVablXE%3D&x-oss-process=image%2Fresize%2Cm_fill%2Ch_160%2Cw_160%2Climit_1",
     * 	"avatarMiddleThumbUrl": "https://test-agilecdn.livchat.me/20250623/******************************.png?Expires=1759400347&OSSAccessKeyId=LTAI5tNPNT6g1inCHkxgcUGz&Signature=e3Pj%2FV2btvaLXWHtg7z8YVYn6jo%3D&x-oss-process=image%2Fresize%2Cm_fill%2Ch_480%2Cw_480%2Climit_1",
     * 	"avatarRespList": [{
     * 		"mediaPath": "20250623/******************************.png",
     * 		"mediaUrl": "https://test-agilecdn.livchat.me/20250623/******************************.png?Expires=1759400347&OSSAccessKeyId=LTAI5tNPNT6g1inCHkxgcUGz&Signature=GhnKajzLfous94ggWyFWD7OY5Yw%3D&x-oss-process=image%2Fresize%2Cm_fill%2Ch_1080%2Cw_1080%2Climit_1",
     * 		"middleThumbUrl": "https://test-agilecdn.livchat.me/20250623/******************************.png?Expires=1759400347&OSSAccessKeyId=LTAI5tNPNT6g1inCHkxgcUGz&Signature=e3Pj%2FV2btvaLXWHtg7z8YVYn6jo%3D&x-oss-process=image%2Fresize%2Cm_fill%2Ch_480%2Cw_480%2Climit_1",
     * 		"thumbUrl": "https://test-agilecdn.livchat.me/20250623/******************************.png?Expires=1759400347&OSSAccessKeyId=LTAI5tNPNT6g1inCHkxgcUGz&Signature=A0JwjQ%2FdGgf5Au5110QYQVablXE%3D&x-oss-process=image%2Fresize%2Cm_fill%2Ch_160%2Cw_160%2Climit_1",
     * 		"status": 0
     * 	}],
     * 	"mediumList": [],
     * 	"gender": 1,
     * 	"birthday": "1995-01-01",
     * 	"age": 30,
     * 	"country": "USA",
     * 	"pkgName": "com.score.callmetest",
     * 	"mediaList": [],
     * 	"broadcasterType": 0,
     * 	"isAnswer": true,
     * 	"availableCoins": 0,
     * 	"auditStatus": 1,
     * 	"isShowReviewSupplementTips": true,
     * 	"tagsList": ["New"],
     * 	"tagDetails": [{
     * 		"tag": "NEW",
     * 		"tagTip": "Attention: If the call duration is < 40s, you'll not get coins.",
     * 		"tagColor": "#24c45d"
     * 	}, {
     * 		"tag": "Coinless",
     * 		"tagTip": "Send him a recharge link with discount after the call",
     * 		"tagColor": "#999999"
     * 	}],
     * 	"rongcloudToken": "<EMAIL>;hw3r.cn.rongcfg.com",
     * 	"rcToken": "",
     * 	"isRecharge": false,
     * 	"isVip": false,
     * 	"level": 0,
     * 	"followNum": 0,
     * 	"praiseNum": 0,
     * 	"isBlock": false,
     * 	"isSwitchNotDisturbIm": false,
     * 	"isSwitchNotDisturbCall": false,
     * 	"isHavePassword": false,
     * 	"isReview": false,
     * 	"isMultiple": false,
     * 	"registerPkgName": "com.score.callmetest",
     * 	"registerCountry": "CN",
     * 	"isFakeBroadcaster": false,
     * 	"loginPkgName": "com.score.callmetest",
     * 	"hasEquity": false,
     * 	"createTime": "*************",
     * 	"followingNum": 0,
     * 	"sets": {},
     * 	"svipInfo": {},
     * 	"mysteriousInfo": {
     * 		"effective": false
     * 	},
     * 	"isGreenMode": false,
     * 	"analysisLanguage": "zh",
     * 	"isDeleteAccount": false,
     * 	"isRealFriend": false
     * }
     */
    var myUserInfo: UserInfo? = null
        private set

    // 充值历史
    private var rechargeHistory: List<RechargeRecord> = emptyList()

    fun getCachedUserInfo(userId: String): UserInfo? = userInfoCache.get(userId)
    fun putCachedUserInfo(userId: String, userInfo: UserInfo?) {
        if (userInfo != null) userInfoCache.put(userId, userInfo)
    }

    fun getCachedExtraInfo(userId: String): BroadcasterExtraInfo? = extraInfoCache.get(userId)
    fun putCachedExtraInfo(userId: String, extraInfo: BroadcasterExtraInfo?) {
        if (extraInfo != null) extraInfoCache.put(userId, extraInfo)
    }

    fun getCachedGiftCount(userId: String): GetGiftCountResponse? = giftCountCache.get(userId)
    fun putCachedGiftCount(userId: String, giftCount: GetGiftCountResponse?) {
        if (giftCount != null) giftCountCache.put(userId, giftCount)
    }

    fun getCachedStatus(userId: String): String? = userStatusCache.get(userId)
    fun putCachedStatus(userId: String, status: String?) {
        if (status != null) {
            userStatusCache.put(userId, status)
        }
    }

    fun getCachedDrawable(userId: String): Drawable? = userAvatarDrawableCache.get(userId)
    fun putCachedDrawable(userId: String, drawable: Drawable?) {
        if (drawable != null) userAvatarDrawableCache.put(userId, drawable)
    }

    fun updateMyUserInfo(userInfo: UserInfo?) {
        if (userInfo == null) {
            return
        }
        myUserInfo = userInfo
        EventBus.post(UserInfoEvent(myUserInfo))
    }

    /**
     * 获取最新信息，用eventbus来通知
     */
    fun refreshMyUserInfo() {
        ThreadUtils.runOnIO {
            try {
                if (myUserInfo != null) {
                    updateMyUserInfo(fetchUserInfo(myUserInfo!!.userId!!))
                }
            } catch (e: Exception) {

            }
        }
    }

    /**
     * 获取用户信息（通过userId）
     */
    suspend fun fetchUserInfo(userId: String): UserInfo? = withContext(Dispatchers.IO) {
        val resp = RetrofitUtils.dataRepository.getUserInfoPostV2(GetUserInfoPostV2Request(userId))
        if (resp is NetworkResult.Success) {
            val userInfo = resp.data
            if (userInfo != null && !userInfo.userId.isNullOrEmpty()) {
                putCachedUserInfo(userInfo.userId, userInfo)
            }
            userInfo
        } else null
    }

    /**
     * 获取用户信息（先从缓存取，缓存没有再网络获取并缓存）
     *
     *
     * @param [userId] 用户id
     * @param [forceUpdate] 是否强制更新
     * @param [onGetUserInfo] 获取用户信息
     */
    fun getUserInfo(
        userId: String?,
        scope: CoroutineScope? = null,
        forceUpdate: Boolean = false,
        onGetUserInfo: (UserInfo?) -> Unit = {}
    ) {
        if (userId.isNullOrEmpty()) {
            onGetUserInfo.invoke(null)
            return
        }

        var userInfo = getCachedUserInfo(userId)
        if (userInfo != null) {
            onGetUserInfo.invoke(userInfo)
        }

        if (forceUpdate || userInfo == null) {
            var newScope = scope
            if (newScope == null) {
                newScope = CoroutineScope(Dispatchers.IO)
            }
            newScope.launch {
                val resp = RetrofitUtils.dataRepository.getUserInfoPostV2(GetUserInfoPostV2Request(userId))
                userInfo = if (resp is NetworkResult.Success) resp.data else null

                if (userInfo != null && !userInfo!!.userId.isNullOrEmpty()) {
                    putCachedUserInfo(userInfo!!.userId!!, userInfo)
                }

                newScope.launch(Dispatchers.Main) {
                    onGetUserInfo.invoke(userInfo)
                }
            }
        }
    }

    /**
     * 更新用户基本信息
     */
    suspend fun updateBaseUserInfo(
        nickname: String?,
        birthday: String?,
        country: String?,
        about: String?
    ): Boolean = withContext(Dispatchers.IO) {
        val resp = RetrofitUtils.dataRepository.saveUserInfo(
            SaveUserInfoRequest(
                nickname = nickname,
                birthday = birthday,
                country = country,
                about = about
            )
        )
        resp is NetworkResult.Success
    }

    /**
     * 更新用户头像
     */
    suspend fun updateAvatar(ossPath: String): Boolean = withContext(Dispatchers.IO) {
        val resp = RetrofitUtils.dataRepository.updateAvatar(UpdateAvatarRequest(ossPath))
        resp is NetworkResult.Success
    }

    /**
     * 更新/新增/删除用户媒体资源
     * @param actionType 新增=1,更新=2,删除=3
     */
    suspend fun updateMedia(
        actionType: Int,
        mediaPath: String? = null,
        mediaType: String? = "photo",
        replaceMediaId: String? = null,
        deleteMediaId: String? = null,
        coins: Int? = null
    ): Boolean = withContext(Dispatchers.IO) {
        val resp = RetrofitUtils.dataRepository.updateMedia(
            UpdateMediaRequest(
                actionType = actionType,
                mediaPath = mediaPath,
                mediaType = mediaType,
                replaceMediaId = replaceMediaId,
                deleteMediaId = deleteMediaId,
                coins = coins
            )
        )
        resp is NetworkResult.Success
    }

    /**
     * 获取用户充值历史
     */
    fun getRechargeHistory(): List<RechargeRecord> = rechargeHistory

    /**
     * 添加充值记录
     */
    fun addRechargeRecord(record: RechargeRecord) {
        rechargeHistory = rechargeHistory + record
    }

    /**
     * 检查用户是否有充值记录
     */
    fun hasRechargeHistory(): Boolean {
        return rechargeHistory.isNotEmpty()
    }

    /**
     * 获取用户首次充值时间
     */
    fun getFirstRechargeTime(): Long? {
        return rechargeHistory.minByOrNull { it.timestamp }?.timestamp
    }

    /**
     * 获取用户最近充值时间
     */
    fun getLastRechargeTime(): Long? {
        return rechargeHistory.maxByOrNull { it.timestamp }?.timestamp
    }

    /**
     * 获取用户总充值金额
     */
    fun getTotalRechargeAmount(): Double {
        return rechargeHistory.sumOf { it.amount }
    }

    /**
     * 获取用户充值次数
     */
    fun getRechargeCount(): Int {
        return rechargeHistory.size
    }

    /**
     * 清空用户信息和缓存
     */
    fun clear() {
        userInfoCache.evictAll()
        extraInfoCache.evictAll()
        giftCountCache.evictAll()
        myUserInfo = null
        rechargeHistory = emptyList()
        // token
        SharePreferenceUtil.remove(Constant.TOKEN_KEY)
    }

    // 添加好友 - 通过FollowManager
    fun addFriend(scope: CoroutineScope, userId: String, callback: (Boolean) -> Unit) {
        FollowManager.followUser(scope,userId, object : FollowManager.FollowActionCallback {
            override fun onSuccess() {
                callback(true)
            }

            override fun onError(errorMsg: String) {
                callback(false)
            }
        })
    }

    /**
     * 单次查询指定用户状态
     */
    fun loadOnlineStatus(
        scope: CoroutineScope,
        userId: String,
        callback: (String?, Throwable?) -> Unit
    ) {
        scope.launch {
            try {
                val resp = withContext(Dispatchers.IO) {
                    RetrofitUtils.dataRepository.getUserOnlineStatusPostV2(
                        GetUserOnlineStatusPostV2Request(userId)
                    )
                }
                var status = if(resp is NetworkResult.Success){
                    resp.data ?: CallStatus.UNKNOWN
                } else CallStatus.UNKNOWN

                // 适配审核模式
                if (StrategyManager.isReviewPkg()) {
                    getUserInfo(userId,scope = scope) { userInfo ->
                        if (userInfo?.isAnswer == true && !StrategyManager.reviewPkgUsers.contains(userInfo.userId)) {
                            status = CallStatus.ONLINE
                        }else {
                            status = GlobalManager.getReviewOtherStatus(userInfo?.userId)
                        }
                    }
                }
                putCachedStatus(userId, status)
                addUserToRefreshQueue(userId)
                callback(status, null)
                // 处于前台才会下发
                if(AppLifecycleManager.isAppInForeground()) {
                    // eventbus下方通知
                    EventBus.post(CustomEvents.NewStatusEvent(mapOf(userId to status)))
                }
            } catch (e: Exception) {
                callback(null, e)
            }
        }
    }

    // <editor-folder desc="定时查询状态">

    /**
     * 就userid最后刷新状态时间
     */
    private val lastRefreshTimeMap = ConcurrentHashMap<String, Long>()

    /**
     * 需要刷新的用户id队列
     */
    private val refreshUserIds = ConcurrentLinkedQueue<String>()

    private val handler = Handler(Looper.getMainLooper())

    private val REFRESH_IUSER_IDS_MAX = 20 // 队列最大20
    private val REFRESH_INTERVAL = 5_000L // 5秒
    private val CACHE_VALID_DURATION = 5_000L // 5秒

    /**
     * 10秒定时刷新
     */
    private var isTimerActive = false
    private var onPeriodicRefresh: (() -> Unit)? = null
    private val periodicRefreshRunnable = object : Runnable {
        override fun run() {
            if (isTimerActive) {
                onPeriodicRefresh?.invoke()
                handler.postDelayed(this, REFRESH_INTERVAL)
            }
        }
    }

    /**
     * 有些地方初次加载时数据中有最新的状态
     */
    fun putInitialStatus(userId: String, status: String) {
        putCachedStatus(userId, status)
    }

    /**
     * 添加用户到刷新队列
     */
    fun addUserToRefreshQueue(vararg userIds: String) {
        userIds.forEach { userId ->
            // 如果队列里存在userId，移动到队尾
            if (refreshUserIds.contains(userId)) {
                refreshUserIds.remove(userId)
            }
            // 入队
            refreshUserIds.offer(userId)
            // 超出最大限制，删除最旧的
            while (refreshUserIds.size >= REFRESH_IUSER_IDS_MAX) {
                refreshUserIds.poll()
            }
        }
    }

    /**
     * 刷新可见主播状态（带缓存）
     * @param force 强制刷新
     */
    fun refreshUserStatus(
        scope: CoroutineScope,
        force: Boolean = false
    ) {
        // 处于前台才会下发
        if(AppLifecycleManager.isAppInBackground()) {
            // 后台不进行刷新
            return
        }
        val userIds = refreshUserIds.toList()
        if(userIds.isEmpty()){
            return
        }

        scope.launch {
            try {
                val response = RetrofitUtils.dataRepository.getUserListOnlineStatusPostV2(
                    GetUserListOnlineStatusPostV2Request(userIds)
                )
                if (response is NetworkResult.Success) {
                    val newStatuses = response.data ?: emptyMap()
                    val now = System.currentTimeMillis()
                    val postStatus = mutableMapOf<String, String>()
                    newStatuses.forEach { (userId, status) ->
                        // 更新缓存
                        updateStatusCache(userId,status,scope)
                        // 检查时间是否[CACHE_VALID_DURATION]内
                        // 暂时不需要缓存状态更新时间了
//                        if (force || (lastRefreshTimeMap[userId] != null && now - lastRefreshTimeMap[userId]!! < CACHE_VALID_DURATION)) {
                            postStatus[userId] = status
//                        }
//                        lastRefreshTimeMap[userId] = now
                    }
                    Timber.i("refreshUserStatus: $newStatuses")
                    // 处于前台才会下发
                    if(AppLifecycleManager.isAppInForeground() && postStatus.isNotEmpty()) {
                        // eventbus下方通知
                        EventBus.post(CustomEvents.NewStatusEvent(postStatus))
                    }
                }
            } catch (e: Exception) {
                Timber.e(e,"refreshUserStatus exception...")
            }
        }
    }

    /**
     * 更新缓存
     */
    private fun updateStatusCache(id: String,newStatus: String, scope: CoroutineScope) {
        var status = newStatus

        // 适配审核模式
        if (StrategyManager.isReviewPkg()) {
            getUserInfo(id,scope = scope) { userInfo ->
                status = if (userInfo?.isAnswer == true && !StrategyManager.reviewPkgUsers.contains(
                        userInfo.userId
                    )
                ) {
                    CallStatus.ONLINE
                } else {
                    GlobalManager.getReviewOtherStatus(userInfo?.userId)
                }
            }
        }

        putCachedStatus(id, status)
    }

    /**
     * 开启轮询
     */
    fun startStatusUpdatePolling(refreshCallback: () -> Unit) {
        onPeriodicRefresh = refreshCallback
        isTimerActive = true
        handler.removeCallbacks(periodicRefreshRunnable)
        // 首次立即刷新
        refreshCallback()
        // REFRESH_INTERVAL秒后开始定时刷新
        handler.postDelayed(periodicRefreshRunnable, REFRESH_INTERVAL)
    }

    /**
     * 关闭轮询
     */
    fun stopStatusUpdatesPolling() {
        isTimerActive = false
        handler.removeCallbacks(periodicRefreshRunnable)
        onPeriodicRefresh = null
    }

    // </editor-folder>


    fun updateDisturbState(
        scope: CoroutineScope,
        isSwitchNotDisturbCall: Boolean,
        isSwitchNotDisturbIm: Boolean,
        callback: (Boolean) -> Unit
    ) {
        scope.launch {
            try {
                val resp = withContext(Dispatchers.IO) {
                    RetrofitUtils.dataRepository.switchNotDisturb(
                        NotDisturbSwitchRequest(
                            isSwitchNotDisturbCall = isSwitchNotDisturbCall,
                            isSwitchNotDisturbIm = isSwitchNotDisturbIm
                        )
                    )
                }
                val status = if(resp is NetworkResult.Success) {
                    resp.data ?: false
                }else false
                callback(status)
            } catch (e: Exception) {
                callback(false)
            }
        }
    }

    /**
     * 获取注册奖励,
     * 此接口必须每次启动都要调用，否则收不到机器人呼叫。​
     */
    fun getPresentedCoin(
        scope: CoroutineScope,
        callback: (GetPresentedCoinResp?) -> Unit
    ) {
        scope.launch {
            try {
                val resp =  withContext(Dispatchers.IO) {
                    RetrofitUtils.dataRepository.getPresentedCoin()
                }

                val data = if(resp is NetworkResult.Success) {
                    resp.data
                }else null
                scope.launch(Dispatchers.Main) {
                    callback(data)
                }
            } catch (e: Exception) {
                scope.launch(Dispatchers.Main) {
                    callback(null)
                }
            }
        }
    }
}

/**
 * 充值记录数据类
 */
data class RechargeRecord(
    val orderNo: String,
    val amount: Double,
    val coins: Int,
    val payChannel: String,
    val timestamp: Long,
    val status: Int // 1-充值中 2-充值成功 3-充值失败
)