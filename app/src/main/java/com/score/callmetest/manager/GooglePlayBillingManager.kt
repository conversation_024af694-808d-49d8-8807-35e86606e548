package com.score.callmetest.manager

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.os.Handler
import android.os.Looper
import com.android.billingclient.api.AcknowledgePurchaseParams
import com.android.billingclient.api.BillingClient
import com.android.billingclient.api.BillingClientStateListener
import com.android.billingclient.api.BillingFlowParams
import com.android.billingclient.api.BillingResult
import com.android.billingclient.api.ConsumeParams
import com.android.billingclient.api.PendingPurchasesParams
import com.android.billingclient.api.ProductDetails
import com.android.billingclient.api.Purchase
import com.android.billingclient.api.PurchasesUpdatedListener
import com.android.billingclient.api.QueryProductDetailsParams
import com.android.billingclient.api.QueryPurchasesParams
import com.score.callmetest.Constant
import com.score.callmetest.network.PurchaseEventType
import com.score.callmetest.util.ThreadUtils
import timber.log.Timber
import kotlin.collections.listOf

/**
 * 谷歌支付管理类
 *
 *
 * Purchase {
 *   "orderId" : "GPA.3391-8045-5363-4321543",
 *   "packageName" : "com.xxxxxx.xxxxxx",
 *   "productId" : "product_id_123",
 *   "purchaseTime" : *************,
 *   "purchaseState" : 0,
 *   "purchaseToken" : "clejfbmhndfllkmlkgjhmfil.AO-J1OwK0jaR-pkaEghBJ9Erof5jeM_G1Vyvo1D57cvraKppnj3bl5bugzr3I_Md79VuVJxAk20NJZ3HpPfXv7syEncFJzMz8gfdsgrewgfdsa32re4",
 *   "obfuscatedAccountId" : "88f0f835897c465fdsar4342rewf432f3",
 *   "quantity" : 1,
 *   "acknowledged" : false
 * }
 */
object GooglePlayBillingManager {
    @SuppressLint("StaticFieldLeak") // Safe: only holds Application context, never Activity
    private var appContext: Context? = null

    private var billingClient: BillingClient? = null

    private var purchaseCallback: ((Purchase?, Int) -> Unit)? = null

    private val pendingCallbacks = mutableListOf<() -> Unit>()

    // 添加连接状态标记
    private var isConnecting = false

    fun init(context: Context) {
        this.appContext = context.applicationContext
        ThreadUtils.runOnIO {
            ensureReady()
        }
    }

    fun isAvailable(): Boolean {
        try {
            return billingClient?.isReady == true &&
                    billingClient?.isFeatureSupported(BillingClient.FeatureType.IN_APP_MESSAGING)?.responseCode == BillingClient.BillingResponseCode.OK
        } catch (e : Exception) {
            e.printStackTrace()
            return false
        }
    }

    /**
     * 连接服务
     */
    fun ensureReady(onReady: (() -> Unit)? = null) {
        if (isAvailable()) {
            onReady?.invoke()
            return
        }

        if (onReady != null) pendingCallbacks.add(onReady)

        // 如果正在连接中，直接返回，避免重复连接
        if (isConnecting) {
            Timber.tag("dsc--Billing").d("BillingClient is already connecting, waiting...")
            return
        }

        if (billingClient == null) {
            Timber.tag("dsc--Billing").i("Creating BillingClient instance")
            billingClient = BillingClient.newBuilder(this.appContext!!)
                .setListener(object : PurchasesUpdatedListener {
                    override fun onPurchasesUpdated(
                        billingResult: BillingResult,
                        purchases: List<Purchase>?
                    ) {
                        Handler(Looper.getMainLooper()).post {
                            Timber.tag("dsc--Billing")
                                .i("onPurchasesUpdated: code=${billingResult.responseCode}, msg=${billingResult.debugMessage}, purchasesCount=${purchases?.size ?: 0}")

                            if (billingResult.responseCode == BillingClient.BillingResponseCode.OK && purchases != null) {
                                for (purchase in purchases) {
                                    Timber.tag("dsc--Billing")
                                        .i("购买成功: ${purchase.orderId}, token=${purchase.purchaseToken}")
                                    purchaseCallback?.invoke(purchase, 0)
                                }
                            } else if (billingResult.responseCode == BillingClient.BillingResponseCode.USER_CANCELED) {
                                Timber.tag("dsc--Billing").w("用户取消购买")
                                purchaseCallback?.invoke(null, billingResult.responseCode)
                            } else {
                                Timber.tag("dsc--Billing")
                                    .e("购买失败: ${billingResult.debugMessage}")
                                purchaseCallback?.invoke(null, billingResult.responseCode)
                            }
                        }
                    }
                })
                .enablePendingPurchases(
                    PendingPurchasesParams.newBuilder()
                        .enableOneTimeProducts()
                        .build()
                )
                .enableAutoServiceReconnection()
                .build()
        }

        // 检查是否已经连接或正在连接
        if (billingClient?.isReady == true) {
            pendingCallbacks.forEach { it() }
            pendingCallbacks.clear()
            return
        }

        if (!isConnecting) {
            isConnecting = true
            Timber.tag("dsc--Billing").i("Starting BillingClient connection")
            billingClient?.startConnection(object : BillingClientStateListener {
                override fun onBillingSetupFinished(billingResult: BillingResult) {
                    isConnecting = false
                    if (billingResult.responseCode == BillingClient.BillingResponseCode.OK && isAvailable()) {
                        Timber.tag("dsc--Billing").d("BillingClient 连接成功")
                        pendingCallbacks.forEach { it() }
                        pendingCallbacks.clear()
                    } else {
                        Timber.tag("dsc--Billing")
                            .e("BillingClient 连接失败: ${billingResult.debugMessage}")
                        pendingCallbacks.clear()
                    }
                }

                override fun onBillingServiceDisconnected() {
                    isConnecting = false
                    Timber.tag("dsc--Billing").w("BillingClient 断开连接")
                }
            })
        }
    }

    /**
     * 查询商品
     */
    fun queryProducts(
        productIds: List<String>,
        isSubscription: Boolean = false,
        onSuccess: ((Map<String, ProductDetails>) -> Unit)? = null,
        onFail: (Int) -> Unit = {}
    ) {
        val productList = productIds.map {
            QueryProductDetailsParams.Product.newBuilder()
                .setProductId(it)
                .setProductType(if (isSubscription) BillingClient.ProductType.SUBS else BillingClient.ProductType.INAPP)
                .build()
        }
        val params = QueryProductDetailsParams.newBuilder().setProductList(productList).build()
        billingClient?.queryProductDetailsAsync(params) { billingResult, queryProductDetailsResult ->
            if (billingResult.responseCode == BillingClient.BillingResponseCode.OK) {
                Handler(Looper.getMainLooper()).post {
                    if (queryProductDetailsResult.productDetailsList.isNotEmpty()) {
                        val map = mutableMapOf<String, ProductDetails>()
                        for (pd in queryProductDetailsResult.productDetailsList) {
                            map[pd.productId] = pd
                        }
                        onSuccess?.invoke(map)
                    } else {
                        onSuccess?.invoke(emptyMap())
                    }
                }
            } else {
                Handler(Looper.getMainLooper()).post {
                    onFail.invoke(billingResult.responseCode)
                }
            }
        }
    }

    /**
     * 检查用户是否已拥有该商品（消耗型或订阅型）
     */
    fun checkUserOwnsProduct(
        productId: String,
        isSubscription: Boolean = false,
        onResult: (Purchase?) -> Unit
    ) {
        val type =
            if (isSubscription) BillingClient.ProductType.SUBS else BillingClient.ProductType.INAPP
        billingClient?.queryPurchasesAsync(
            QueryPurchasesParams.newBuilder().setProductType(type).build()
        ) { _, purchasesList ->
            val ownedPurchase = purchasesList.find { purchase ->
                purchase.products.contains(productId) &&
                        purchase.purchaseState == Purchase.PurchaseState.PURCHASED &&
                        !purchase.isAcknowledged
            }
            onResult(ownedPurchase)
        }
    }

    fun launchPurchasePage(
        activity: Activity,
        params: BillingFlowParams,
        onSuccess: ((Purchase?, Int) -> Unit)? = null,
        onFail: ((Int, String) -> Unit)? = null
    ) {
        purchaseCallback = onSuccess
        Timber.tag("dsc--Billing").i("launchBillingFlow, params=$params")
        val billingResult = billingClient?.launchBillingFlow(activity, params)
        if (billingResult?.responseCode != BillingClient.BillingResponseCode.OK) {
            Timber.tag("dsc--Billing")
                .e("launchBillingFlow failed: code=${billingResult?.responseCode}, msg=${billingResult?.debugMessage}")
            onFail?.invoke(billingResult?.responseCode ?: -1, "launchBillingFlow failed: code=${billingResult?.responseCode}, msg=${billingResult?.debugMessage}")
        } else {
            // 在PurchasesUpdatedListener回调
        }
    }

    /**
     * 启动时补单，查询所有未消耗的订单
     */
    fun queryUnconsumedPurchases(onPurchase: (Purchase, Boolean) -> Unit) {
        Timber.tag("dsc--Billing").i("queryUnconsumedPurchases called")
        billingClient?.queryPurchasesAsync(
            QueryPurchasesParams.newBuilder().setProductType(BillingClient.ProductType.INAPP)
                .build()
        ) { _, purchasesList ->
            Timber.tag("dsc--Billing").i("queryPurchasesAsync INAPP, count=${purchasesList.size}")
            purchasesList.forEach { onPurchase(it, false) }
        }
        billingClient?.queryPurchasesAsync(
            QueryPurchasesParams.newBuilder().setProductType(BillingClient.ProductType.SUBS).build()
        ) { _, purchasesList ->
            Timber.tag("dsc--Billing").i("queryPurchasesAsync SUBS, count=${purchasesList.size}")
            purchasesList.forEach { onPurchase(it, true) }
        }
    }

    /**
     * 消耗型商品消耗
     */
    fun consumePurchase(
        purchaseToken: String,
        onConsumed: (() -> Unit)? = null,
        onFail: ((Int, String) -> Unit)? = null
    ) {
        Timber.tag("dsc--Billing").i("consumePurchase called, token=$purchaseToken")
        val params = ConsumeParams.newBuilder().setPurchaseToken(purchaseToken).build()
        billingClient?.consumeAsync(params) { billingResult, _ ->
            Timber.tag("dsc--Billing")
                .i("consumeAsync callback: code=${billingResult.responseCode}, msg=${billingResult.debugMessage}")

            if (billingResult.responseCode == BillingClient.BillingResponseCode.OK) {
                Timber.tag("dsc--Billing").d("消费成功")
                onConsumed?.invoke()
            } else {
                Timber.tag("dsc--Billing").e("消费失败: ${billingResult.debugMessage}")
                onFail?.invoke(billingResult.responseCode, billingResult.debugMessage)
            }
        }
    }

    /**
     * 订阅型商品确认
     */
    fun acknowledgePurchase(
        purchaseToken: String,
        onAcknowledged: (() -> Unit)? = null,
        onFail: ((Int, String) -> Unit)? = null
    ) {
        Timber.tag("dsc--Billing").i("acknowledgePurchase called, token=$purchaseToken")
        val params = AcknowledgePurchaseParams.newBuilder().setPurchaseToken(purchaseToken).build()
        billingClient?.acknowledgePurchase(params) { billingResult ->
            Timber.tag("dsc--Billing")
                .i("acknowledgePurchase callback: code=${billingResult.responseCode}, msg=${billingResult.debugMessage}")
            if (billingResult.responseCode == BillingClient.BillingResponseCode.OK) {
                Timber.tag("dsc--Billing").d("确认成功")
                onAcknowledged?.invoke()
            } else {
                Timber.tag("dsc--Billing").e("确认失败: ${billingResult.debugMessage}")
                onFail?.invoke(billingResult.responseCode, billingResult.debugMessage)
            }
        }
    }

} 