package com.score.callmetest.manager

import android.content.Context
import android.media.MediaPlayer
import android.media.MediaRecorder
import android.net.Uri
import android.os.Build
import android.os.Handler
import android.os.Looper
import androidx.core.net.toFile
import timber.log.Timber
import java.io.File
import java.io.IOException
import java.util.concurrent.atomic.AtomicBoolean
import androidx.core.net.toUri
import io.rong.imlib.common.SavePathUtils

/**
 * 音频录制和播放管理类
 * 负责处理语音消息的录制和播放
 *
 * ps. 虽然已经设置录音存放位置file:///data/user/0/com.score.callmetest/cache/audio/xxx.voice
 *      但是，经过融云的发送后，它会自动复制or下载到另外路径，如：file:///data/user/0/com.score.callmetest/files/dXEF9YNr-fUf47ZOt6QFLg_1752844330358/hq_voice/36.aac
 *
 */
internal object AudioRecorderManager {

    // 默认最大录音时长 (毫秒)
    private const val DEFAULT_MAX_RECORD_DURATION = 60000L

    // 媒体录制器
    private var mMediaRecorder: MediaRecorder? = null
    
    // 录音文件uri
    private var mCurrentRecordingFileUri: Uri? = null

    // 录音开始时间
    private var mRecordStartTime: Long = 0
    
    // 录音时长 (毫秒)
    private var mRecordDuration: Long = 0
    
    // 是否正在录音
    private val mIsRecording = AtomicBoolean(false)

    // 录音计时器
    private val mHandler = Handler(Looper.getMainLooper())
    private var mRecordingTimeRunnable: Runnable? = null
    
    // 录音更新监听器 - 返回当前录制时间(毫秒)
    private var mOnRecordingTimeUpdateListener: ((Long) -> Unit)? = null
    
    // 录音错误监听器
    private var mOnRecordErrorListener: ((Exception) -> Unit)? = null

    /**
     * 开始录音
     * @param context 上下文
     * @param maxDurationMs 最大录音时长(毫秒)，默认60秒
     * @param onMaxDurationReached 达到最大录音时间回调
     * @return 返回是否成功开始录音
     */
    fun startRecording(
        context: Context,
        maxDurationMs: Long = DEFAULT_MAX_RECORD_DURATION,
        savePath: String? = null,
        onMaxDurationReached: () -> Unit
    ): Boolean {
        if (mIsRecording.get()) {
            Timber.tag("AudioManager").d("已经在录音中，无需再次开始")
            return false
        }
        
        // 确保之前的录音器已释放
        releaseRecorder()
        
        try {
            // 创建录音文件
            val outputFile = if(savePath.isNullOrEmpty()) {
                val outputDir = context.cacheDir
                SavePathUtils.getSavePath()
//                val outputDir = Environment.getExternalStorageDirectory()
                File(outputDir, generateFileName())
            }else {
                File(savePath)
            }
            // 如果存放文件路径不存在则创建
            outputFile.parentFile?.mkdirs()
            outputFile.parentFile?.setWritable(true,true)
            SavePathUtils.getSavePath()

            mCurrentRecordingFileUri = outputFile.toUri()
            
            // 初始化媒体录制器
            mMediaRecorder = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                MediaRecorder(context)
            } else {
                @Suppress("DEPRECATION")
                MediaRecorder()
            }


            mMediaRecorder?.apply {
                setAudioEncodingBitRate(32000)
                setAudioChannels(1)
                setAudioSource(MediaRecorder.AudioSource.MIC)
                setOutputFormat(MediaRecorder.OutputFormat.AAC_ADTS)
                setAudioEncoder(MediaRecorder.AudioEncoder.AAC)
                setOutputFile(outputFile.toUri().path)
                setMaxDuration(maxDurationMs.toInt())

                // 设置完成监听器
                setOnInfoListener { _, what, _ ->
                    if (what == MediaRecorder.MEDIA_RECORDER_INFO_MAX_DURATION_REACHED) {
                        Timber.tag("AudioManager").d("录音达到最大时长，自动停止")
                        onMaxDurationReached()
                    }
                }
                
                // 准备并开始录音
                prepare()
                start()
            }
            
            // 设置录音状态
            mIsRecording.set(true)
            mRecordStartTime = System.currentTimeMillis()
            
            // 开始计时
            startRecordingTimer()
            
            Timber.tag("AudioManager").d("开始录音: $mCurrentRecordingFileUri")
            return true
            
        } catch (e: Exception) {
            Timber.tag("AudioManager").e("录音失败: ${e.message}")
            mOnRecordErrorListener?.invoke(e)
            releaseRecorder()
            return false
        }
    }
    
    /**
     * 停止录音
     * @return 返回录音文件路径和录音时长(毫秒)，如果录音失败则返回null
     */
    fun stopRecording(): Pair<Uri, Long>? {
        if (!mIsRecording.get()) {
            Timber.tag("AudioManager").d("没有正在进行的录音")
            return null
        }
        
        try {
            // 停止计时器
            stopRecordingTimer()
            
            // 计算录音时长
            mRecordDuration = System.currentTimeMillis() - mRecordStartTime
            
            // 停止录音
            mMediaRecorder?.apply {
                stop()
                release()
            }
            mMediaRecorder = null
            
            // 重置录音状态
            mIsRecording.set(false)
            
            val fileUri = mCurrentRecordingFileUri
//            val filePath = mCurrentRecordingFilePath
            if (fileUri != null) {
                Timber.tag("AudioManager").d("录音完成: $fileUri, 时长: $mRecordDuration ms")
                return Pair(fileUri, mRecordDuration)
            }
            
        } catch (e: Exception) {
            Timber.tag("AudioManager").e("停止录音失败: ${e.message}")
            mOnRecordErrorListener?.invoke(e)
        } finally {
            releaseRecorder()
        }
        
        return null
    }
    
    /**
     * 取消录音
     * 停止录音并删除录音文件
     */
    fun cancelRecording() {
        if (!mIsRecording.get()) {
            Timber.tag("AudioManager").d("没有正在进行的录音")
            return
        }
        
        try {
            // 停止计时器
            stopRecordingTimer()
            
            // 停止录音
            mMediaRecorder?.apply {
                stop()
                release()
            }
            mMediaRecorder = null
            
            // 删除录音文件
            mCurrentRecordingFileUri?.let {
                val file = it.toFile()
                if (file.exists()) {
                    file.delete()
                    Timber.tag("AudioManager").d("已删除录音文件: $it")
                }
            }
            
        } catch (e: Exception) {
            Timber.tag("AudioManager").e("取消录音失败: ${e.message}")
        } finally {
            // 重置录音状态
            mIsRecording.set(false)
            mMediaRecorder = null
            mCurrentRecordingFileUri = null
        }
    }

    
    /**
     * 设置录音时间更新监听器
     */
    fun setOnRecordingTimeUpdateListener(listener: (Long) -> Unit) {
        mOnRecordingTimeUpdateListener = listener
    }
    
    /**
     * 设置录音错误监听器
     */
    fun setOnRecordErrorListener(listener: (Exception) -> Unit) {
        mOnRecordErrorListener = listener
    }
    
    /**
     * 获取当前录音时长 (毫秒)
     */
    fun getCurrentRecordingDuration(): Long {
        return if (mIsRecording.get()) {
            System.currentTimeMillis() - mRecordStartTime
        } else {
            mRecordDuration
        }
    }
    
    /**
     * 是否正在录音
     */
    fun isRecording(): Boolean {
        return mIsRecording.get()
    }

    
    /**
     * 释放资源
     * 在Activity/Fragment销毁时调用，防止内存泄漏
     */
    fun release() {
        stopRecordingTimer()
        releaseRecorder()
        mOnRecordingTimeUpdateListener = null
        mOnRecordErrorListener = null
    }

    // 生成录音文件名
    private fun generateFileName(): String {
//        val timeStamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
        val timeStamp = "audio/${System.currentTimeMillis()}temp.voice"
        return timeStamp
    }
    
    /**
     * 开始录音计时器
     */
    private fun startRecordingTimer() {
        // 停止已有的计时器
        stopRecordingTimer()
        
        // 创建新的计时器
        mRecordingTimeRunnable = object : Runnable {
            override fun run() {
                if (mIsRecording.get()) {
                    val currentDuration = System.currentTimeMillis() - mRecordStartTime
                    // 通知录音时间更新
                    mOnRecordingTimeUpdateListener?.invoke(currentDuration)
                    // 继续计时
                    mHandler.postDelayed(this, 100)
                }
            }
        }
        
        // 启动计时器
        mHandler.post(mRecordingTimeRunnable!!)
    }
    
    /**
     * 停止录音计时器
     */
    private fun stopRecordingTimer() {
        mRecordingTimeRunnable?.let {
            mHandler.removeCallbacks(it)
            mRecordingTimeRunnable = null
        }
    }
    
    /**
     * 释放录音器资源
     */
    private fun releaseRecorder() {
        try {
            mMediaRecorder?.apply {
                stop()
            }
        } catch (e: Exception) {
            Timber.tag("AudioManager").e("停止录音器异常: ${e.message}")
        }
        
        try {
            mMediaRecorder?.release()
        } catch (e: Exception) {
            Timber.tag("AudioManager").e("释放录音器异常: ${e.message}")
        }
        
        mMediaRecorder = null
        mIsRecording.set(false)
    }
} 