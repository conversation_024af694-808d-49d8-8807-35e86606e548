package com.score.callmetest.manager

import android.content.Context
import com.android.installreferrer.api.InstallReferrerClient
import com.android.installreferrer.api.InstallReferrerStateListener
import com.score.callmetest.entity.InstallReferrerEntity
import com.google.gson.Gson
import com.score.callmetest.network.NetworkResult
import com.score.callmetest.network.RetrofitUtils
import com.score.callmetest.util.ThreadUtils
import timber.log.Timber
import androidx.core.content.edit

object InstallReferrerManager {
    private const val PREFS_NAME = "install_referrer_prefs"
    private const val KEY_DATA = "install_referrer_data"
    private const val KEY_REPORTED = "install_referrer_reported"

    private var referrerClient: InstallReferrerClient? = null

    fun init(context: Context) {
        if (isReported(context)) {
            return
        }
        referrerClient = InstallReferrerClient.newBuilder(context).build()
        referrerClient?.startConnection(object : InstallReferrerStateListener {
            override fun onInstallReferrerSetupFinished(responseCode: Int) {
                when (responseCode) {
                    InstallReferrerClient.InstallReferrerResponse.OK -> {
                        try {
                            referrerClient?.run {
                                val referrerDetails = installReferrer
                                val entity = InstallReferrerEntity(
                                    referrerUrl = referrerDetails.installReferrer ?: "",
                                    installVersion = referrerDetails.installVersion ?: "unknown",
                                    appInstallTime = referrerDetails.installBeginTimestampSeconds,
                                    appInstallServerTime = referrerDetails.installBeginTimestampServerSeconds,
                                    referrerClickTime = referrerDetails.referrerClickTimestampSeconds,
                                    referrerClickServerTime = referrerDetails.referrerClickTimestampServerSeconds
                                )
                                save(context, entity)
                                Timber.tag("dsc--InstallReferrer").d("InstallReferrer saved: $entity")
                            }
                            // release
                            release()

                        } catch (e: Exception) {
                            Timber.tag("dsc--InstallReferrer").e(e, "Failed to get install referrer")
                        }
                    }
                    InstallReferrerClient.InstallReferrerResponse.FEATURE_NOT_SUPPORTED -> {
                        Timber.tag("dsc--InstallReferrer").w("InstallReferrer API not supported")
                    }
                    InstallReferrerClient.InstallReferrerResponse.SERVICE_UNAVAILABLE -> {
                        Timber.tag("dsc--InstallReferrer").w("InstallReferrer service unavailable")
                    }
                }
            }
            override fun onInstallReferrerServiceDisconnected() {
                Timber.tag("dsc--InstallReferrer").w("InstallReferrer service disconnected")
            }
        })
    }

    private fun release() {
        referrerClient?.endConnection()
        referrerClient = null
    }

    private fun save(context: Context, entity: InstallReferrerEntity) {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        prefs.edit {
            putString(KEY_DATA, Gson().toJson(entity))
            putBoolean(KEY_REPORTED, false)
        }
    }

    private fun get(context: Context): InstallReferrerEntity? {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        val json = prefs.getString(KEY_DATA, null) ?: return null
        return Gson().fromJson(json, InstallReferrerEntity::class.java)
    }

    private fun isReported(context: Context): Boolean {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        return prefs.getBoolean(KEY_REPORTED, false)
    }

    private fun setReported(context: Context) {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        prefs.edit { putBoolean(KEY_REPORTED, true) }
    }

    fun tryReport(context: Context) {
        if (!isReported(context)) {
            val entity = get(context) ?: return
            ThreadUtils.runOnIO {
                try {
                    val response = RetrofitUtils.dataRepository.submitInstallReferrer(entity)
                    if (response is NetworkResult.Success && response.data == true) {
                        setReported(context)
                        Timber.tag("dsc--InstallReferrer").i("InstallReferrer reported successfully: $entity")
                    } else {
                        Timber.tag("dsc--InstallReferrer").w("InstallReferrer report failed: ${response}, $entity")
                    }
                } catch (e: Exception) {
                    Timber.tag("dsc--InstallReferrer").e(e, "InstallReferrer report exception")
                }
            }
        }
    }

} 