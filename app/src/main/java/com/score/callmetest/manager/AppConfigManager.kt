package com.score.callmetest.manager

import android.util.Log
import com.score.callmetest.Constant
import com.score.callmetest.network.AppConfigData
import com.score.callmetest.network.BaseResponse
import com.score.callmetest.network.ConfigItem
import com.score.callmetest.network.ConfigPostV2
import com.score.callmetest.network.DecryptedAppConfig
import com.score.callmetest.network.NetworkResult
import com.score.callmetest.network.NoNetworkException
import com.score.callmetest.network.RetrofitUtils
import com.score.callmetest.network.RiskControlInfoConfig
import com.score.callmetest.util.AESUtils
import com.score.callmetest.util.Base64Utils
import com.score.callmetest.util.SharePreferenceUtil
import com.score.callmetest.util.ThreadUtils
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonElement
import kotlinx.serialization.encodeToString
import kotlinx.serialization.decodeFromString
import kotlinx.serialization.json.jsonPrimitive
import kotlinx.serialization.json.intOrNull
import kotlinx.serialization.json.longOrNull
import kotlinx.serialization.json.doubleOrNull
import kotlinx.serialization.json.booleanOrNull
import kotlinx.serialization.json.floatOrNull
import kotlinx.serialization.json.contentOrNull
import kotlinx.serialization.json.JsonPrimitive
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response
import timber.log.Timber

object AppConfigManager {
    private var appConfigData: AppConfigData? = null

    /**
     * DecryptedAppConfig 解密后
     * {
     * 	"rvsta": "2",
     * 	"ver": "1",
     * 	"items": [{
     * 		"data": "AIzaSyCY4MncZdTNDX22qyoXBCndAsQALC0gFtU",
     * 		"name": "glt"
     * 	}, {
     * 		"data": "qf3d5gbjq92th",
     * 		"name": "rck"
     * 	}, {
     * 		"data": "z3v5yqkbzaz10",
     * 		"name": "rc_app_key"
     * 	}, {
     * 		"data": "SG",
     * 		"name": "rc_area_code"
     * 	}, {
     * 		"data": "nav.sg-light-edge.com",
     * 		"name": "rc_navi_server"
     * 	}, {
     * 		"data": "stats.sg-light-edge.com",
     * 		"name": "rc_stat_server"
     * 	}, {
     * 		"data": "e72270b7a0b4463989252f55e68ff731",
     * 		"name": "rtck"
     * 	}, {
     * 		"data": 396347616,
     * 		"name": "zego_app_id"
     * 	}, {
     * 		"data": "0.1",
     * 		"name": "simple-performance-network"
     * 	}, {
     * 		"data": "true",
     * 		"name": "log_cdn_performance"
     * 	}, {
     * 		"data": "0",
     * 		"name": "tpp_ua"
     * 	}, {
     * 		"data": "0",
     * 		"name": "im_greeting"
     * 	}, {
     * 		"data": "0",
     * 		"name": "anchor_mask"
     * 	}, {
     * 		"data": "1",
     * 		"name": "user_behavior_submit_enable"
     * 	}, {
     * 		"data": 3600,
     * 		"name": "mg_broadcaster_call_enable_max_seconds"
     * 	}, {
     * 		"data": "beade28e64674a59bd1d704032bd9f41",
     * 		"name": "encrypt_key"
     * 	}, {
     * 		"data": "true",
     * 		"name": "is_encrypt"
     * 	}, {
     * 		"data": "false",
     * 		"name": "is_open_invitation"
     * 	}, {
     * 		"data": "1",
     * 		"name": "tpp_open_type"
     * 	}, {
     * 		"name": "app_ext_data"
     * 	}, {
     * 		"data": "https://translation.googleapis.com/language/translate/v2?key=AIzaSyCY4MncZdTNDX22qyoXBCndAsQALC0gFtU",
     * 		"name": "translate_v2"
     * 	}, {
     * 		"data": "AIzaSyCY4MncZdTNDX22qyoXBCndAsQALC0gFtU",
     * 		"name": "google_translation_key"
     * 	}, {
     * 		"data": "",
     * 		"name": "microsoft_translation_key"
     * 	}, {
     * 		"data": 0,
     * 		"name": "translate_type"
     * 	}, {
     * 		"data": "false",
     * 		"name": "is_enable_email_login"
     * 	}, {
     * 		"data": "1",
     * 		"name": "anchor_netspeedcheck_enable"
     * 	}, {
     * 		"data": 0.3,
     * 		"name": "hack_revenue_factor"
     * 	}, {
     * 		"data": "AF",
     * 		"name": "attribution_sdk"
     * 	}, {
     * 		"data": {
     * 			"sizeLimit": 30,
     * 			"interval": 172800000,
     * 			"url": "https://dldir1.qq.com/weixin/Windows/WeChatSetup.exe"
     * 		},
     * 		"name": "anchor_netspeedcheck"
     * 	}, {
     * 		"data": "1000",
     * 		"name": "time_log_upload_num"
     * 	}, {
     * 		"data": "0",
     * 		"name": "time_log_upload_backup"
     * 	}, {
     * 		"data": {
     * 			"ver": "1.0.0",
     * 			"pkgname": "安卓-Callme",
     * 			"always_show": "false",
     * 			"force": "false",
     * 			"upgradeContent": "",
     * 			"source": 1,
     * 			"url": "4"
     * 		},
     * 		"name": "upgrade"
     * 	}, {
     * 		"data": 0,
     * 		"name": "anchor_beauty_min_frame"
     * 	}, {
     * 		"data": 200,
     * 		"name": "anchor_beauty_max_cost"
     * 	}, {
     * 		"data": 20,
     * 		"name": "anchor_beauty_min_time"
     * 	}, {
     * 		"data": 1,
     * 		"name": "pgstts"
     * 	}],
     * 	"riskControlInfoConfig": {
     * 		"k_interval": 5,
     * 		"k_factor": "ay377y1sfk089qhd",
     * 		"k_factor_num": "HOY184"
     * 	}
     * }
     */
    private var decryptedAppConfig: DecryptedAppConfig? = null

    fun getAppConfig(
        ver: Int = 0,
        onSuccess: (AppConfigData) -> Unit,
        onError: (String) -> Unit
    ) {
        ThreadUtils.runOnIO {
            try {
                val response = RetrofitUtils.dataRepository.getAppConfigPostV2(ver)
                if (response is NetworkResult.Success) {
                    val data = response.data
                    if (data != null) {
                        onSuccess(data)
                    } else {
                        onError("-1")
                    }
                } else {
                    val errorMsg = if(response is NetworkResult.Error){
                        if(!response.message.startsWith("Server error")){
                            response.message
                        }else {
                            "-1"
                        }
                    }else {
                        "-1"
                    }
                    onError(errorMsg)
                }
            } catch (e: Exception) {
                Timber.e(e)
                onError("-1")
            }
        }
    }

    fun decryptAppConfig(data: AppConfigData) {
        try {
            // 1. Base64解码equinox和tau
            val equinoxDecoded = Base64Utils.decodeToString(data.equinox ?: "")
            val tauDecoded = Base64Utils.decodeToString(data.tau ?: "")

            // 2. 拼接得到encrypt_key
            val encryptKey = equinoxDecoded + tauDecoded
            SharePreferenceUtil.putString(Constant.ENCRYPT_KEY, encryptKey)

            // 3. Base64解码theta得到need_decrypt_content
            val needDecryptContent = Base64Utils.decodeToString(data.theta ?: "")

            // 4. 用AES解密
            val decryptedContent = AESUtils.decrypt(needDecryptContent, encryptKey)

            // 5. 解析解密后的JSON内容为结构化数据
            try {
                decryptedAppConfig =
                    Json.Default.decodeFromString<DecryptedAppConfig>(decryptedContent)
                Log.d("AppConfigUtils", "Decrypted and parsed config: $decryptedAppConfig")
            } catch (e: Exception) {
                Log.e("AppConfigUtils", "Failed to parse decrypted content as JSON", e)
                // 如果解析失败，设置为null
                decryptedAppConfig = null
            }

            Log.d("AppConfigUtils", "Decrypted content: $decryptedContent")
        } catch (e: Exception) {
            Log.e("AppConfigUtils", "Decrypt app config failed", e)
            decryptedAppConfig = null
        }
    }

    fun saveAppConfig(data: AppConfigData) {
        appConfigData = data
        decryptAppConfig(data)
        
        // 保存到SharedPreferences
        saveAppConfigToSP(data)
    }

    /**
     * 将应用配置保存到SharedPreferences
     */
    private fun saveAppConfigToSP(data: AppConfigData) {
        try {
            // 将整个AppConfigData对象序列化为JSON字符串
            val jsonString = Json.encodeToString(data)
            SharePreferenceUtil.putString(Constant.APP_CONFIG_JSON, jsonString)
            SharePreferenceUtil.putLong(Constant.APP_CONFIG_SAVE_TIME, System.currentTimeMillis())
        } catch (e: Exception) {
            // 保存失败，静默处理
        }
    }

    fun getLocalAppConfig(): AppConfigData? {
        // 优先返回内存中的配置
        if (appConfigData != null) {
            return appConfigData
        }
        
        // 如果内存中没有，尝试从SharedPreferences读取
        return getAppConfigFromSP()
    }

    /**
     * 从SharedPreferences读取应用配置
     */
    private fun getAppConfigFromSP(): AppConfigData? {
        try {
            val jsonString = SharePreferenceUtil.getString(Constant.APP_CONFIG_JSON)
            
            if (!jsonString.isNullOrEmpty()) {
                // 将JSON字符串反序列化为AppConfigData对象
                val configData = Json.decodeFromString<AppConfigData>(jsonString)
                
                // 将读取的配置也保存到内存中
                appConfigData = configData
                decryptAppConfig(configData)
                
                return configData
            }
            
            return null
        } catch (e: Exception) {
            return null
        }
    }

    fun getDecryptedAppConfig(): DecryptedAppConfig? {
        return decryptedAppConfig
    }

    /**
     * 根据配置项名称获取配置值
     */
    fun getConfigValue(name: String): String? {
        val data = decryptedAppConfig?.items?.find { it.name == name }?.data
        return when (data) {
            is JsonPrimitive -> data.contentOrNull
            null -> null
            else -> data.toString()
        }
    }

    /**
     * 根据配置项名称获取整数值
     */
    fun getConfigIntValue(name: String): Int? {
        return decryptedAppConfig?.items?.find { it.name == name }?.data?.jsonPrimitive?.intOrNull
    }

    /**
     * 根据配置项名称获取长整数值
     */
    fun getConfigLongValue(name: String): Long? {
        return decryptedAppConfig?.items?.find { it.name == name }?.data?.jsonPrimitive?.longOrNull
    }

    /**
     * 根据配置项名称获取双精度浮点数值
     */
    fun getConfigDoubleValue(name: String): Double? {
        return decryptedAppConfig?.items?.find { it.name == name }?.data?.jsonPrimitive?.doubleOrNull
    }

    /**
     * 根据配置项名称获取布尔值
     */
    fun getConfigBooleanValue(name: String): Boolean? {
        return decryptedAppConfig?.items?.find { it.name == name }?.data?.jsonPrimitive?.booleanOrNull
    }

    /**
     * 根据配置项名称获取浮点数值
     */
    fun getConfigFloatValue(name: String): Float? {
        return decryptedAppConfig?.items?.find { it.name == name }?.data?.jsonPrimitive?.floatOrNull
    }

    /**
     * 获取加密密钥
     */
    fun getEncryptKey(): String? {
        return getConfigValue("encrypt_key")
    }

    /**
     * 获取是否启用加密
     */
    fun isEncryptEnabled(): Boolean {
        return getConfigBooleanValue("is_encrypt") ?: false
    }

    /**
     * 获取风险控制配置
     */
    fun getRiskControlConfig(): RiskControlInfoConfig? {
        return decryptedAppConfig?.riskControlInfoConfig
    }

    /**
     * 获取所有配置项
     */
    fun getAllConfigItems(): List<ConfigItem>? {
        return decryptedAppConfig?.items
    }

    /**
     * 检查配置项是否存在
     */
    fun hasConfigItem(name: String): Boolean {
        return decryptedAppConfig?.items?.any { it.name == name } ?: false
    }

    /**
     * 获取所有配置项的名称
     */
    fun getAllConfigNames(): List<String>? {
        return decryptedAppConfig?.items?.mapNotNull { it.name }
    }

    /**
     * 获取配置保存时间
     */
    fun getConfigSaveTime(): Long {
        return SharePreferenceUtil.getLong(Constant.APP_CONFIG_SAVE_TIME, 0L)
    }

    /**
     * 检查配置是否过期（默认24小时）
     */
    fun isConfigExpired(expireTimeMillis: Long = 24 * 60 * 60 * 1000L): Boolean {
        val saveTime = getConfigSaveTime()
        if (saveTime == 0L) {
            return true // 没有保存时间，认为已过期
        }
        
        val currentTime = System.currentTimeMillis()
        val isExpired = (currentTime - saveTime) > expireTimeMillis
        
        return isExpired
    }

    /**
     * 清除本地保存的配置
     */
    fun clearLocalConfig() {
        try {
            appConfigData = null
            decryptedAppConfig = null
            
            SharePreferenceUtil.remove(Constant.APP_CONFIG_JSON)
            SharePreferenceUtil.remove(Constant.APP_CONFIG_SAVE_TIME)
        } catch (e: Exception) {
            // 清除失败，静默处理
        }
    }
}