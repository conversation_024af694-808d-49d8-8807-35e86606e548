package com.score.callmetest.manager

import com.score.callmetest.CallmeApplication
import com.score.callmetest.ui.rating.RatingDialog
import com.score.callmetest.util.ActivityUtils
import com.score.callmetest.util.SharePreferenceUtil

object RatingManager {
    private const val PAY_SUCCESS_TIMES = "pay_success_times"
    private const val HAS_SHOW_RATING_DIALOG = "has_show_rating_dialog"

    private var isRatingDialogShown = false

    fun addPaySuccessTimes() {
        SharePreferenceUtil.putInt(PAY_SUCCESS_TIMES, SharePreferenceUtil.getInt(PAY_SUCCESS_TIMES, 0) + 1)
    }

    fun checkShowRatingDialog(): <PERSON><PERSON><PERSON> {
        // 避免多次访问sp
        if (isRatingDialogShown) {
            return false
        }
        if(SharePreferenceUtil.getBoolean(HAS_SHOW_RATING_DIALOG, false)){
            isRatingDialogShown = true
            return false
        }
        val paySuccessTimes = SharePreferenceUtil.getInt(PAY_SUCCESS_TIMES, 0)
        if (paySuccessTimes >= 2) {
            // 显示评分对话框
            if (!SharePreferenceUtil.getBoolean(HAS_SHOW_RATING_DIALOG, false)) {
                ActivityUtils.getTopActivity()?.let {
                    RatingDialog(it).show()
                    SharePreferenceUtil.putBoolean(HAS_SHOW_RATING_DIALOG, true)
                    isRatingDialogShown = true
                }
                return true
            }
        }
        return false
    }
}