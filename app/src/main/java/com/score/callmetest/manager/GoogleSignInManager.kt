package com.score.callmetest.manager

import android.content.Context
import android.net.Uri
import android.os.Bundle
import android.util.Log
import androidx.credentials.CredentialManager
import androidx.credentials.GetCredentialRequest
import androidx.credentials.CustomCredential
import androidx.credentials.exceptions.GetCredentialException
import androidx.credentials.exceptions.NoCredentialException
import androidx.credentials.ClearCredentialStateRequest

import com.google.android.libraries.identity.googleid.GetGoogleIdOption
import com.google.android.libraries.identity.googleid.GetSignInWithGoogleOption
import com.google.android.libraries.identity.googleid.GoogleIdTokenCredential
import com.score.callmetest.BuildConfig
import com.score.callmetest.CallmeApplication
import com.score.callmetest.R
import com.score.callmetest.util.ThreadUtils
import timber.log.Timber

/**
 * Google 登录管理（基于 AndroidX Credentials + Google Identity Services）
 * - 使用 CredentialManager + GetGoogleIdOption 获取 Google ID Token
 * - 不持有 Activity/Fragment；仅使用 applicationContext
 * - 线程安全；主线程执行；异常保护与 Timber 日志
 */
object GoogleSignInManager {

    const val TYPE_NO_CREDENTIAL = "No credentials available"

    data class GoogleAccountInfo(
        val id: String?,
        val displayName: String?,
        val givenName: String?,
        val familyName: String?,
        val email: String?,
        val idToken: String?,
        val profilePictureUri: Uri?
    )

    /**
     * 统一的登录入口（Credentials/GIS）。
     * - filterByAuthorizedAccounts: 仅展示已授权过的账号
     * - autoSelect: 若仅有1个可用账号则自动选择
     */
    private fun signIn(
        context: Context = CallmeApplication.context,
        filterByAuthorizedAccounts: Boolean = false,
        autoSelect: Boolean = false,
        nonce: String? = null,
        onSuccess: (GoogleAccountInfo) -> Unit,
        onError: (String) -> Unit,
        onCancel:() -> Unit
    ) {
        val appCtx = context.applicationContext
        val clientId = BuildConfig.GOOGLE_CLIENT_ID
        if (clientId.isNullOrBlank()) {
            Timber.w("GoogleSignInManager: server client id is empty")
        }

        ThreadUtils.runOnMain {
            try {
                val credManager = CredentialManager.create(appCtx)
                val googleIdOption = buildGoogleIdOption(
                    clientId,
                    filterByAuthorizedAccounts,
                    autoSelect,
                    nonce
                )
                val request = GetCredentialRequest.Builder()
                    .addCredentialOption(googleIdOption)
                    .build()

                try {
                    val response = credManager.getCredential(appCtx, request)
                    val info = parseGoogleIdTokenCredential(response.credential)
                    if (info != null) {
                        Timber.d("GoogleSignIn success: email=%s", info.email)
                        onSuccess(info)
                    } else {
                        onError("Unsupported credential type or empty data")
                    }
                } catch (e: NoCredentialException) {
                    Timber.i("没有登录账号")
                    onError(TYPE_NO_CREDENTIAL)
                } catch (e: GetCredentialException) {
                    Timber.i(e, "GetCredential failed")
                    if(android.credentials.GetCredentialException.TYPE_USER_CANCELED==e.type){
                        //用户取消，没选择google账号
                        Timber.i(e, "用户取消")
                        onCancel()
                    }else {
                        onError(e.message ?: "GetCredential failed")
                    }
                } catch (t: Throwable) {
                    Timber.e(t, "GetCredential unexpected error")
                    onError(t.message ?: "GetCredential unexpected error")
                }
            } catch (t: Throwable) {
                Timber.e(t, "GoogleSignInManager.signIn error")
                onError(t.message ?: "signIn error")
            }
        }
    }

    /**
     * 登录
     * @param [context] 上下文
     * @param [isSilent] 静默登录
     * @param [onSuccess] 成功
     * @param [onError] 错误
     * @param [onCancel] 取消
     */
    fun signIn(context: Context = CallmeApplication.context,
                      isSilent: Boolean,
                      onSuccess: (GoogleAccountInfo) -> Unit,
                      onError: (String) -> Unit,
                      onCancel:() -> Unit) {
        if(isSilent){
            silentSignIn(
                context = context,
                onSuccess = onSuccess,
                onError = onError,
                onCancel = onCancel
            )
        }else{
            signIn(
                context = context,
                filterByAuthorizedAccounts = false,
                autoSelect = true,
                onSuccess = onSuccess,
                onError = onError,
                onCancel = onCancel
            )
        }
    }


    /**
     * 静默登录（仅尝试已授权账户并自动选择）
     */
    private fun silentSignIn(
        context: Context = CallmeApplication.context,
        onSuccess: (GoogleAccountInfo) -> Unit,
        onError: (String) -> Unit,
        onCancel:() -> Unit
    ) {
        signIn(
            context = context,
            filterByAuthorizedAccounts = true,
            autoSelect = true,
            onSuccess = onSuccess,
            onError = onError,
            onCancel = onCancel
        )
    }

    /**
     * 登出（Credentials/GIS 模式）：清除凭据状态，便于下次重新选择与授权。
     * 注意：是否真的弹出选择由 filterByAuthorizedAccounts/autoSelect 与系统策略共同决定。
     */
    fun signOut(
        context: Context = CallmeApplication.context,
        onComplete: (() -> Unit)? = null,
        onError: ((String) -> Unit)? = null
    ) {
        ThreadUtils.runOnMain {
            try {
                val credManager = CredentialManager.create(context.applicationContext)
                credManager.clearCredentialState(ClearCredentialStateRequest())
                Timber.d("GoogleSignInManager signOut: credential state cleared")
                onComplete?.invoke()
            } catch (t: Throwable) {
                Timber.w(t, "GoogleSignInManager signOut failed")
                onError?.invoke(t.message ?: "signOut failed")
            }
        }
    }


    /**
     * 构建 GoogleId 选项
     */
    private fun buildGoogleIdOption(
        serverClientId: String?,
        filterByAuthorizedAccounts: Boolean,
        autoSelect: Boolean,
        nonce: String?
    ): GetGoogleIdOption {
        GetSignInWithGoogleOption
        val builder = GetGoogleIdOption.Builder()
            .setFilterByAuthorizedAccounts(filterByAuthorizedAccounts)
            .setAutoSelectEnabled(autoSelect)
        if (!serverClientId.isNullOrBlank()) builder.setServerClientId(serverClientId)
        if (!nonce.isNullOrBlank()) builder.setNonce(nonce)
        return builder.build()
    }

    /**
     * 解析 Credential 为 GoogleAccountInfo
     */
    private fun parseGoogleIdTokenCredential(credential: androidx.credentials.Credential): GoogleAccountInfo? {
        return try {
            // 仅支持 GoogleIdTokenCredential
            if (credential is CustomCredential &&
                credential.type == GoogleIdTokenCredential.TYPE_GOOGLE_ID_TOKEN_CREDENTIAL
            ) {
                val google = GoogleIdTokenCredential.createFrom(credential.data)
                GoogleAccountInfo(
                    id = google.id,
                    displayName = google.displayName,
                    givenName = google.givenName,
                    familyName = google.familyName,
                    email = google.id, // 提示：某些场景仅返回 sub/idToken，email 需要在后端验证idToken后获取
                    idToken = google.idToken,
                    profilePictureUri = google.profilePictureUri
                )
            } else {
                null
            }
        } catch (t: Throwable) {
            Timber.w(t, "parseGoogleIdTokenCredential failed")
            null
        }

    }

}

