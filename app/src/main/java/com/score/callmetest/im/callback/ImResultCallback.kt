package com.score.callmetest.im.callback

import io.rong.imlib.RongIMClient

/**
 * 获取历史消息的回调，按照时间顺序从新到旧排列
 *
 * todo dsc 后续还需要把Message也抽离出来，不使用sdk中任何东西
 *
 */
abstract class ImResultCallback<T>: RongIMClient.ResultCallback<T>() {

    override fun onSuccess(t: T?) {
        success(t)
    }

    override fun onError(e: RongIMClient.ErrorCode?) {
        error(e?.code,e?.message)
    }

    abstract fun success(t: T?)

    abstract fun error(code: Int?,errorMsg: String?)
}