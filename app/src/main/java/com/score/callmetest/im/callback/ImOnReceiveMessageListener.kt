package com.score.callmetest.im.callback

import io.rong.imlib.model.Message
import io.rong.imlib.model.ReceivedProfile

/**
 * 消息接收监听器接口
 *
 * todo dsc 后续还需要把Message也抽离出来，不使用sdk中任何东西
 *
 */
interface ImOnReceiveMessageListener {
    /**
     * 接收到消息回调
     * @param message 消息
     */
    fun onReceiveMessage(message: Message){}

    /**
     * 收到信息-按需重写一个即可
     *
     * @param [message] 消息
     * @param [profile] 配置文件
     */
    fun onReceivedMessage(message: Message?, profile: ReceivedProfile?){}
}