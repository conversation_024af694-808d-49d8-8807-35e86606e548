package com.score.callmetest.im

/**
 * 连接状态枚举
 */
enum class ImConnectionStatus {

    CONNECTED,           // 连接成功
    CONNECTING,          // 连接中
    UNCONNECTED,        // 未连接状态，即应用没有调用过连接方法。
    NETWORK_UNAVAILABLE, // 网络不可用
    KICKED_OFFLINE,      // 被其他设备踢下线
    TOKEN_INCORRECT,     // Token不正确
    CONN_USER_BLOCKED,      // 用户被控制台封禁
    SIGN_OUT,      // 用户主动断开连接的状态
    SUSPEND,      // 连接暂时挂起（多是由于网络问题导致），SDK 会在合适时机进行自动重连
    TIMEOUT,      // 连接超时，SDK 将停止连接，用户需要做超时处理，再自行调用连接接口进行连接
    UNKNOWN              // 未知状态
}
