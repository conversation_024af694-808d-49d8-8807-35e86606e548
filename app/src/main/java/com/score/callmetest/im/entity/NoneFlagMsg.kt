package com.score.callmetest.im.entity

import android.os.Parcel
import android.os.Parcelable
import com.score.callmetest.im.ImProtocol
import io.rong.common.ParcelUtils
import io.rong.imlib.MessageTag
import io.rong.imlib.model.MessageContent
import timber.log.Timber

import android.text.TextUtils
import kotlinx.serialization.SerialName
import org.json.JSONException
import org.json.JSONObject
import java.nio.charset.StandardCharsets
import java.util.regex.Pattern

/**
 * 目前作用于优惠商品
 */
@MessageTag(value = ImProtocol.NONE_FLAG_MSG_PROC, flag = MessageTag.NONE)
class NoneFlagMsg : MessageContent {
    var content: String? = null
    var contentType: String? = null

    @SerialName("extra")
    var localExtra: String? = null

    companion object {
        private const val TAG = "NoneFlagMsg"
        private const val CONTENT = "content"
        private const val CONTENT_TYPE = "contentType"
        private const val EXTRA = "extra"

        @JvmStatic
        fun obtain(
            content: String,
            contentType: String,
            fromUserId: String,
            toUserId: String,
            extra: String
        ): NoneFlagMsg {
            val model = NoneFlagMsg()
            model.content = content
            model.contentType = contentType
            model.localExtra = extra
            return model
        }

        @JvmField
        val CREATOR: Parcelable.Creator<NoneFlagMsg> = object : Parcelable.Creator<NoneFlagMsg> {
            override fun createFromParcel(source: Parcel): NoneFlagMsg = NoneFlagMsg(source)
            override fun newArray(size: Int): Array<NoneFlagMsg?> = arrayOfNulls(size)
        }
    }

    constructor()

    constructor(data: ByteArray?) {
        if (data == null) {
            Timber.tag(TAG).e("data empty")
            return
        }
        val jsonStr = String(data, StandardCharsets.UTF_8)
        try {
            val jsonObj = JSONObject(jsonStr)
            if (jsonObj.has(CONTENT)) {
                content = jsonObj.optString(CONTENT)
            }
            if (jsonObj.has(CONTENT_TYPE)) {
                contentType = jsonObj.optString(CONTENT_TYPE)
            }
            if (jsonObj.has(EXTRA)) {
                localExtra = jsonObj.optString(EXTRA)
            }
        } catch (ignored: JSONException) {
        }
    }

    constructor(parcel: Parcel) {
        content = ParcelUtils.readFromParcel(parcel)
        contentType = ParcelUtils.readFromParcel(parcel)
        localExtra = ParcelUtils.readFromParcel(parcel)
    }

    constructor(content: String) {
        this.content = content
    }

    override fun encode(): ByteArray {
        val jsonObj = JSONObject()
        try {
            if (!TextUtils.isEmpty(content)) {
                jsonObj.put(CONTENT, getEmotion(content!!))
            }
            if (!TextUtils.isEmpty(contentType)) {
                jsonObj.put(CONTENT_TYPE, contentType)
            }
            if (!TextUtils.isEmpty(localExtra)) {
                jsonObj.put(EXTRA, localExtra)
            }
        } catch (ignored: JSONException) {
        }
        return jsonObj.toString().toByteArray(StandardCharsets.UTF_8)
    }

    override fun describeContents(): Int = 0

    override fun writeToParcel(dest: Parcel, flags: Int) {
        ParcelUtils.writeToParcel(dest, content)
        ParcelUtils.writeToParcel(dest, contentType)
        ParcelUtils.writeToParcel(dest, localExtra)
    }

    private fun getEmotion(content: String): String {
        val pattern = Pattern.compile("\\[/u([0-9A-Fa-f]+)\\]")
        val matcher = pattern.matcher(content)
        val sb = StringBuffer()

        while (matcher.find()) {
            val matchStr = matcher.group(1)
            val inthex = matchStr?.let { Integer.parseInt(it, 16) } ?: 0
            matcher.appendReplacement(sb, String(Character.toChars(inthex)))
        }

        matcher.appendTail(sb)
        return sb.toString()
    }

    override fun getSearchableWord(): List<String> {
        val words = ArrayList<String>()
        content?.let { words.add(it) }
        return words
    }
}