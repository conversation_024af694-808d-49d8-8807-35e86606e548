package com.score.callmetest.im

import android.app.Application
import android.net.Uri
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.google.gson.Gson
import com.score.callmetest.constants.RongCloudConstants
import com.score.callmetest.constants.SocketCommands
import com.score.callmetest.im.callback.ImConnectCallback
import com.score.callmetest.im.callback.ImGetMsgCallback
import com.score.callmetest.im.callback.ImOnReceiveMessageListener
import com.score.callmetest.im.callback.ImOperationCallback
import com.score.callmetest.im.callback.ImResultCallback
import com.score.callmetest.im.callback.ImSendMediaMsgCallback
import com.score.callmetest.im.callback.ImSendMsgCallback
import com.score.callmetest.im.download.HQVoiceMsgDownloadManager
import com.score.callmetest.im.entity.GiftContent
import com.score.callmetest.im.entity.HyperLinkMsg
import com.score.callmetest.im.entity.NoneFlagMsg
import com.score.callmetest.im.entity.SingleJsonMsg
import com.score.callmetest.network.GiftInfo
import io.rong.imlib.IRongCallback
import io.rong.imlib.IRongCoreListener
import io.rong.imlib.RongCoreClient
import io.rong.imlib.RongIMClient
import io.rong.imlib.listener.OnReceiveMessageWrapperListener
import io.rong.imlib.model.Conversation
import io.rong.imlib.model.HistoryMessageOption
import io.rong.imlib.model.InitOption
import io.rong.imlib.model.Message
import io.rong.imlib.model.MessageContent
import io.rong.imlib.model.ReceivedProfile
import io.rong.message.CommandMessage
import io.rong.message.HQVoiceMessage
import io.rong.message.ImageMessage
import io.rong.message.TextMessage
import io.rong.push.RongPushPlugin
import timber.log.Timber
import java.io.File


/**
 * 融云SDK管理类
 * 封装融云SDK的初始化、连接、消息收发、会话管理等功能
 *
 */
object RongCloudManager {

    // log-tag
    const val LOG_TAG = "RongCloud"

    // 连接状态
    private val _connectionStatus = MutableLiveData<ImConnectionStatus>()
    val connectionStatus: LiveData<ImConnectionStatus> = _connectionStatus

    // 接收消息监听器
    private val messageListeners = mutableListOf<ImOnReceiveMessageListener>()

    /**-
     * 初始化融云SDK
     * @param application 应用程序实例
     */
    fun init(application: Application,key: String? = null,rcAreaCode: String? = null) {
        Timber.tag(LOG_TAG).d("初始化融云SDK")

        try {

            // 多进程--默认也是多进程
            RongCoreClient.getInstance().enableSingleProcess(false)

            // 推送
            RongPushPlugin.init(application)

            // 如果重连时发现已有别的移动端设备在线，将不再重连，不影响已正常登录的移动端设备
            // 通过应用配置>IM 服务>免费基础功能>其他，开启允许 SDK 修改重连互踢策略
            RongIMClient.getInstance().setReconnectKickEnable(true)

            // 配置自定义message
            val myMessages = arrayListOf(
                HyperLinkMsg::class.java,
                SingleJsonMsg::class.java,
                NoneFlagMsg::class.java
            )
            RongIMClient.registerMessageType(myMessages)

            // 初始化SDK
            val areaValues = InitOption.AreaCode.entries.toTypedArray()
            var areaCode :InitOption.AreaCode? = null
            loot@for (code in areaValues) {
                if (code.name == rcAreaCode) {
                    areaCode = code
                    break@loot
                }
            }
            val initOption = InitOption.Builder()
                .setAreaCode(areaCode) // 若是海外数据中心--需要填入有效区域码
//                .enablePush(true) // 是否整体禁用推送。
                .build()
            RongCoreClient.init(application,key,initOption)

            // 配置消息接收监听器
            configureMessageReceiveListener()

            // 监听连接状态
            configureConnectionStatusListener()

            // 语音消息下载器注册
            HQVoiceMsgDownloadManager.init()

            Timber.tag(LOG_TAG).d("融云SDK初始化完成")
        } catch (e: Exception) {
            Timber.tag(LOG_TAG).e("融云SDK初始化失败: ${e.message}")
        }
    }


    /**
     * 注销当前登录，执行该方法后不会再收到 push 消息。
     */
    fun logout() {
        Timber.tag(LOG_TAG).d("logout...")
        // 清空监听器列表
        messageListeners.clear()
        RongIMClient.getInstance().logout()
        // logout中含disconnect
//        disconnect(false)
    }

    /**
     * 清理资源，防止内存泄漏
     * 在应用退出时调用
     */
    fun cleanup() {
        Timber.tag(LOG_TAG).d("清理融云SDK资源")

        try {
            // 清空监听器列表
            messageListeners.clear()

            // 断开连接
            disconnect(true)

            // 语音-网络监听取消
            HQVoiceMsgDownloadManager.clear()

            Timber.tag(LOG_TAG).d("融云SDK资源清理完成")
        } catch (e: Exception) {
            Timber.tag(LOG_TAG).e("融云SDK资源清理失败: ${e.message}")
        }
    }

    /**
     * 是否私人聊天
     *
     * @param [message] 消息
     * @return [Boolean]
     */
    fun isPrivateChat(message: Message): Boolean{
        return message.conversationType == Conversation.ConversationType.PRIVATE
    }

    /**
     * 添加黑名单
     */
    fun addToBlackList(targetId: String, callback: ImOperationCallback? = null) {
        RongIMClient.getInstance().addToBlacklist(targetId, object : ImOperationCallback() {
            override fun success() {
                callback?.success()
            }

            override fun error(code: Int?, errorMsg: String?) {
                callback?.error(code, errorMsg)
            }
        })
    }

    /**
     * 移除黑名单
     */
    fun removeFromBlackList(targetId: String, callback: ImOperationCallback? = null){
        RongIMClient.getInstance().removeFromBlacklist(targetId, object : ImOperationCallback() {
            override fun success() {
                callback?.success()
            }

            override fun error(code: Int?, errorMsg: String?) {
                callback?.error(code, errorMsg)
            }
        })
    }

    /**
     * 下载文件
     *
     *
     * 用来获取媒体原文件时调用。如果本地缓存中包含此文件，则从本地缓存中直接获取，否则将从服务器端下载。
     *
     * @param message 文件消息。
     * @param callback 下载文件的回调。该回调在主线程中执行，请避免在回调中执行耗时操作，防止 SDK 线程阻塞。
     */
    fun downloadMediaMessage(message: Message?, callback: IRongCallback.IDownloadMediaMessageCallback?) {
        // 下载事件分发
//        for (item in mMessageEventListeners) {
//            item.onDownloadMessage(DownloadEvent(DownloadEvent.START, message))
//        }
        RongIMClient.getInstance()
            .downloadMediaMessage(
                message,
                object : IRongCallback.IDownloadMediaMessageCallback {
                    override fun onSuccess(message: Message?) {
                        // 进度事件
//                        for (item in mMessageEventListeners) {
//                            item.onDownloadMessage(
//                                DownloadEvent(DownloadEvent.SUCCESS, message)
//                            )
//                        }
                        callback?.onSuccess(message)
                    }

                    override fun onProgress(message: Message?, progress: Int) {
                        // 进度事件
//                        for (item in mMessageEventListeners) {
//                            item.onDownloadMessage(
//                                DownloadEvent(
//                                    DownloadEvent.PROGRESS, message, progress
//                                )
//                            )
//                        }
                        callback?.onProgress(message, progress)
                    }

                    override fun onError(message: Message?, code: RongIMClient.ErrorCode?) {
//                        for (item in mMessageEventListeners) {
//                            item.onDownloadMessage(
//                                DownloadEvent(DownloadEvent.ERROR, message, code)
//                            )
//                        }
                        callback?.onError(message, code)
                    }

                    override fun onCanceled(message: Message?) {
//                        for (item in mMessageEventListeners) {
//                            item.onDownloadMessage(
//                                DownloadEvent(DownloadEvent.CANCEL, message)
//                            )
//                        }
                        callback?.onCanceled(message)
                    }
                })
    }

    /**
     * 暂停下载多媒体文件
     *
     * @param message 包含多媒体文件的消息，即[MessageContent]为 FileMessage, ImageMessage 等。
     * @param callback 暂停下载多媒体文件时的回调。该回调在主线程中执行，请避免在回调中执行耗时操作，防止 SDK 线程阻塞。
     */
    fun pauseDownloadMediaMessage(message: Message?, callback: RongIMClient.OperationCallback?) {
        RongIMClient.getInstance()
            .pauseDownloadMediaMessage(
                message,
                object : RongIMClient.OperationCallback() {
                    override fun onSuccess() {
                        callback?.onSuccess()
//                        for (item in mMessageEventListeners) {
//                            item.onDownloadMessage(
//                                DownloadEvent(DownloadEvent.PAUSE, message)
//                            )
//                        }
                    }

                    override fun onError(errorCode: RongIMClient.ErrorCode?) {
                        callback?.onError(errorCode)
                    }
                })
    }

    /**
     * 取消下载多媒体文件。
     *
     * @param message 包含多媒体文件的消息，即[MessageContent]为 FileMessage, ImageMessage 等。
     * @param callback 取消下载多媒体文件时的回调。该回调在主线程中执行，请避免在回调中执行耗时操作，防止 SDK 线程阻塞。
     */
    fun cancelDownloadMediaMessage(message: Message?, callback: RongIMClient.OperationCallback?) {
        RongIMClient.getInstance().cancelDownloadMediaMessage(message, callback)
    }

    /**
     * 配置消息接收监听器
     */
    private fun configureMessageReceiveListener() {
        // 设置消息接收监听器
        RongIMClient.addOnReceiveMessageListener(object: OnReceiveMessageWrapperListener() {
            override fun onReceivedMessage(message: Message?, profile: ReceivedProfile?) {
                // 子线程
                if(message != null && message.content is CommandMessage){
                    val command = message.content as CommandMessage
                    Timber.tag(LOG_TAG).d("receive new message----command--${command.name}--${command.data}")
                }else {
                Timber.tag(LOG_TAG).d("receive new message----${message?.messageId}--${message?.content?.toString()}")
                    }
                // 事件进行分发=暂时不需要拦截

                // 通知所有监听器
                messageListeners.forEach { listener ->
                    listener.onReceivedMessage(message,profile)
                    if(message != null) listener.onReceiveMessage(message)
                }
            }

            /**
             * 每次连接成功后，离线消息收取完毕时会触发以下回调方法。如果没有离线消息，连接成功后会立即触发。
             */
            override fun onOfflineMessageSyncCompleted() {
                super.onOfflineMessageSyncCompleted()
                Timber.tag(LOG_TAG).d("离线消息收取完毕...")
            }
        })
    }

    /**
     * 配置连接状态监听器
     */
    private fun configureConnectionStatusListener() {
        RongCoreClient.addConnectionStatusListener{ status ->
            Timber.tag(LOG_TAG).d("连接状态变化: $status")
            val connectionStatus = switchCustomStatus(status)
            _connectionStatus.postValue(connectionStatus)
        }
    }

    /**
     * 融云的连接状态-->自定义状态
     */
    private fun switchCustomStatus(status: IRongCoreListener.ConnectionStatusListener.ConnectionStatus): ImConnectionStatus{
        return when (status) {
            IRongCoreListener.ConnectionStatusListener.ConnectionStatus.CONNECTED -> ImConnectionStatus.CONNECTED
            IRongCoreListener.ConnectionStatusListener.ConnectionStatus.CONNECTING -> ImConnectionStatus.CONNECTING
            IRongCoreListener.ConnectionStatusListener.ConnectionStatus.UNCONNECTED -> ImConnectionStatus.UNCONNECTED
            IRongCoreListener.ConnectionStatusListener.ConnectionStatus.NETWORK_UNAVAILABLE -> ImConnectionStatus.NETWORK_UNAVAILABLE
            IRongCoreListener.ConnectionStatusListener.ConnectionStatus.KICKED_OFFLINE_BY_OTHER_CLIENT -> ImConnectionStatus.KICKED_OFFLINE
            IRongCoreListener.ConnectionStatusListener.ConnectionStatus.TOKEN_INCORRECT -> ImConnectionStatus.TOKEN_INCORRECT
            IRongCoreListener.ConnectionStatusListener.ConnectionStatus.CONN_USER_BLOCKED -> ImConnectionStatus.CONN_USER_BLOCKED
            IRongCoreListener.ConnectionStatusListener.ConnectionStatus.SIGN_OUT -> ImConnectionStatus.SIGN_OUT
            IRongCoreListener.ConnectionStatusListener.ConnectionStatus.SUSPEND -> ImConnectionStatus.SUSPEND
            IRongCoreListener.ConnectionStatusListener.ConnectionStatus.TIMEOUT -> ImConnectionStatus.TIMEOUT
            else -> ImConnectionStatus.UNKNOWN
        }
    }


    // <editor-folder desc="连接相关">

    /**
     * 连接融云服务器
     * @param token 用户令牌
     * @param callback 连接回调
     */
    fun connect(token: String, callback: ImConnectCallback? = null) {
        Timber.tag(LOG_TAG).d("连接融云服务器")

        RongIMClient.connect(token, object : RongIMClient.ConnectCallback() {
            override fun onSuccess(userId: String) {
                Timber.tag(LOG_TAG).d("连接成功: $userId")
                callback?.success()
            }

            override fun onError(errorCode: RongIMClient.ConnectionErrorCode) {
                Timber.tag(LOG_TAG).e("连接失败: ${errorCode.name}--${errorCode.value}")
                callback?.faild()
            }

            override fun onDatabaseOpened(code: RongIMClient.DatabaseOpenStatus) {
                Timber.tag(LOG_TAG).d("数据库打开状态: ${code.name}--${code.value}")
            }
        })
    }

    /**
     * 断开连接
     * @param isReceivePush 是否接收推送
     */
    fun disconnect(isReceivePush: Boolean) {
        Timber.tag(LOG_TAG).d("断开连接，是否接收推送: $isReceivePush")
        RongIMClient.getInstance().disconnect(isReceivePush)
    }

    /**
     * 获取当前连接状态
     * @return 连接状态
     */
    fun getCurrentConnectionStatus(): ImConnectionStatus {
        val status = RongCoreClient.getInstance().currentConnectionStatus
        return switchCustomStatus(status)
    }

    // </editor-folder>

    // <editor-folder desc="聊天详情message相关">

    /**
     * 发送文本消息
     * @param targetId 目标ID
     * @param content 文本内容
     * @param pushContent 推送内容
     * @param pushData 推送推送附加信息
     * @param callback 发送回调
     */
    fun sendTextMessage(
        targetId: String,
        content: String,
        pushContent: String? = null,
        pushData: String? = null,
        callback: ImSendMsgCallback? = null
    ) {
        Timber.tag(LOG_TAG).d("发送${targetId}文本消息: $content")
        // 组织发送实体
        val textMessage = TextMessage.obtain(content)
        // 目前只需要私聊
        val conversationType: Conversation.ConversationType = Conversation.ConversationType.PRIVATE
        val message = Message.obtain(targetId, conversationType, textMessage)
        // 发送
        RongIMClient.getInstance().sendMessage(message, pushContent,pushData,callback)
    }

    /**
     * 发送图片消息--目前固定每次发送原图-后续如有需要再改
     * @param targetId 目标ID
     * @param imageUri 图片URI
     * @param pushContent 推送内容
     * @param pushData 推送推送附加信息
     * @param callback 发送回调
     */
    fun sendImageMessage(
        targetId: String,
        imageUri: Uri,
        pushContent: String? = null,
        pushData: String? = null,
        callback: ImSendMediaMsgCallback? = null
    ) {
        Timber.tag(LOG_TAG).d("发送图片消息: $imageUri")

        // 目前只需要私聊
        val conversationType: Conversation.ConversationType = Conversation.ConversationType.PRIVATE
        // 构建消息体
        val imageMessage = ImageMessage.obtain(imageUri, true)
        val message = Message.obtain(targetId, conversationType, imageMessage)
        // 发送
        RongIMClient.getInstance().sendMediaMessage(message, pushContent, pushData, callback)
    }

    /**
     * 发送图片消息
     * @param targetId 目标ID
     * @param imageFile 图片文件
     * @param pushContent 推送内容
     * @param pushData 推送推送附加信息
     * @param callback 发送回调
     */
    fun sendImageMessage(
        targetId: String,
        imageFile: File,
        pushContent: String? = null,
        pushData: String? = null,
        callback: ImSendMediaMsgCallback? = null
    ) {
        val uri = Uri.fromFile(imageFile)
        sendImageMessage( targetId, uri, pushContent,pushData, callback)
    }

    /**
     * 发送语音消息
     * @param targetId 目标ID
     * @param voiceUri 音频URI
     * @param pushContent 推送内容
     * @param pushData 推送推送附加信息
     * @param callback 发送回调
     */
    fun sendVoiceMessage(
        targetId: String,
        voiceUri: Uri,
        voiceDuration: Int,
        pushContent: String? = null,
        pushData: String? = null,
        callback: ImSendMediaMsgCallback? = null
    ) {
        Timber.tag(LOG_TAG).d("发送语音消息: $voiceUri")

        // 目前只需要私聊
        val conversationType: Conversation.ConversationType = Conversation.ConversationType.PRIVATE
        // 构建消息体
        val voiceMessage = HQVoiceMessage.obtain(voiceUri,voiceDuration)
        val message = Message.obtain(targetId, conversationType, voiceMessage)
        // 发送
        RongIMClient.getInstance().sendMediaMessage(message, pushContent, pushData, callback)
    }

    /**
     * 发送gift消息
     * @param targetId 目标ID
     * @param voiceUri 音频URI
     * @param pushContent 推送内容
     * @param pushData 推送推送附加信息
     * @param callback 发送回调
     */
    fun sendGiftMessage(
        targetId: String,
        giftCode: String,
        fromUserName: String,
        toUserName: String,
        gift: GiftInfo,
        pushContent: String? = null,
        pushData: String? = null,
        callback: ImSendMsgCallback? = null
    ) {
        Timber.tag(LOG_TAG).d("发送gift消息: $fromUserName->$toUserName:$giftCode")

        // 目前只需要私聊
        val conversationType: Conversation.ConversationType = Conversation.ConversationType.PRIVATE
        // 构建消息体
        // {"giftCode":"","fromUserName":"","toUserName":""}
        val giftContent = GiftContent(giftCode,fromUserName,toUserName)
        val gson = Gson()
        val giftMessage = SingleJsonMsg.obtain(gson.toJson(giftContent), contentType = "gift", extra = gson.toJson(gift))
        val message = Message.obtain(targetId, conversationType, giftMessage)
        // 发送
        RongIMClient.getInstance().sendMessage(message, pushContent, pushData, callback)
    }

    /**
     * 发送 Command 消息
     * 用于视频通话中的聊天消息双通道发送
     *
     * @param targetId 目标用户ID
     * @param fromUserId 发送者ID
     * @param content 消息内容
     * @param timestamp 时间戳
     * @param pushContent 推送内容
     * @param pushData 推送数据
     * @param callback 发送回调
     */
    fun sendCommandMessage(
        targetId: String,
        fromUserId: String,
        content: String,
        timestamp: Long = System.currentTimeMillis(),
        pushContent: String? = null,
        pushData: String? = null,
        callback: ImSendMsgCallback? = null
    ) {
        Timber.tag(LOG_TAG).d("发送Command消息: $fromUserId->$targetId: $content")

        try {
            // 构建消息数据对象
            val msgObject = org.json.JSONObject().apply {
                put("fromUserId", fromUserId)
                put("toUserId", targetId)
                put("content", content)
                put("timestamp", timestamp)
                put("command", SocketCommands.Message.ON_CHAT)
            }

            // 创建 CommandMessage
            val commandMessage = CommandMessage.obtain(
                RongCloudConstants.MESSAGE_EVENT,
                msgObject.toString()
            )

            // 创建消息
            val conversationType = Conversation.ConversationType.PRIVATE
            val message = Message.obtain(targetId, conversationType, commandMessage)

            // 发送消息
            RongIMClient.getInstance().sendMessage(message, pushContent, pushData, callback)

        } catch (e: Exception) {
            Timber.tag(LOG_TAG).e("发送Command消息失败: ${e.message}")
            callback?.failed(-1, "发送失败: ${e.message}")
        }
    }

    /**
     * 获取最新消息
     * @param targetId 目标ID
     * @param count 获取数量
     * @param callback 获取最新消息的回调，按照时间顺序从新到旧排列，如果会话中的消息数量小于参数 count 的值，会将该会话中的所有消息返回。
     */
    fun getLatestMessages(
        targetId: String,
        count: Int,
        callback: ImResultCallback<List<Message>>
    ) {
        Timber.tag(LOG_TAG).d("获取最新消息: $targetId")
        // 目前只需要私聊
        val conversationType: Conversation.ConversationType = Conversation.ConversationType.PRIVATE
        RongIMClient.getInstance().getLatestMessages(conversationType, targetId, count, callback)
    }

    /**
     * 分页查询并获取本地历史消息(消息按发送时间从新到旧排列)
     * @param targetId 目标ID
     * @param oldestMessageId 最后一条消息的 ID。如需查询本地数据库中最新的消息，设置为 -1
     * @param count 获取数量
     * @param callback 回调
     */
    fun getLocalHistoryMessages(
        targetId: String,
        oldestMessageId: Int,
        count: Int,
        callback: ImResultCallback<List<Message>>
    ) {
        Timber.tag(LOG_TAG).d("获取历史消息: $targetId")
        // 目前只需要私聊
        val conversationType: Conversation.ConversationType = Conversation.ConversationType.PRIVATE
        // 查询
        RongIMClient.getInstance().getHistoryMessages(conversationType, targetId, oldestMessageId, count, callback)
    }

    /**
     * 按融云msgId获取消息
     * @param [messageId] 消息id
     * @param [callback] 回调
     */
    fun getMessageById(messageId: Int, callback: ImResultCallback<Message>) {
        Timber.tag(LOG_TAG).d("获取消息: $messageId")
        return RongIMClient.getInstance().getMessage(messageId,callback)
    }

    /**
     * 分页查询并获取远端历史消息
     * 查询结果与本地数据库对比，排除重复的消息后，返回消息对象列表。返回的消息列表中的消息按发送时间从新到旧排列
     * 建议先使用 getHistoryMessages，在本地数据库消息全部获取完之后，再获取远端历史消息。否则可能会获取不到指定的部分或全部消息。
     * @param targetId 目标ID
     * @param dateTime 时间戳，获取发送时间早于 dateTime 的历史消息。传 0 表示获取最新 count 条消息。
     * @param count 要获取的消息数量。如果 SDK < 5.4.1，范围为 [2-20]；如果 SDK ≧ 5.4.1，范围为 [2-100]。
     * @param callback 回调
     */
    fun getRemoteHistoryMessages(
        targetId: String,
        dateTime: Long,
        count: Int,
        callback: ImResultCallback<List<Message>>
    ) {
        Timber.tag(LOG_TAG).d("获取历史消息: $targetId")
        // 目前只需要私聊
        val conversationType: Conversation.ConversationType = Conversation.ConversationType.PRIVATE
        // 查询
        RongIMClient.getInstance().getRemoteHistoryMessages(conversationType, targetId, dateTime, count, callback)
    }

    /**
     * 分页查询并获取远端历史消息
     * 此方法先从本地获取历史消息，本地有缺失的情况下会从服务端同步缺失的部分；从服务端同步失败的时候会返回非 0 的 errorCode，同时把本地能取到的消息回调上去。 必须开通历史消息云存储功能。。
     * @param targetId 目标ID
     * @param dateTime 时间戳，获取发送时间早于 dateTime 的历史消息。传 0 表示获取最新 count 条消息。
     * @param count 要获取的消息数量。如果 SDK < 5.4.1，范围为 [2-20]；如果 SDK ≧ 5.4.1，范围为 [2-100]。
     * @param isAscend 要拉取顺序。DESCEND：降序 .按消息发送时间递减的顺序，获取发送时间早于 dataTime 的消息，返回的列表中的消息按发送时间从新到旧排列。
     * ASCEND： 升序 . 按消息发送时间递增的顺序，获取发送时间晚于 dataTime 的消息，返回的列表中的消息按发送时间从旧到新排列。
     * @param callback 回调
     */
    fun getHistoryMessages(
        targetId: String,
        dateTime: Long,
        count: Int,
        isAscend: Boolean = false,
        callback: ImGetMsgCallback
    ) {
        Timber.tag(LOG_TAG).d("获取历史消息: $targetId")
        // 目前只需要私聊
        val conversationType: Conversation.ConversationType = Conversation.ConversationType.PRIVATE
        // 构建historyMessageOption
        val option = HistoryMessageOption().apply {
            //时间戳，用于控制分页查询消息的边界。默认值为 0。
            this.dataTime = dateTime
            // 要获取的消息数量。如果 SDK < 5.4.1，范围为 [2-20]；如果 SDK ≧ 5.4.1，范围为 [2-100]；默认值为 5
            this.count = count
            // 拉取顺序。DESCEND：降序 .按消息发送时间递减的顺序，获取发送时间早于 dataTime 的消息，返回的列表中的消息按发送时间从新到旧排列。
            // ASCEND： 升序 . 按消息发送时间递增的顺序，获取发送时间晚于 dataTime 的消息，返回的列表中的消息按发送时间从旧到新排列。
            this.setOrder(if(isAscend) HistoryMessageOption.PullOrder.ASCEND else HistoryMessageOption.PullOrder.DESCEND)
        }
        // 查询
        RongCoreClient.getInstance().getMessages(conversationType, targetId, option, callback)
    }

    /**
     * 删除指定id消息
     * @param ids IDs
     * @param callback 回调
     */
    fun deleteMessages(
        vararg ids: Int,
        callback: ImResultCallback<Boolean>
    ) {
        Timber.tag(LOG_TAG).d("清空会话消息: $ids")
        // 目前只需要私聊
        val conversationType: Conversation.ConversationType = Conversation.ConversationType.PRIVATE
        RongIMClient.getInstance().deleteMessages(ids,callback)
    }

    /**
     * 清空本地会话消息
     * @param targetId 目标ID
     * @param callback 回调
     */
    fun cleanLocalMessages(
        targetId: String,
        callback: ImResultCallback<Boolean>
    ) {
        Timber.tag(LOG_TAG).d("清空会话消息: $targetId")
        // 目前只需要私聊
        val conversationType: Conversation.ConversationType = Conversation.ConversationType.PRIVATE
        RongIMClient.getInstance().deleteMessages(conversationType, targetId, callback)
    }

    /**
     * 清除服务器端历史消息和本地消息，如果清除服务器端消息必须先开通单群聊历史消息云存储服务。（单个会话）
     * @param targetId 目标ID
     * @param recordTime 时间戳。默认删除小于等于 recordTime 的消息。如果传 0，则删除所有消息。
     * @param cleanRemote 是否删除服务器端消息。true：同时删除服务端对应消息。false：不删除服务端对应消息。
     * @param callback 回调
     */
    fun cleanHistoryMessages(
        targetId: String,
        recordTime: Long,
        cleanRemote: Boolean = true,
        callback: ImOperationCallback
    ) {
        Timber.tag(LOG_TAG).d("清空会话消息: $targetId")
        // 目前只需要私聊
        val conversationType: Conversation.ConversationType = Conversation.ConversationType.PRIVATE
        RongIMClient.getInstance().cleanHistoryMessages(conversationType, targetId, recordTime,cleanRemote,callback)
    }

    // </editor-folder>

    // <editor-folder desc="对外接口">

    /**
     * 添加消息接收监听器
     * @param listener 监听器
     */
    fun addOnReceiveMessageListener(listener: ImOnReceiveMessageListener) {
        if (!messageListeners.contains(listener)) {
            messageListeners.add(listener)
        }
    }

    /**
     * 移除消息接收监听器
     * @param listener 监听器
     */
    fun removeOnReceiveMessageListener(listener: ImOnReceiveMessageListener) {
        messageListeners.remove(listener)
    }

    // </editor-folder>

}