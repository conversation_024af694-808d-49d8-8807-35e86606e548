package com.score.callmetest.im.callback

import com.score.callmetest.im.RongCloudManager
import io.rong.imlib.IRongCallback
import io.rong.imlib.RongIMClient
import io.rong.imlib.model.Message
import timber.log.Timber

/**
 * 发送消息回调
 */
abstract class ImSendMsgCallback: IRongCallback.ISendMessageCallback {

    /**
     * 消息已存储数据库。
     */
    override fun onAttached(message: Message?) {
        Timber.tag(RongCloudManager.LOG_TAG).d("消息已存储数据库")
    }

    /**
     * 发送成功
     */
    override fun onSuccess(message: Message?) {
        Timber.tag(RongCloudManager.LOG_TAG).d("消息发送成功")
        success(message)
    }

    /**
     * 发送失败
     */
    override fun onError(message: Message?, errorCode: RongIMClient.ErrorCode?) {
        Timber.tag(RongCloudManager.LOG_TAG).d("消息发送失败-${errorCode?.name}--${errorCode?.value}")
        failed(errorCode?.code,errorCode?.message)
    }

    /**
     * 发送成功
     */
    abstract fun success(message: Message?)

    /**
     * 发送失败
     */
    abstract fun failed(code: Int?, errorMsg: String?)

}