package com.score.callmetest.im.callback

import com.score.callmetest.im.RongCloudManager
import io.rong.imlib.IRongCallback
import io.rong.imlib.RongIMClient
import io.rong.imlib.model.Message
import timber.log.Timber

/**
 * 媒体消息发送回调
 */
abstract class ImSendMediaMsgCallback: IRongCallback.ISendMediaMessageCallback {


    /**
     * cancel
     */
    override fun onCanceled(message: Message?) {
        Timber.tag(RongCloudManager.LOG_TAG).d("消息发送取消")
        cancel(message)
    }

    /**
     * 消息已存储数据库。
     */
    override fun onAttached(message: Message?) {
        Timber.tag(RongCloudManager.LOG_TAG).d("消息已存储数据库")
    }

    /**
     * 发送成功
     */
    override fun onSuccess(message: Message?) {
        Timber.tag(RongCloudManager.LOG_TAG).d("消息发送成功")
        success(message)
    }

    /**
     * 发送失败
     */
    override fun onError(message: Message?, errorCode: RongIMClient.ErrorCode?) {
        Timber.tag(RongCloudManager.LOG_TAG).d("消息发送失败-${errorCode?.name}--${errorCode?.value}")
        faild(errorCode?.code,errorCode?.message)
    }

    /**
     * 发送进度
     */
    override fun onProgress(message: Message?, progress: Int) {
        Timber.tag(RongCloudManager.LOG_TAG).d("消息${message?.messageId}发送进度-$progress")
        progress(message,progress)
    }

    /**
     * 发送成功
     */
    abstract fun success(message: Message?)

    /**
     * 发送失败
     */
    abstract fun faild(code: Int?, errorMsg: String?)

    /**
     * 发送进度（按需重写）
     */
    fun progress(message: Message?, progress: Int){}

    /**
     * 取消发送（按需重写）
     */
    fun cancel(message: Message?){}

}