package com.score.callmetest.im.callback

import io.rong.imlib.IRongCoreCallback
import io.rong.imlib.IRongCoreEnum
import io.rong.imlib.model.Message

/**
 * 获取历史消息的回调。
 */
abstract class ImGetMsgCallback: IRongCoreCallback.IGetMessageCallback {
    override fun onComplete(
        messageList: List<Message?>?,
        errorCode: IRongCoreEnum.CoreErrorCode?
    ) {
        if(errorCode == IRongCoreEnum.CoreErrorCode.SUCCESS ){
            success(messageList)
            return
        }
        error(errorCode?.code,errorCode?.message)
    }

    abstract fun success(messageList: List<Message?>?)

    abstract fun error(code: Int?,errorMsg: String?)
}