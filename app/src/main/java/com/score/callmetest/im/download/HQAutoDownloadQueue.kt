package com.score.callmetest.im.download

import io.rong.imlib.model.Message
import java.util.concurrent.ConcurrentLinkedQueue

/**
 * 语音自动下载队列
 */
class HQAutoDownloadQueue {
    companion object {
        // 最大下载队列
        private const val MAX_QUEUE_COUNT = 100
    }

    private val highPriority = ConcurrentLinkedQueue<HQAutoDownloadEntry?>()
    private val normalPriority = ConcurrentLinkedQueue<HQAutoDownloadEntry?>()
    val autoDownloadEntryHashMap: HashMap<String?, HQAutoDownloadEntry?> = HashMap()


    /**
     * 通过DownloadPriority分配不同下载队列入队（如果队列有，则更新）
     *
     * @param [autoDownloadEntry] 自动下载入口
     */
    fun enqueue(autoDownloadEntry: HQAutoDownloadEntry) {
        val message: Message = autoDownloadEntry.message

        if (autoDownloadEntry.priority == DownloadPriority.NORMAL) {
            normalPriority.add(autoDownloadEntry)
        } else if (autoDownloadEntry.priority == DownloadPriority.HIGH) {
            highPriority.add(autoDownloadEntry)
        }
        if (!autoDownloadEntryHashMap.containsKey(message.uId)) {
            autoDownloadEntryHashMap.put(message.uId, autoDownloadEntry)
        }

        val doubleQueueSize = normalPriority.size + highPriority.size
        // 超过最大--移除队列队头的
        if (doubleQueueSize > MAX_QUEUE_COUNT) {
            if (!normalPriority.isEmpty()) {
                autoDownloadEntryHashMap.remove(normalPriority.poll()!!.message.uId)
            } else {
                val highItem = highPriority.poll()
                if (highItem != null) {
                    autoDownloadEntryHashMap.remove(highItem.message.uId)
                }
            }
        }
    }

    /**
     * message是否已经在下载队列中
     *
     * @param [message] 消息
     * @return [Boolean]
     */
    fun ifMsgInHashMap(message: Message?): Boolean {
        return autoDownloadEntryHashMap.containsKey(message?.uId)
    }

    /**
     * 出列
     *
     * @return [Message?]
     */
    fun dequeue(): Message? {
        if (!highPriority.isEmpty()) {
            return highPriority.poll()?.message
        }

        if (!normalPriority.isEmpty()) {
            return normalPriority.poll()?.message
        }
        return null
    }

    val isEmpty: Boolean
        get() = highPriority.isEmpty() && normalPriority.isEmpty()

}