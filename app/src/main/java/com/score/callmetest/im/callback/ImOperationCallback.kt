package com.score.callmetest.im.callback

import io.rong.imlib.RongIMClient

/**
 * 删除消息回调(cleanHistoryMessages)
 */
abstract class ImOperationCallback: RongIMClient.OperationCallback() {

    override fun onSuccess() = success()
    override fun onError(coreErrorCode: RongIMClient.ErrorCode?) = error(coreErrorCode?.code,coreErrorCode?.message)

    abstract fun success()

    abstract fun error(code: Int?,errorMsg: String?)
}