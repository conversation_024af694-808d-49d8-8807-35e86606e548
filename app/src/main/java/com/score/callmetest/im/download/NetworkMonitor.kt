package com.score.callmetest.im.download

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.net.ConnectivityManager
import android.net.Network
import android.net.NetworkCapabilities
import android.net.NetworkRequest
import android.os.Build
import androidx.lifecycle.LiveData
import androidx.lifecycle.Observer
import androidx.lifecycle.asLiveData
import com.score.callmetest.CallmeApplication
import io.rong.common.SystemUtils
import io.rong.imlib.common.NetUtils
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.launch


/**
 * 监听网络变化
 */
class NetworkMonitor: Observer<Boolean> {

    private val context: Context = CallmeApplication.context

    // 使用 NetworkCallback
    private val connectivityManager: ConnectivityManager?
    private val networkStateFlow: Flow<Boolean>?

    // 转为 LiveData 供 ViewModel 使用
    val networkStateLiveData: LiveData<Boolean>?


    constructor(){
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.N) {
            // 注册旧版广播接收器
            SystemUtils.registerReceiverCompat(
                context, legacyReceiver, IntentFilter(ConnectivityManager.CONNECTIVITY_ACTION)
            )
        }

        connectivityManager = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            context.getSystemService(ConnectivityManager::class.java)
        }else null
        // 使用 Flow 封装网络状态
        networkStateFlow = if(Build.VERSION.SDK_INT >= Build.VERSION_CODES.N){
            callbackFlow {
                val callback = object : ConnectivityManager.NetworkCallback() {
                    override fun onAvailable(network: Network) {
                        launch { send(true) }  // 网络可用
                    }

                    override fun onLost(network: Network) {
                        launch { send(false) }  // 网络丢失
                    }
                }

                val request = NetworkRequest.Builder()
                    .addCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
                    .build()

                connectivityManager?.registerNetworkCallback(request, callback)

                // 协程取消时注销回调
                awaitClose { connectivityManager?.unregisterNetworkCallback(callback) }
            }
        }else null
        // 转为 LiveData 供 ViewModel 使用
        networkStateLiveData = if(Build.VERSION.SDK_INT >= Build.VERSION_CODES.N){
            networkStateFlow?.asLiveData()
        }else null
        // 接收网络信息
        networkStateLiveData?.observeForever(this)
    }

    // 旧版网络信息广播接收器（仅用于 Android N 以下）
    private val legacyReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            // 处理网络变化
            val networkAvailable = NetUtils.isNetWorkAvailable(context)
            if ((ConnectivityManager.CONNECTIVITY_ACTION) == intent.action && networkAvailable) {
                HQVoiceMsgDownloadManager.resumeDownloadService()
            } else {
                HQVoiceMsgDownloadManager.pauseDownloadService()
            }
        }
    }

    /*init {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.N) {
            // 注册旧版广播接收器
            SystemUtils.registerReceiverCompat(
                context, legacyReceiver, IntentFilter(ConnectivityManager.CONNECTIVITY_ACTION)
            )
        } else {
            // 使用 NetworkCallback
//            val connectivityManager = context.getSystemService(ConnectivityManager::class.java)
            // 使用 Flow 封装网络状态
            *//*val networkStateFlow: Flow<Boolean> = callbackFlow {
                val callback = object : ConnectivityManager.NetworkCallback() {
                    override fun onAvailable(network: Network) {
                        launch { send(true) }  // 网络可用
                    }

                    override fun onLost(network: Network) {
                        launch { send(false) }  // 网络丢失
                    }
                }

                val request = NetworkRequest.Builder()
                    .addCapability(android.net.NetworkCapabilities.NET_CAPABILITY_INTERNET)
                    .build()

                connectivityManager.registerNetworkCallback(request, callback)

                // 协程取消时注销回调
                awaitClose { connectivityManager.unregisterNetworkCallback(callback) }
            }*//*

            // 转为 LiveData 供 ViewModel 使用
//            val networkStateLiveData: LiveData<Boolean> = networkStateFlow.asLiveData()
        }
    }*/

    override fun onChanged(isConnected: Boolean) {
        if(isConnected){
            HQVoiceMsgDownloadManager.resumeDownloadService()
        }else {
            HQVoiceMsgDownloadManager.pauseDownloadService()
        }
    }

    //
    fun clear() {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.N) {
            CallmeApplication.context.unregisterReceiver(legacyReceiver)
        } else {
            // 注销 NetworkCallback
            networkStateLiveData?.removeObserver(this)
        }
    }
}