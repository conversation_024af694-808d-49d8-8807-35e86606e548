package com.score.callmetest.im.entity

import android.os.Parcel
import android.os.Parcelable
import android.text.TextUtils
import com.score.callmetest.im.ImProtocol
import io.rong.common.ParcelUtils
import io.rong.imlib.MessageTag
import io.rong.imlib.model.MessageContent
import kotlinx.serialization.SerialName
import org.json.JSONException
import org.json.JSONObject
import timber.log.Timber
import java.nio.charset.StandardCharsets
import java.util.regex.Pattern

/**
 * 超链接
 *
 * <AUTHOR>
 * @date 2025/07/22
 * @constructor 创建[HyperLinkMsg]
 */
@MessageTag(value = ImProtocol.HYPER_LINK_MSG_PROC, flag = MessageTag.ISCOUNTED)
class HyperLinkMsg : MessageContent {

    companion object {
        private const val TAG = "HyperLinkMsg"
        private const val CONTENT = "content"
        private const val CONTENT_TYPE = "contentType"
        private const val EXTRA = "extra"

        @JvmStatic
        fun obtain(content: String, contentType: String, fromUserId: String, toUserId: String, extra: String): HyperLinkMsg {
            val model = HyperLinkMsg()
            model.content = content
            model.contentType = contentType
            model.localExtra = extra
            return model
        }

        @JvmField
        val CREATOR: Parcelable.Creator<HyperLinkMsg> = object : Parcelable.Creator<HyperLinkMsg> {
            override fun createFromParcel(source: Parcel): HyperLinkMsg = HyperLinkMsg(source)
            override fun newArray(size: Int): Array<HyperLinkMsg?> = arrayOfNulls(size)
        }
    }

    /**
     * Click here to recharge,get 10% more coins.
     */
    var content: String? = null

    /**
     * recharge_link
     */
    var contentType: String? = null

    /**
     * {"invitationId":"d802bad3-1f54-404d-9a98-f92b26475b81"}
     */
    @SerialName("extra")
    var localExtra: String? = null

    constructor()

    constructor(data: ByteArray?) {
        if (data == null) {
            Timber.tag(TAG).e("data empty")
            return
        }

        val jsonStr = String(data, StandardCharsets.UTF_8)
        try {
            val jsonObj = JSONObject(jsonStr)
            if (jsonObj.has(CONTENT)) content = jsonObj.optString(CONTENT)
            if (jsonObj.has(CONTENT_TYPE)) contentType = jsonObj.optString(CONTENT_TYPE)
            if (jsonObj.has(EXTRA)) localExtra = jsonObj.optString(EXTRA)
        } catch (ignored: JSONException) {
        }
    }

    constructor(parcel: Parcel) {
        content = ParcelUtils.readFromParcel(parcel)
        contentType = ParcelUtils.readFromParcel(parcel)
        localExtra = ParcelUtils.readFromParcel(parcel)
    }

    constructor(content: String) {
        this.content = content
    }

    override fun encode(): ByteArray {
        val jsonObj = JSONObject()
        try {
            if (!TextUtils.isEmpty(content))
                jsonObj.put(CONTENT, getEmotion(content!!))
            if (!TextUtils.isEmpty(contentType))
                jsonObj.put(CONTENT_TYPE, contentType)
            if (!TextUtils.isEmpty(localExtra))
                jsonObj.put(EXTRA, localExtra)
        } catch (ignored: JSONException) {
        }
        return jsonObj.toString().toByteArray(StandardCharsets.UTF_8)
    }

    override fun describeContents(): Int = 0

    override fun writeToParcel(dest: Parcel, flags: Int) {
        ParcelUtils.writeToParcel(dest, content)
        ParcelUtils.writeToParcel(dest, contentType)
        ParcelUtils.writeToParcel(dest, localExtra)
    }

    private fun getEmotion(content: String): String {
        val pattern = Pattern.compile("\\[/u([0-9A-Fa-f]+)\\]")
        val matcher = pattern.matcher(content)
        val sb = StringBuffer()

        while (matcher.find()) {
            val matchStr = matcher.group(1)
            val inthex = matchStr?.let { Integer.parseInt(it, 16) } ?: 0
            matcher.appendReplacement(sb, String(Character.toChars(inthex)))
        }

        matcher.appendTail(sb)
        return sb.toString()
    }

    override fun getSearchableWord(): List<String> {
        val words = ArrayList<String>()
        content?.let { words.add(it) }
        return words
    }
}