package com.score.callmetest.im.entity


import android.os.Parcel
import android.os.Parcelable
import com.score.callmetest.im.ImProtocol
import io.rong.common.ParcelUtils
import io.rong.imlib.MessageTag
import io.rong.imlib.model.MessageContent
import timber.log.Timber

import android.text.TextUtils
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import org.json.JSONException
import org.json.JSONObject
import java.nio.charset.StandardCharsets
import java.util.regex.Pattern

/**
 * 礼物消息
 *
 * <AUTHOR>
 * @date 2025/07/22
 * @constructor 创建[SingleJsonMsg]
 */
@MessageTag(value = ImProtocol.SINGLE_JSON_MSG_PROC, flag = MessageTag.ISCOUNTED)
class SingleJsonMsg : MessageContent{
    companion object {
        private const val TAG = "SingleJsonMsg"
        private const val CONTENT = "content"
        private const val CONTENT_TYPE = "contentType"
        private const val EXTRA = "extra"

        @JvmStatic
        fun obtain(
            content: String,
            contentType: String,
//            fromUserId: String,
//            toUserId: String,
            extra: String
        ): SingleJsonMsg {
            val model = SingleJsonMsg()
            model.content = content
            model.contentType = contentType
            model.localExtra = extra
            return model
        }

        @JvmField
        val CREATOR: Parcelable.Creator<SingleJsonMsg> = object : Parcelable.Creator<SingleJsonMsg> {
            override fun createFromParcel(source: Parcel): SingleJsonMsg = SingleJsonMsg(source)
            override fun newArray(size: Int): Array<SingleJsonMsg?> = arrayOfNulls(size)
        }
    }

    var content: String? = null
    var contentType: String? = null
    @SerialName("extra")
    var localExtra: String? = null

    constructor()

    constructor(data: ByteArray?) {
        if (data == null) {
            Timber.tag(TAG).e("data empty")
            return
        }
        val jsonStr = String(data, StandardCharsets.UTF_8)
        try {
            val jsonObj = JSONObject(jsonStr)
            if (jsonObj.has(CONTENT)) {
                content = jsonObj.optString(CONTENT)
            }
            if (jsonObj.has(CONTENT_TYPE)) {
                contentType = jsonObj.optString(CONTENT_TYPE)
            }
            if (jsonObj.has(EXTRA)) {
                localExtra = jsonObj.optString(EXTRA)
            }
        } catch (ignored: JSONException) {
        }
    }

    constructor(parcel: Parcel) {
        content = ParcelUtils.readFromParcel(parcel)
        contentType = ParcelUtils.readFromParcel(parcel)
        localExtra = ParcelUtils.readFromParcel(parcel)
    }

    constructor(content: String) {
        this.content = content
    }

    override fun encode(): ByteArray {
        val jsonObj = JSONObject()
        try {
            if (!TextUtils.isEmpty(content)) {
                jsonObj.put(CONTENT, content)
            }
            if (!TextUtils.isEmpty(contentType)) {
                jsonObj.put(CONTENT_TYPE, contentType)
            }
            if (!TextUtils.isEmpty(localExtra)) {
                jsonObj.put(EXTRA, localExtra)
            }
        } catch (ignored: JSONException) {
        }
        return jsonObj.toString().toByteArray(StandardCharsets.UTF_8)
    }

    override fun describeContents(): Int = 0

    override fun writeToParcel(dest: Parcel, flags: Int) {
        ParcelUtils.writeToParcel(dest, content)
        ParcelUtils.writeToParcel(dest, contentType)
        ParcelUtils.writeToParcel(dest, localExtra)
    }

    private fun getEmotion(content: String): String {
        val pattern = Pattern.compile("\\[/u([0-9A-Fa-f]+)\\]")
        val matcher = pattern.matcher(content)
        val sb = StringBuffer()

        while (matcher.find()) {
            val matchStr = matcher.group(1)
            val inthex = matchStr?.let { Integer.parseInt(it, 16) } ?: 0
            matcher.appendReplacement(sb, String(Character.toChars(inthex)))
        }

        matcher.appendTail(sb)
        return sb.toString()
    }

    override fun getSearchableWord(): List<String> {
        val words = ArrayList<String>()
        content?.let { words.add(it) }
        return words
    }
}

/**
 * giftMsg的content构建json使用
 *
 * {"giftCode":"","fromUserName":"","toUserName":""}
 */
@Serializable
data class GiftContent(
    val giftCode: String = "",
    val fromUserName: String = "",
    val toUserName: String = ""
)