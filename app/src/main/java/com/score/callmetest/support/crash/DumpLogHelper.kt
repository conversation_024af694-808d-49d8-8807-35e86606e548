package com.score.callmetest.support.crash

import android.annotation.SuppressLint
import android.content.Context
import android.content.pm.PackageInfo
import android.content.pm.PackageManager
import android.os.Build
import android.os.Environment
import android.os.StatFs
import android.util.Log
import com.score.callmetest.BuildConfig
import com.score.callmetest.CallmeApplication
import com.score.callmetest.util.logD
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import kotlinx.coroutines.withContext
import java.io.BufferedWriter
import java.io.File
import java.io.FileWriter
import java.io.PrintWriter
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.concurrent.atomic.AtomicLong

/**
 * 日志转储工具类
 *
 * 功能：
 * - 崩溃日志保存到本地文件
 * - 普通日志记录（仅Debug模式）
 * - 设备信息收集
 * - 文件大小管理和清理
 * - 线程安全的文件操作
 */
object DumpLogHelper {
    private const val TAG = "DumpLogHelper"

    // 文件相关常量
    private const val CRASH_FILE_PREFIX = "crash"
    private const val LOG_FILE_PREFIX = "log"
    private const val FILE_EXTENSION = ".txt"

    // 文件大小限制（10MB）
    private const val MAX_FILE_SIZE = 10 * 1024 * 1024 * 1024L

    // 最大保留文件数量
    private const val MAX_FILE_COUNT = 5

    // 文件操作互斥锁
    private val fileMutex = Mutex()

    // 文件计数器
    private val fileCounter = AtomicLong(0)

    // 日期格式化器
    @SuppressLint("ConstantLocale")
    private val dateFormatter = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
    @SuppressLint("ConstantLocale")
    private val timeFormatter = SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS", Locale.getDefault())

    /**
     * 获取日志存储目录
     */
    private fun getLogDirectory(): File {
        val context = CallmeApplication.context

        // 优先使用外部缓存目录，如果不可用则使用内部缓存目录
        val baseDir = when {
            Environment.getExternalStorageState() == Environment.MEDIA_MOUNTED -> {
                context.externalCacheDir ?: context.cacheDir
            }
            else -> context.cacheDir
        }

        val logDir = File(baseDir, "logs")
        if (!logDir.exists()) {
            logDir.mkdirs()
        }
        return logDir
    }

    /**
     * 保存崩溃日志到文件
     * @param ex 异常对象（可选）
     * @param message 错误消息（可选）
     * @param tag 日志标签（可选）
     * @param thread 发生异常的线程（可选）
     */
    suspend fun dumpCrashToFile(
        ex: Throwable? = null,
        message: String? = null,
        tag: String? = null,
        thread: Thread? = null
    ) = withContext(Dispatchers.IO) {
        // 如果异常和消息都为空，则不处理
        if (ex == null && message.isNullOrBlank()) return@withContext

        fileMutex.withLock {
            try {
                val logDir = getLogDirectory()
                val currentTime = System.currentTimeMillis()
                val date = dateFormatter.format(Date(currentTime))
                val timestamp = timeFormatter.format(Date(currentTime))

                // 获取或创建崩溃日志文件（不要每次都创建新文件）
                val fileName = "${CRASH_FILE_PREFIX}_${date}$FILE_EXTENSION"
                val file = File(logDir, fileName)

                // 检查文件大小，如果超过限制则创建新文件
                val actualFile = if (file.exists() && file.length() > MAX_FILE_SIZE) {
                    File(logDir, "${CRASH_FILE_PREFIX}_${date}_${fileCounter.incrementAndGet()}$FILE_EXTENSION")
                } else {
                    file
                }

                PrintWriter(BufferedWriter(FileWriter(actualFile, true))).use { writer ->
                    // 如果是新文件，写入设备信息
                    if (!actualFile.exists() || actualFile.length() == 0L) {
                        writer.println()
                        writeDeviceInfo(writer)
                        writer.println()
                        writeMemoryInfo(writer)
                    }

                    // 写入分隔符
                    writer.println("=========BEGIN==============")
                    writer.println("=== CRASH/ERROR REPORT ===")
                    writer.println("Time: $timestamp")
                    writer.println("Thread: ${thread?.name ?: Thread.currentThread().name}")
                    if (tag != null) {
                        writer.println("Tag: $tag")
                    }

                    writer.println()

                    // 写入错误消息（如果有）
                    if (!message.isNullOrBlank()) {
                        writer.println("=== ERROR MESSAGE ===")
                        writer.println(message)
                        writer.println()
                    }

                    // 写入异常堆栈（如果有）
                    if (ex != null) {
                        writer.println("=== EXCEPTION STACK TRACE ===")
                        ex.printStackTrace(writer)
                        writer.println()
                    }

                    writer.println("=========END==============")
                    writer.println()
                }

                println("崩溃日志已保存: ${actualFile.absolutePath}")

                // 清理旧文件
                cleanupOldFiles(logDir, CRASH_FILE_PREFIX)

            } catch (e: Exception) {
                System.err.println("保存崩溃日志失败..${e.toString()}")
            }
        }
    }

    /**
     * 保存普通日志到文件
     * @param log 日志内容
     * @param tag 日志标签
     * @param priority 日志级别
     */
    suspend fun dumpLogToFile(log: String?, tag: String? = null, priority: Int = Log.INFO) = withContext(Dispatchers.IO) {
        // 检查日志内容是否为空
        if (log.isNullOrBlank()) {
            return@withContext
        }

        // 根据构建类型和日志级别决定是否记录
        val shouldLog = when {
            BuildConfig.DEBUG -> true  // Debug模式记录所有日志
            priority >= Log.WARN -> true  // Release模式只记录WARN及以上级别
            else -> false
        }

        if (!shouldLog) {
            return@withContext
        }

        fileMutex.withLock {
            try {
                val logDir = getLogDirectory()
                val currentTime = System.currentTimeMillis()
                val date = dateFormatter.format(Date(currentTime))
                val timestamp = timeFormatter.format(Date(currentTime))

                val fileName = "${LOG_FILE_PREFIX}_${date}$FILE_EXTENSION"
                val file = File(logDir, fileName)

                // 检查文件大小，如果超过限制则创建新文件
                val actualFile = if (file.exists() && file.length() > MAX_FILE_SIZE) {
                    File(logDir, "${LOG_FILE_PREFIX}_${date}_${fileCounter.incrementAndGet()}$FILE_EXTENSION")
                } else {
                    file
                }

                PrintWriter(BufferedWriter(FileWriter(actualFile, true))).use { writer ->
                    // 如果是新文件，写入设备信息
                    if (!actualFile.exists() || actualFile.length() == 0L) {
                        writer.println("=== LOG SESSION START ===")
                        writeDeviceInfo(writer)
                        writer.println("===========================")
                    }

                    val priorityStr = when (priority) {
                        Log.VERBOSE -> "V"
                        Log.DEBUG -> "D"
                        Log.INFO -> "I"
                        Log.WARN -> "W"
                        Log.ERROR -> "E"
                        else -> "U"
                    }

                    writer.println("$timestamp [$priorityStr/${tag ?: "APP"}] [${Thread.currentThread().name}] $log")
                }

                // 清理旧的日志文件
                cleanupOldFiles(logDir, LOG_FILE_PREFIX)

            } catch (e: Exception) {
                System.err.println("保存日志失败..${e.toString()}")
            }
        }
    }

    /**
     * 写入设备和应用信息
     */
    private fun writeDeviceInfo(writer: PrintWriter) {
        try {
            val context = CallmeApplication.context
            val pm = context.packageManager
            val pi = pm.getPackageInfo(context.packageName, PackageManager.GET_ACTIVITIES)

            writer.println("=== DEVICE & APP INFO ===")

            // 应用信息
            writer.println("App Package: ${context.packageName}")
            writer.println("App Version: ${pi.versionName}")
            writer.print("App Version Code: ")
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                writer.println(pi.longVersionCode)
            } else {
                @Suppress("DEPRECATION")
                writer.println(pi.versionCode)
            }
            writer.println("Build Type: ${BuildConfig.BUILD_TYPE}")
            writer.println("Debug: ${BuildConfig.DEBUG}")

            // 系统信息
            writer.println("OS Version: Android ${Build.VERSION.RELEASE} (API ${Build.VERSION.SDK_INT})")
            writer.println("Manufacturer: ${Build.MANUFACTURER}")
            writer.println("Brand: ${Build.BRAND}")
            writer.println("Model: ${Build.MODEL}")
            writer.println("Device: ${Build.DEVICE}")
            writer.println("Product: ${Build.PRODUCT}")
            writer.println("Hardware: ${Build.HARDWARE}")
            writer.println("Board: ${Build.BOARD}")
            writer.println("Bootloader: ${Build.BOOTLOADER}")
            writer.println("Fingerprint: ${Build.FINGERPRINT}")

            // CPU架构
            writer.print("CPU ABIs: ")
            writer.println(Build.SUPPORTED_ABIS.joinToString(", "))

            // 屏幕信息
            val displayMetrics = context.resources.displayMetrics
            writer.println("Screen: ${displayMetrics.widthPixels}x${displayMetrics.heightPixels}, density=${displayMetrics.density}")

        } catch (e: Exception) {
            writer.println("Failed to collect device info: ${e.message}")
        }
    }

    /**
     * 写入内存信息
     */
    private fun writeMemoryInfo(writer: PrintWriter) {
        try {
            writer.println("=== MEMORY INFO ===")

            val runtime = Runtime.getRuntime()
            val maxMemory = runtime.maxMemory()
            val totalMemory = runtime.totalMemory()
            val freeMemory = runtime.freeMemory()
            val usedMemory = totalMemory - freeMemory

            writer.println("Max Memory: ${formatBytes(maxMemory)}")
            writer.println("Total Memory: ${formatBytes(totalMemory)}")
            writer.println("Used Memory: ${formatBytes(usedMemory)}")
            writer.println("Free Memory: ${formatBytes(freeMemory)}")
            writer.println("Memory Usage: ${(usedMemory * 100 / maxMemory)}%")

            // 存储空间信息
            val logDir = getLogDirectory()
            if (logDir.exists()) {
                val stat = StatFs(logDir.path)
                val availableBytes = stat.availableBytes
                val totalBytes = stat.totalBytes
                writer.println("Storage Available: ${formatBytes(availableBytes)}")
                writer.println("Storage Total: ${formatBytes(totalBytes)}")
            }

        } catch (e: Exception) {
            writer.println("Failed to collect memory info: ${e.message}")
        }
    }

    /**
     * 写入所有线程堆栈信息（仅Debug模式）
     */
    private fun writeAllThreadStacks(writer: PrintWriter) {
        try {
            writer.println("=== ALL THREAD STACKS ===")

            val threadMap = Thread.getAllStackTraces()
            threadMap.forEach { (thread, stackTrace) ->
                writer.println("Thread: ${thread.name} (${thread.state})")
                stackTrace.forEach { element ->
                    writer.println("    at $element")
                }
                writer.println()
            }
        } catch (e: Exception) {
            writer.println("Failed to collect thread stacks: ${e.message}")
        }
    }

    /**
     * 清理旧文件
     */
    private fun cleanupOldFiles(directory: File, prefix: String) {
        try {
            val files = directory.listFiles { _, name ->
                name.startsWith(prefix) && name.endsWith(FILE_EXTENSION)
            }?.sortedByDescending { it.lastModified() }

            if (files != null && files.size > MAX_FILE_COUNT) {
                files.drop(MAX_FILE_COUNT).forEach { file ->
                    if (file.delete()) {
                        println("删除旧日志文件: ${file.name}")
                    }
                }
            }
        } catch (e: Exception) {
            System.err.println("清理旧文件失败..${e.toString()}")
        }
    }

    /**
     * 格式化字节数
     */
    private fun formatBytes(bytes: Long): String {
        val units = arrayOf("B", "KB", "MB", "GB")
        var size = bytes.toDouble()
        var unitIndex = 0

        while (size >= 1024 && unitIndex < units.size - 1) {
            size /= 1024
            unitIndex++
        }

        return "%.2f %s".format(size, units[unitIndex])
    }

    /**
     * 获取所有日志文件
     */
    fun getAllLogFiles(): List<File> {
        return try {
            val logDir = getLogDirectory()
            logDir.listFiles { _, name ->
                name.endsWith(FILE_EXTENSION)
            }?.toList() ?: emptyList()
        } catch (e: Exception) {
            System.err.println("获取日志文件列表失败..${e.toString()}")
            emptyList()
        }
    }

    /**
     * 清理所有日志文件
     */
    fun clearAllLogs(): Boolean {
        return try {
            val logDir = getLogDirectory()
            val files = logDir.listFiles { _, name ->
                name.endsWith(FILE_EXTENSION)
            }

            var success = true
            files?.forEach { file ->
                if (!file.delete()) {
                    success = false
                }
            }
            success
        } catch (e: Exception) {
            System.err.println("清理所有日志失败..${e.toString()}")
            false
        }
    }
}