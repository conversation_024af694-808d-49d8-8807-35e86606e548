package com.score.callmetest.support.crash

import android.util.Log
import timber.log.Timber

/**
 * 自定义 Timber Tree，专门处理 WARN 和 ERROR 级别的日志
 *
 * 职责：
 * - 只处理 WARN 和 ERROR 级别的日志
 * - 收集日志信息（包括普通消息和异常）
 * - 转发给 FakeCrashLibrary 进行统一处理
 */
class CrashReportingTree : Timber.Tree() {

    override fun log(priority: Int, tag: String?, message: String, t: Throwable?) {
        // 只处理 WARN 和 ERROR 级别的日志
        if (priority < Log.WARN) {
            return
        }

        // 转发给 FakeCrashLibrary 进行统一处理
        when (priority) {
            Log.WARN -> {
                FakeCrashLibrary.logWarning(message, t, tag)
            }
            Log.ERROR -> {
                FakeCrashLibrary.logError(message, t, tag)
            }
        }
    }
}

