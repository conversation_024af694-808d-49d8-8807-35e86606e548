package com.score.callmetest.support.crash

import android.annotation.SuppressLint
import android.content.Context
import android.os.Process
import timber.log.Timber

/**
 * 全局崩溃处理器
 *
 * 职责：
 * - 捕获未处理的异常
 * - 保存崩溃日志
 * - 优雅退出应用
 */
class CrashHandler private constructor() : Thread.UncaughtExceptionHandler {

    companion object {
        private const val TAG = "CrashHandler"

        @SuppressLint("StaticFieldLeak")
        @Volatile
        private var INSTANCE: CrashHandler? = null

        /**
         * 初始化崩溃处理器
         */
        fun init() {
            if (INSTANCE == null) {
                synchronized(this) {
                    if (INSTANCE == null) {
                        INSTANCE = CrashHandler().apply {
                            initialize()
                        }
                    }
                }
            }
        }
    }

    private var defaultHandler: Thread.UncaughtExceptionHandler? = null

    /**
     * 初始化方法
     */
    private fun initialize() {
        // 获取系统默认的UncaughtException处理器
        defaultHandler = Thread.getDefaultUncaughtExceptionHandler()
        // 设置该CrashHandler为程序的默认处理器
        Thread.setDefaultUncaughtExceptionHandler(this)
        Timber.tag(TAG).d("崩溃处理器初始化完成")
    }

    /**
     * 当UncaughtException发生时会转入该函数来处理
     */
    override fun uncaughtException(thread: Thread, ex: Throwable) {
        Timber.tag(TAG).e(ex, "捕获到未处理异常，线程: ${thread.name}")

        val handled = handleException(ex)

        if (!handled && defaultHandler != null) {
            // 如果自定义处理失败，则让系统默认的异常处理器来处理
            defaultHandler?.uncaughtException(thread, ex)
        } else {
            // 延迟后终止进程，确保日志写入完成
            try {
                Thread.sleep(1000)
            } catch (e: InterruptedException) {
                // 忽略
            }
            Process.killProcess(Process.myPid())
        }
    }

    /**
     * 自定义错误处理，收集错误信息并保存日志
     */
    private fun handleException(ex: Throwable?): Boolean {
        if (ex == null) {
            return false
        }

        return try {
            // 打印异常堆栈到控制台
            ex.printStackTrace()

            // 保存崩溃日志到本地文件
            Timber.e(ex)

            true
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "处理异常时发生错误")
            false
        }
    }
}