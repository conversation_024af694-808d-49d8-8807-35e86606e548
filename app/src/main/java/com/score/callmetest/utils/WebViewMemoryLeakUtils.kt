package com.score.callmetest.utils

import android.view.ViewGroup
import android.webkit.WebView
import timber.log.Timber

/**
 * WebView内存泄漏防护工具类
 * 
 * 提供统一的WebView清理方法，防止内存泄漏
 * 
 * 使用方法：
 * ```kotlin
 * override fun onDestroy() {
 *     WebViewMemoryLeakUtils.cleanupWebView(webView, "JSBridgeService")
 *     super.onDestroy()
 * }
 * ```
 */
object WebViewMemoryLeakUtils {

    /**
     * 清理WebView资源，防止内存泄漏
     * 
     * @param webView 要清理的WebView实例
     * @param jsInterfaceName JavaScript接口名称（如果有的话）
     */
    fun cleanupWebView(webView: WebView?, jsInterfaceName: String? = null) {
        if (webView == null) {
            Timber.d("WebView is null, no cleanup needed")
            return
        }

        try {
            Timber.d("开始清理WebView资源")

            // 停止加载
            webView.stopLoading()

            // 清除历史记录
            webView.clearHistory()

            // 清除缓存
            webView.clearCache(true)

            // 清除表单数据
            webView.clearFormData()

            // 移除JavaScript接口（如果指定了接口名）
            jsInterfaceName?.let { name ->
                try {
                    webView.removeJavascriptInterface(name)
                    Timber.d("已移除JavaScript接口: $name")
                } catch (e: Exception) {
                    Timber.w(e, "移除JavaScript接口时出现异常: $name")
                }
            }

            // 设置WebViewClient和WebChromeClient为null
            // WebViewClient不能设置为null，所以跳过这一步
            // webView.webViewClient = null
            webView.webChromeClient = null

            // 暂停所有布局、解析、JavaScript计时器
            webView.onPause()
            webView.pauseTimers()

            // 从父容器中移除WebView
            removeWebViewFromParent(webView)

            // 销毁WebView
            webView.destroy()

            Timber.d("WebView资源清理完成")

        } catch (e: Exception) {
            Timber.e(e, "清理WebView资源时出现异常")
        }
    }

    /**
     * 从父容器中移除WebView
     * 
     * @param webView 要移除的WebView
     */
    private fun removeWebViewFromParent(webView: WebView) {
        try {
            val parent = webView.parent as? ViewGroup
            parent?.removeView(webView)
            Timber.d("已从父容器中移除WebView")
        } catch (e: Exception) {
            Timber.w(e, "从父容器中移除WebView时出现异常，可能已经被移除")
        }
    }

    /**
     * 配置WebView设置以减少内存占用
     * 
     * @param webView 要配置的WebView
     * @param enableCache 是否启用缓存（默认false以减少内存占用）
     */
    fun configureWebViewForMemoryOptimization(webView: WebView, enableCache: Boolean = false) {
        webView.settings.apply {
            // 基本设置
            javaScriptEnabled = true
            domStorageEnabled = true
            loadWithOverviewMode = true
            useWideViewPort = true
            setSupportZoom(false)
            builtInZoomControls = false

            // 安全设置，防止潜在的安全问题和内存泄漏
            allowFileAccess = false
            allowContentAccess = false
            allowFileAccessFromFileURLs = false
            allowUniversalAccessFromFileURLs = false

            // 缓存设置
            if (!enableCache) {
                cacheMode = android.webkit.WebSettings.LOAD_NO_CACHE
                // setAppCacheEnabled已废弃，使用cacheMode替代
                databaseEnabled = false
            }

            // 禁用地理位置以减少权限和内存占用
            setGeolocationEnabled(false)
        }

        Timber.d("WebView内存优化配置完成")
    }

    /**
     * 暂停WebView以节省资源
     * 
     * @param webView 要暂停的WebView
     */
    fun pauseWebView(webView: WebView?) {
        webView?.apply {
            onPause()
            pauseTimers()
        }
    }

    /**
     * 恢复WebView
     * 
     * @param webView 要恢复的WebView
     */
    fun resumeWebView(webView: WebView?) {
        webView?.apply {
            onResume()
            resumeTimers()
        }
    }
}
