package com.score.callmetest.constants

/**
 * Socket 命令常量类
 * 统一管理所有 Socket 通信中使用的命令字符串
 *
 * 设计目标：
 * 1. 避免硬编码字符串，减少拼写错误
 * 2. 便于维护和修改命令名称
 * 3. 提供类型安全的命令引用
 * 4. 支持IDE的自动补全和重构
 */
object SocketCommands {
    
    /**
     * 通话相关命令
     */
    object Call {
        /** 发起通话 */
        const val ON_CALL = "onCall"
        
        /** 挂断通话 */
        const val ON_HANG_UP = "onHangUp"
        
        /** 接听通话 ps.这里的接听，是对方接听。经检测，对方打过来，这里点击接听，是不触发这个的。 */
        const val ON_PICK_UP = "onPickUp"
        
        /** 预计挂断时间 */
        const val ESTIMATED_HANG_UP_TIME = "estimatedHangUpTime"

        /**.匹配免费次数 */
        const val FLASH_CHAT_FREE_TIMES = "flashChatFreeTimes"

        /**.免费通话时长 */
        const val FREE_CALL_DURATION = "freeCallDuration"
    }
    
    /**
     * 消息相关命令
     */
    object Message {
        /** 聊天消息 - 新的命令名称 */
        const val ON_CHAT = "onChat"
        
        /** 消息事件 - 保留兼容性 */
        @Deprecated("使用 ON_CHAT 替代", ReplaceWith("ON_CHAT"))
        const val MESSAGE_EVENT = "messageEvent"
    }
    
    /**
     * 金币相关命令
     */
    object Coins {
        /** 可用金币数量 */
        const val AVAILABLE_COINS = "availableCoins"
    }
    
    /**
     * 订单相关命令
     */
    object Order {
        /** 充值订单状态 */
        const val RECHARGE_ORDER_STATUS = "rechargeOrderStatus"
    }
    
    /**
     * 获取所有支持的命令列表
     * 用于注册监听器
     */
    fun getAllCommands(): Array<String> {
        return arrayOf(
            Call.ON_CALL,
            Call.ON_HANG_UP,
            Call.ON_PICK_UP,
            Call.ESTIMATED_HANG_UP_TIME,
            Call.FLASH_CHAT_FREE_TIMES,
            Call.FREE_CALL_DURATION,
            Message.ON_CHAT,
            Message.MESSAGE_EVENT, // 保留兼容性
            Coins.AVAILABLE_COINS,
            Order.RECHARGE_ORDER_STATUS
        )
    }
    
    /**
     * 验证命令是否有效
     * @param command 要验证的命令
     * @return 是否为有效命令
     */
    fun isValidCommand(command: String): Boolean {
        return getAllCommands().contains(command)
    }
    
    /**
     * 获取命令的显示名称
     * 用于日志和调试
     */
    fun getCommandDisplayName(command: String): String {
        return when (command) {
            Call.ON_CALL -> "发起通话"
            Call.ON_HANG_UP -> "挂断通话"
            Call.ON_PICK_UP -> "接听通话"
            Call.ESTIMATED_HANG_UP_TIME -> "预计挂断时间"
            Call.FLASH_CHAT_FREE_TIMES -> "免费匹配次数"
            Call.FREE_CALL_DURATION -> "免费通话时间"
            Message.ON_CHAT -> "聊天消息"
            Message.MESSAGE_EVENT -> "消息事件(已废弃)"
            Coins.AVAILABLE_COINS -> "可用金币"
            Order.RECHARGE_ORDER_STATUS -> "充值订单状态"
            else -> "未知命令($command)"
        }
    }
}