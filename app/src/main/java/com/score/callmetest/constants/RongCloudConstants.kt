package com.score.callmetest.constants

/**
 * 融云 IM 相关常量
 */
object RongCloudConstants {
    
    /**
     * 融云消息事件标识
     *
     * 例 「name： messageEvent ,data:messageEvent--{"code":"200","commandId":"rc1753754757588209","command":"onDev","data":{"nickName":"🇧🇷 1V1邀请 A级","content":"name=🇧🇷 1V1邀请 A级; onlineTime=1028min; vchatCount=18; channel=s0lmfyhsrzgz; brand=Redmi; model=2409BRN2CC; sdk=34; net=WIFI; ver=2.6.3;\n🇧🇷 1V1邀请 A级:onCall In Fg","timestamp":"1753754757588","type":"console"}}」
     *
     *  「messageEvent--{"code":"200","commandId":"rc1753755215860215","command":"onChat","data":{"fromUserId":"1888868880055664640","content":"z","timestamp":"1753755215859"}}」
     *
     * 用于 CommandMessage 的 name 字段
     */
    const val MESSAGE_EVENT = "messageEvent"

    /**
     * 响应事件（例如，onHangUp）
     *
     * 例 name: responseEvent ,data:{"code":"200","command":"onHangUp","commandId":"b47682cd1e3940c6a02690b244279ef1","data":{"channelName":"s0lmfyhsrzgz","callCategory":3,"callType":1,"isQuickMatch":false,"clientSessionId":"fa8588aa-dde2-404c-9eae-046e6620f0ee","fromUserId":1888868880055664640,"toUserId":1943267396651450368,"h5FromUserId":"1888868880055664640","h5ToUserId":"1943267396651450368","reason":1,"command":"onHangUp"},"timestamp":1753754758517}
     *
     * 用于 CommandMessage 的 name 字段
     */
    const val RESPONSE_EVENT = "responseEvent"

    /**
     * 礼物索要
     *
     * 例 onGiftAsk--{"code":"200","command":"onGiftAsk","commandId":"1753755299008","data":{"fromUserId":"1888868880055664640","content":"{\"code\":\"3\",\"coinPrice\":199,\"count\":0,\"giftDesc\":\"1\",\"iconPath\":\"https:\/\/test-agilecdn.livchat.me\/livchat\/images\/dev\/20230602\/1685676175739.png?Expires\\u003d1759637223\\u0026OSSAccessKeyId\\u003dLTAI5tNPNT6g1inCHkxgcUGz\\u0026Signature\\u003drTcg2QJMDqGKoeBtuatK%2FGuXHe8%3D\\u0026x-oss-process\\u003dimage%2Fresize%2Cm_lfit%2Ch_1080%2Cw_1080%2Climit_1\",\"iconThumbPath\":\"https:\/\/test-agilecdn.livchat.me\/livchat\/images\/dev\/20230602\/1685676175739.png?Expires\\u003d1759637223\\u0026OSSAccessKeyId\\u003dLTAI5tNPNT6g1inCHkxgcUGz\\u0026Signature\\u003dI1Q4aJH1e0blbHpPgG6mgjCi0%2B4%3D\\u0026x-oss-process\\u003dimage%2Fresize%2Cm_lfit%2Ch_160%2Cw_160\",\"name\":\"\",\"sortNo\":3,\"unlockLevel\":0,\"uuid\":\"b5db05b6-c532-4e69-9f3e-2a5b2b1b54d8\"}","timestamp":"1753755299008"}}
     *
     * 用于 CommandMessage 的 name 字段
     */
    const val ON_GIFT_ASK = "onGiftAsk"

    /**
     * 命令ID生成器
     */
    object CommandId {
        private var commandIdIndex = 0
        
        /**
         * 生成唯一的命令ID
         * 格式：rc + 时间戳 + 递增索引
         */
        @Synchronized
        fun generate(): String {
            return "rc${System.currentTimeMillis()}${++commandIdIndex}"
        }
        
        /**
         * 重置命令ID索引
         * 主要用于测试
         */
        @Synchronized
        fun reset() {
            commandIdIndex = 0
        }
    }
    
    /**
     * 响应码
     */
    object ResponseCode {
        const val SUCCESS = "200"
        const val ERROR = "500"
    }
}