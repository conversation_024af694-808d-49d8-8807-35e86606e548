package com.score.callmetest.db

import android.content.Context

/**
 * 数据库工厂类，提供获取数据库实现的方法
 * 如果后续需要替换Room为其他数据库实现，只需要修改这个工厂类即可
 */
object DatabaseFactory {
    /**
     * 数据库类型枚举
     */
    enum class DatabaseType {
        ROOM, // Room数据库
        // 其他可能的数据库实现，如REALM, SQLITE等
    }
    
    /**
     * 获取数据库实现
     * @param context 上下文
     * @param type 数据库类型，默认为Room
     * @return 数据库操作接口实现
     */
    fun getDatabase(context: Context, type: DatabaseType = DatabaseType.ROOM): DatabaseContract {
        return when (type) {
            DatabaseType.ROOM -> DatabaseManager.getInstance(context)
            // 如果后续添加其他数据库实现，在这里添加对应的case
            // DatabaseType.REALM -> RealmDatabaseManager.getInstance(context)
            // DatabaseType.SQLITE -> SQLiteDatabaseManager.getInstance(context)
        }
    }
} 