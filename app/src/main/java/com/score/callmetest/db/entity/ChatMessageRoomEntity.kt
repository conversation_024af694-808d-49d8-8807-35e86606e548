package com.score.callmetest.db.entity

import androidx.core.net.toUri
import androidx.room.Entity
import androidx.room.Ignore
import androidx.room.PrimaryKey
import androidx.room.TypeConverters
import com.score.callmetest.entity.ChatMessageEntity
import com.score.callmetest.entity.MessageType
import com.score.callmetest.entity.MessageStatus
import com.score.callmetest.network.GiftInfo
import java.io.File

/**
 * 聊天消息Room实体类
 */
@Entity(tableName = "chat_messages")
data class ChatMessageRoomEntity(
    @PrimaryKey
    val messageId: String,
    var rcMsgId: String? = null,
    val currentUserId: String,
    val senderId: String,
    val senderName: String,
    val senderAvatar: String,
    val receiverId: String,
    val content: String,
    var contentType: String? = null,
    val messageType: MessageType = MessageType.TEXT,
    var status: MessageStatus = MessageStatus.SENDING,
    var timestamp: Long = System.currentTimeMillis(),
    var progress: Int = 0,
    val isCurrentUser: Boolean,
    var thumbUri: String? = null,
    var mediaUri: String? = null,
    var mediaLocalUri: String? = null,
    var mediaDuration: Long = 0,
    val giftInfo: GiftInfo? = null,
    var isAutoTrans: Boolean = true,
    var extra: String? = null
) {

    @Ignore
    var isChange: Boolean = false
    @Ignore
    var isPlaying: Boolean = false

    /**
     * 转换为对应的实体类
     */
    fun toEntity(): ChatMessageEntity {
        return ChatMessageEntity(
            messageId = messageId,
            rcMsgId = rcMsgId,
            currentUserId = currentUserId,
            senderId = senderId,
            senderName = senderName,
            senderAvatar = senderAvatar,
            receiverId = receiverId,
            content = content,
            contentType = contentType,
            messageType = messageType,
            status = status,
            timestamp = timestamp,
            progress = progress,
            isCurrentUser = isCurrentUser,
            thumbUri = thumbUri?.toUri(),
            mediaUri = mediaUri?.toUri(),
            mediaLocalUri = mediaLocalUri?.toUri(),
            mediaDuration = mediaDuration,
            giftInfo = giftInfo, // 使用GiftInfoConverter自动处理序列化/反序列化
            isAutoTrans = true,
            extra = extra
        )
    }
    
    companion object {
        /**
         * 从实体类转换为Room实体
         */
        fun fromEntity(entity: ChatMessageEntity): ChatMessageRoomEntity {
            return ChatMessageRoomEntity(
                messageId = entity.messageId,
                rcMsgId = entity.rcMsgId,
                currentUserId = entity.currentUserId,
                senderId = entity.senderId,
                senderName = entity.senderName,
                senderAvatar = entity.senderAvatar,
                receiverId = entity.receiverId,
                content = entity.content,
                contentType = entity.contentType,
                messageType = entity.messageType,
                status = entity.status,
                timestamp = entity.timestamp,
                progress = entity.progress,
                isCurrentUser = entity.isCurrentUser,
                thumbUri = entity.thumbUri?.toString(),
                mediaUri = entity.mediaUri?.toString(),
                mediaLocalUri = entity.mediaLocalUri?.toString(),
                mediaDuration = entity.mediaDuration,
                giftInfo = entity.giftInfo, // 使用GiftInfoConverter自动处理序列化
                isAutoTrans = entity.isAutoTrans,
                extra = entity.extra
            )
        }
    }
}