package com.score.callmetest.db.dao

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import com.score.callmetest.db.entity.CallHistoryRoomEntity
import kotlinx.coroutines.flow.Flow

/**
 * 通话历史DAO接口
 */
@Dao
interface CallHistoryDao {
    /**
     * 插入一条通话历史
     * @param callHistory 通话历史实体
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertCallHistory(callHistory: CallHistoryRoomEntity)
    
    /**
     * 批量插入通话历史
     * @param callHistories 通话历史实体列表
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertCallHistories(callHistories: List<CallHistoryRoomEntity>)
    
    /**
     * 更新一条通话历史
     * @param callHistory 通话历史实体
     */
    @Update
    suspend fun updateCallHistory(callHistory: CallHistoryRoomEntity)
    
    /**
     * 删除一条通话历史
     * @param callHistory 通话历史实体
     */
    @Delete
    suspend fun deleteCallHistory(callHistory: CallHistoryRoomEntity)
    
    /**
     * 根据ID删除一条通话历史
     * @param id 通话历史ID
     */
    @Query("DELETE FROM call_history WHERE id = :id")
    suspend fun deleteCallHistoryById(id: String)
    
    /**
     * 获取所有通话历史，按是否置顶和时间排序
     * @return 通话历史流
     */
    @Query("SELECT * FROM call_history ORDER BY isPinned DESC, callEndTime DESC")
    fun getAllCallHistories(): Flow<List<CallHistoryRoomEntity>>

    /**
     * 获取当前用户的所有通话历史，按是否置顶和时间排序
     * @param currentUserId 当前用户ID
     * @return 通话历史流
     */
    @Query("SELECT * FROM call_history WHERE currentUserId = :currentUserId ORDER BY isPinned DESC, callEndTime DESC")
    fun getAllCallHistoriesByCurrentUserId(currentUserId: String): Flow<List<CallHistoryRoomEntity>>

    /**
     * 获取特定用户的通话历史，按时间戳倒序排序
     * @param userId 用户ID
     * @return 通话历史流
     */
    @Query("SELECT * FROM call_history WHERE userId = :userId ORDER BY callEndTime DESC")
    fun getCallHistoriesByUserId(userId: String): Flow<List<CallHistoryRoomEntity>>

    /**
     * 根据ID获取一条通话历史
     * @param id 通话历史ID
     * @return 通话历史实体
     */
    @Query("SELECT * FROM call_history WHERE id = :id")
    suspend fun getCallHistoryById(id: String): CallHistoryRoomEntity?

    /**
     * 清空所有通话历史
     */
    @Query("DELETE FROM call_history")
    suspend fun clearAllCallHistories()

    /**
     * 清空当前用户的所有通话历史
     * @param currentUserId 当前用户ID
     */
    @Query("DELETE FROM call_history WHERE currentUserId = :currentUserId")
    suspend fun clearAllCallHistoriesByCurrentUserId(currentUserId: String)
} 