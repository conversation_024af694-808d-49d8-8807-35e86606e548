package com.score.callmetest.db

import androidx.room.Query
import com.score.callmetest.db.entity.ChatMessageRoomEntity
import com.score.callmetest.entity.CallHistoryEntity
import com.score.callmetest.entity.ChatMessageEntity
import com.score.callmetest.entity.MessageListEntity
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.withContext

/**
 * 数据库操作接口，定义了数据库的所有操作
 * 该接口作为数据库操作的抽象，方便后续替换Room为其他数据库实现
 */
interface DatabaseContract {
    
    // <editor-folder desc="消息列表相关操作">
    
    /**
     * 插入一条消息列表项
     * @param messageList 消息列表项实体
     * @param callback 回调函数，在操作成功或失败时调用
     */
    fun insertMessageList(messageList: MessageListEntity, callback: ((Boolean) -> Unit)? = null)
    
    /**
     * 批量插入消息列表项
     * @param messageLists 消息列表项实体列表
     * @param callback 回调函数，在操作成功或失败时调用
     */
    fun insertMessageLists(messageLists: List<MessageListEntity>, callback: ((Boolean) -> Unit)? = null)

    /**
     * 更新当前用户的消息列表项
     * @param messageList 消息列表项实体
     * @param callback 回调函数，在操作成功或失败时调用
     */
    fun updateCurrentUserMessageList(messageList: MessageListEntity, callback: ((Boolean) -> Unit)? = null)
    
    /**
     * 删除一条消息列表项
     * @param messageList 消息列表项实体
     * @param callback 回调函数，在操作成功或失败时调用
     */
    fun deleteMessageList(messageList: MessageListEntity, callback: ((Boolean) -> Unit)? = null)
    
    /**
     * 根据用户ID删除一条消息列表项
     * @param userId 用户ID
     * @param callback 回调函数，在操作成功或失败时调用
     */
    fun deleteMessageListById(userId: String, callback: ((Boolean) -> Unit)? = null)

    /**
     * 获取当前用户的所有消息列表项，按是否置顶和时间排序
     * @return 消息列表项流
     */
    fun getCurrentUserAllMessageLists(): Flow<List<MessageListEntity>>

    /**
     * 根据用户ID获取一条消息列表项
     * @param userId 用户ID
     * @param callback 回调函数，在操作成功或失败时调用，参数为消息列表项实体或null
     */
    fun getMessageListById(userId: String, callback: (MessageListEntity?) -> Unit)
    
    /**
     * 更新未读消息数
     * @param userId 用户ID
     * @param unreadCount 未读消息数
     * @param callback 回调函数，在操作成功或失败时调用
     */
    fun updateUnreadCount(userId: String, unreadCount: Int, callback: ((Boolean) -> Unit)? = null)
    
    /**
     * 更新是否置顶
     * @param userId 用户ID
     * @param isPinned 是否置顶
     * @param callback 回调函数，在操作成功或失败时调用
     */
    fun updatePinStatus(userId: String, isPinned: Boolean, callback: ((Boolean) -> Unit)? = null)

    /**
     * 更新状态
     * @param userId 用户ID
     * @param status 状态
     * @param callback 回调函数，在操作成功或失败时调用
     */
    fun updateStatus(userId: String, status: String, callback: ((Boolean) -> Unit)? = null)

    /**
     * 清空当前用户的所有消息列表
     * @param callback 回调函数，在操作成功或失败时调用
     */
    fun clearCurrentUserAllMessageLists(callback: ((Boolean) -> Unit)? = null)

    // </editor-folder>

    // <editor-folder desc="通话历史相关操作">

    /**
     * 插入一条通话历史
     * @param callHistory 通话历史实体
     * @param callback 回调函数，在操作成功或失败时调用
     */
    fun insertCallHistory(callHistory: CallHistoryEntity, callback: ((Boolean) -> Unit)? = null)
    
    /**
     * 批量插入通话历史
     * @param callHistories 通话历史实体列表
     * @param callback 回调函数，在操作成功或失败时调用
     */
    fun insertCallHistories(callHistories: List<CallHistoryEntity>, callback: ((Boolean) -> Unit)? = null)
    


    /**
     * 更新当前用户的通话历史
     * @param callHistory 通话历史实体
     * @param callback 回调函数，在操作成功或失败时调用
     */
    fun updateCurrentUserCallHistory(callHistory: CallHistoryEntity, callback: ((Boolean) -> Unit)? = null)
    
    /**
     * 删除一条通话历史
     * @param callHistory 通话历史实体
     * @param callback 回调函数，在操作成功或失败时调用
     */
    fun deleteCallHistory(callHistory: CallHistoryEntity, callback: ((Boolean) -> Unit)? = null)
    
    /**
     * 根据ID删除一条通话历史
     * @param id 通话历史ID
     * @param callback 回调函数，在操作成功或失败时调用
     */
    fun deleteCallHistoryById(id: String, callback: ((Boolean) -> Unit)? = null)
    

    /**
     * 获取当前用户的所有通话历史，按时间戳倒序排序
     * @return 通话历史流
     */
    fun getCurrentUserAllCallHistories(): Flow<List<CallHistoryEntity>>

    /**
     * 获取特定用户的通话历史，按时间戳倒序排序
     * @param userId 用户ID
     * @return 通话历史流
     */
    fun getCallHistoriesByUserId(userId: String): Flow<List<CallHistoryEntity>>
    
    /**
     * 根据ID获取一条通话历史
     * @param id 通话历史ID
     * @param callback 回调函数，在操作成功或失败时调用，参数为通话历史实体或null
     */
    fun getCallHistoryById(id: String, callback: (CallHistoryEntity?) -> Unit)
    


    /**
     * 清空当前用户的所有通话历史
     * @param callback 回调函数，在操作成功或失败时调用
     */
    fun clearCurrentUserAllCallHistories(callback: ((Boolean) -> Unit)? = null)


    /**
     * 清空当前用户的所有数据
     */
    fun clearCurrentUserDatas(callback: ((Boolean) -> Unit)? = null)

    // </editor-folder>
    
    // <editor-folder desc="聊天消息相关操作">
    
    /**
     * 插入一条聊天消息
     * @param chatMessage 聊天消息实体
     * @param callback 回调函数，在操作成功或失败时调用
     */
    fun insertChatMessage(chatMessage: ChatMessageEntity, callback: ((Boolean) -> Unit)? = null)
    
    /**
     * 批量插入聊天消息
     * @param chatMessages 聊天消息实体列表
     * @param callback 回调函数，在操作成功或失败时调用
     */
    fun insertChatMessages(chatMessages: List<ChatMessageEntity>, callback: ((Boolean) -> Unit)? = null)
    
    /**
     * 更新一条聊天消息
     * @param chatMessage 聊天消息实体
     * @param callback 回调函数，在操作成功或失败时调用
     */
    fun updateChatMessage(chatMessage: ChatMessageEntity, callback: ((Boolean) -> Unit)? = null)
    
    /**
     * 删除一条聊天消息
     * @param chatMessage 聊天消息实体
     * @param callback 回调函数，在操作成功或失败时调用
     */
    fun deleteChatMessage(chatMessage: ChatMessageEntity, callback: ((Boolean) -> Unit)? = null)
    
    /**
     * 根据消息ID删除一条聊天消息
     * @param messageId 消息ID
     * @param callback 回调函数，在操作成功或失败时调用
     */
    fun deleteChatMessageById(messageId: String, callback: ((Boolean) -> Unit)? = null)
    
    /**
     * 获取特定用户的所有聊天消息，按时间排序
     * @param currentUserId 当前用户ID
     * @param userId 用户ID
     * @return 聊天消息流
     */
    fun getChatMessagesByUserId(currentUserId: String, userId: String,callback: (List<ChatMessageEntity>) -> Unit)
    
    /**
     * 根据消息ID获取一条聊天消息
     * @param messageId 消息ID
     * @param callback 回调函数，在操作成功或失败时调用，参数为聊天消息实体或null
     */
    fun getChatMessageById(messageId: String, callback: (ChatMessageEntity?) -> Unit)

    /**
     * 根据融云消息ID更新localPath
     * @param rcMsgId rc消息ID
     * @param localPath 下载到本地的文件路径
     * @param callback 回调函数，在操作成功或失败时调用
     */
    fun updateLocalPathByRcId(rcMsgId: String,localPath: String,callback: ((Boolean) -> Unit)? = null)

    /**
     * 更新发送状态
     * @param messageId 消息ID
     * @param status 状态
     * @param callback 回调函数，在操作成功或失败时调用
     */
    fun updateSendStatus(messageId: String,status: String, callback: (Boolean) -> Unit)
    
    /**
     * 清空当前用户与特定用户之间的所有聊天消息
     * @param currentUserId 当前用户ID
     * @param userId 用户ID
     * @param callback 回调函数，在操作成功或失败时调用
     */
    fun clearChatMessagesByUserId(currentUserId: String, userId: String, callback: ((Boolean) -> Unit)? = null)
    
    /**
     * 清空所有聊天消息
     * @param callback 回调函数，在操作成功或失败时调用
     */
    fun clearAllChatMessages(callback: ((Boolean) -> Unit)? = null)

    /**
     * 清空当前用户所有聊天消息
     * @param callback 回调函数，在操作成功或失败时调用
     */
    fun clearCurrentUserAllChatMessages(callback: ((Boolean) -> Unit)? = null)

    /**
     * 根据messageId进行分页查询特定用户的聊天消息（向前查询更早的消息）
     * @param currentUserId 当前用户ID
     * @param userId 对话用户ID
     * @param messageId 起始消息ID，查询此消息之前的消息
     * @param limit 每页数量
     * @return 聊天消息实体列表
     */
    fun getChatMessagesBeforeMessageIdByUserId(currentUserId: String, userId: String, messageId: String, limit: Int,callback: (List<ChatMessageEntity>) -> Unit)


    /**
     * 获取特定用户的最新聊天消息（用于初始化分页）
     * @param currentUserId 当前用户ID
     * @param userId 对话用户ID
     * @param limit 每页数量
     * @return 聊天消息实体列表
     */
    fun getLatestChatMessagesByUserId(currentUserId: String, userId: String, limit: Int,callback: (List<ChatMessageEntity>) -> Unit)
    
    // </editor-folder>
}