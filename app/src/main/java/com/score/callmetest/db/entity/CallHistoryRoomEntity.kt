package com.score.callmetest.db.entity

import androidx.room.Entity
import androidx.room.PrimaryKey
import com.score.callmetest.CallStatus
import com.score.callmetest.entity.CallHistoryEntity
import java.util.UUID

/**
 * 通话历史Room实体类
 */
@Entity(tableName = "call_history")
data class CallHistoryRoomEntity(
    @PrimaryKey
    val id: String, // 主键ID
    val userId: String,
    val currentUserId: String,
    val userName: String,
    val avatar: String,
    val unitPrice: Int,
    val callType: String,
    val callDuration: Long = 0,
    val callEndTime: Long = System.currentTimeMillis(),
    val hasVideo: Boolean = true,
    val onlineStatus: String = CallStatus.OFFLINE,
    val isPinned: Boolean = false,
    val isBottomView: Boolean = false,
) {
    /**
     * 转换为对应的实体类
     */
    fun toEntity(): CallHistoryEntity {
        return CallHistoryEntity(
            id = id,
            userId = userId,
            currentUserId = currentUserId,
            userName = userName,
            avatar = avatar,
            unitPrice = unitPrice,
            callType = callType,
            callDuration = callDuration,
            callEndTime = callEndTime,
            hasVideo = hasVideo,
            onlineStatus = onlineStatus,
            isPinned = isPinned,
            isBottomView = isBottomView,
        )
    }
    
    companion object {
        /**
         * 从实体类转换为Room实体
         */
        fun fromEntity(entity: CallHistoryEntity): CallHistoryRoomEntity {
            return CallHistoryRoomEntity(
                id = entity.id,
                userId = entity.userId,
                currentUserId = entity.currentUserId,
                userName = entity.userName,
                avatar = entity.avatar,
                unitPrice = entity.unitPrice,
                callType = entity.callType,
                callDuration = entity.callDuration,
                callEndTime = entity.callEndTime,
                hasVideo = entity.hasVideo,
                onlineStatus = entity.onlineStatus,
                isPinned = entity.isPinned,
                isBottomView = entity.isBottomView,
            )
        }
    }
} 