package com.score.callmetest.db.repository

import com.score.callmetest.db.dao.MessageListDao
import com.score.callmetest.db.entity.MessageListRoomEntity
import com.score.callmetest.entity.MessageListEntity
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.withContext

/**
 * 消息列表仓库，负责处理消息列表相关的数据操作
 */
class MessageListRepository(private val messageListDao: MessageListDao) {
    /**
     * 插入一条消息列表项
     * @param messageList 消息列表项实体
     */
    suspend fun insertMessageList(messageList: MessageListEntity) = withContext(Dispatchers.IO) {
        messageListDao.insertMessageList(MessageListRoomEntity.fromEntity(messageList))
    }
    
    /**
     * 批量插入消息列表项
     * @param messageLists 消息列表项实体列表
     */
    suspend fun insertMessageLists(messageLists: List<MessageListEntity>) = withContext(Dispatchers.IO) {
        messageListDao.insertMessageLists(messageLists.map { MessageListRoomEntity.fromEntity(it) })
    }
    
    /**
     * 更新一条消息列表项
     * @param messageList 消息列表项实体
     */
    suspend fun updateMessageList(messageList: MessageListEntity) = withContext(Dispatchers.IO) {
        messageListDao.updateMessageList(MessageListRoomEntity.fromEntity(messageList))
    }
    
    /**
     * 删除一条消息列表项
     * @param messageList 消息列表项实体
     */
    suspend fun deleteMessageList(messageList: MessageListEntity) = withContext(Dispatchers.IO) {
        messageListDao.deleteMessageList(MessageListRoomEntity.fromEntity(messageList))
    }
    
    /**
     * 根据用户ID删除一条消息列表项
     * @param userId 用户ID
     */
    suspend fun deleteMessageListById(userId: String) = withContext(Dispatchers.IO) {
        messageListDao.deleteMessageListById(userId)
    }
    
    /**
     * 获取所有消息列表项，按是否置顶和时间排序
     * @return 消息列表项流
     */
    fun getAllMessageLists(): Flow<List<MessageListEntity>> {
        return messageListDao.getAllMessageLists().map { roomEntities ->
            roomEntities.map { it.toEntity() }
        }
    }

    /**
     * 获取当前用户的所有消息列表项，按是否置顶和时间排序
     * @param currentUserId 当前用户ID
     * @return 消息列表项流
     */
    fun getAllMessageListsByCurrentUserId(currentUserId: String): Flow<List<MessageListEntity>> {
        return messageListDao.getAllMessageListsByCurrentUserId(currentUserId).map { roomEntities ->
            roomEntities.map { it.toEntity() }
        }
    }

    /**
     * 根据用户ID获取一条消息列表项
     * @param userId 用户ID
     * @return 消息列表项实体
     */
    suspend fun getMessageListById(userId: String,currentUserId: String): MessageListEntity? = withContext(Dispatchers.IO) {
        if(currentUserId.isBlank()) {
            messageListDao.getMessageListById(userId)?.toEntity()
        }else{
            messageListDao.getMessageListById(userId,currentUserId)?.toEntity()
        }
    }
    
    /**
     * 更新未读消息数
     * @param userId 用户ID
     * @param unreadCount 未读消息数
     * @param currentUserId
     */
    suspend fun updateUnreadCount(userId: String, unreadCount: Int,currentUserId: String) = withContext(Dispatchers.IO) {
        if(currentUserId.isBlank()){
            messageListDao.updateUnreadCount(userId, unreadCount)
        }else{
            messageListDao.updateUnreadCount(userId, unreadCount,currentUserId)
        }
    }

    /**
     * 更新是否置顶
     * @param userId 用户ID
     * @param currentUserId
     * @param isPinned 是否置顶
     */
    suspend fun updatePinStatus(userId: String,currentUserId: String, isPinned: Boolean) = withContext(Dispatchers.IO) {
        if(currentUserId.isBlank()){
            messageListDao.updatePinStatus(userId,isPinned)
        }else{
            messageListDao.updatePinStatus(userId,currentUserId,isPinned)
        }
    }

    /**
     * 更新状态
     * @param userId 用户ID
     * @param currentUserId
     * @param status 状态
     */
    suspend fun updateStatus(userId: String,currentUserId: String, status: String) = withContext(Dispatchers.IO) {
        if(currentUserId.isBlank()){
            messageListDao.updateStatus(userId,status)
        }else{
            messageListDao.updateStatus(userId,currentUserId,status)
        }
    }
    
    /**
     * 清空所有消息列表
     */
    suspend fun clearAllMessageLists() = withContext(Dispatchers.IO) {
        messageListDao.clearAllMessageLists()
    }

    /**
     * 清空当前用户的所有消息列表
     * @param currentUserId 当前用户ID
     */
    suspend fun clearAllMessageListsByCurrentUserId(currentUserId: String) = withContext(Dispatchers.IO) {
        messageListDao.clearAllMessageListsByCurrentUserId(currentUserId)
    }
} 