package com.score.callmetest.db.converter

import androidx.room.TypeConverter
import com.score.callmetest.entity.MessageStatus

/**
 * 消息状态转换器
 */
class MessageStatusConverter {
    @TypeConverter
    fun fromMessageStatus(value: MessageStatus): String {
        return value.name
    }
    
    @TypeConverter
    fun toMessageStatus(value: String): MessageStatus {
        return MessageStatus.valueOf(value)
    }
}