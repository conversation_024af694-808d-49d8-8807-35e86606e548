package com.score.callmetest.db.entity

import androidx.room.ColumnInfo
import androidx.room.DeleteColumn
import androidx.room.Entity
import androidx.room.Ignore
import androidx.room.PrimaryKey
import androidx.room.TypeConverters
import com.score.callmetest.CallStatus
import com.score.callmetest.entity.MessageListEntity
import com.score.callmetest.entity.MessageType

/**
 * 消息列表Room实体类
 */
@Entity(tableName = "message_list")
data class MessageListRoomEntity(
    @PrimaryKey
    val userId: String,
    val currentUserId: String,
    val userName: String,
    val gender: Int,
    val unitPrice: Int,
    val avatar: String,
    val avatarThumbUrl: String,
    val lastMessage: String,
    val lastMessageType: MessageType,
    val timestamp: String,
    val timeInMillis: Long = System.currentTimeMillis(),
    val unreadCount: Int = 0,
    val onlineStatus: String = CallStatus.OFFLINE,
    val isPinned: Boolean = false,
) {

    @Ignore
    var isBottomView: Boolean = false

    /**
     * 转换为对应的实体类
     */
    fun toEntity(): MessageListEntity {
        return MessageListEntity(
            userId = userId,
            currentUserId = currentUserId,
            userName = userName,
            gender = gender,
            unitPrice = unitPrice,
            avatar = avatar,
            avatarThumbUrl = avatarThumbUrl,
            lastMessage = lastMessage,
            lastMessageType = lastMessageType,
            timestamp = timestamp,
            timeInMillis = timeInMillis,
            unreadCount = unreadCount,
            onlineStatus = onlineStatus,
            isPinned = isPinned
        )
    }
    
    companion object {
        /**
         * 从实体类转换为Room实体
         */
        fun fromEntity(entity: MessageListEntity): MessageListRoomEntity {
            return MessageListRoomEntity(
                userId = entity.userId,
                currentUserId = entity.currentUserId,
                userName = entity.userName,
                gender = entity.gender,
                unitPrice = entity.unitPrice,
                avatar = entity.avatar,
                avatarThumbUrl = entity.avatarThumbUrl,
                lastMessage = entity.lastMessage,
                lastMessageType = entity.lastMessageType,
                timestamp = entity.timestamp,
                timeInMillis = entity.timeInMillis,
                unreadCount = entity.unreadCount,
                onlineStatus = entity.onlineStatus,
                isPinned = entity.isPinned
            )
        }
    }
}