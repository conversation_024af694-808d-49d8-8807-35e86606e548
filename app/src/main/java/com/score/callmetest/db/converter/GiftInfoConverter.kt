package com.score.callmetest.db.converter

import androidx.room.TypeConverter
import com.score.callmetest.network.GiftInfo
import kotlinx.serialization.decodeFromString
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json

/**
 * GiftInfo转换器
 */
class GiftInfoConverter {
    @TypeConverter
    fun fromGiftInfo(giftInfo: GiftInfo?): String? {
        return giftInfo?.let {
            try {
                Json.encodeToString(it)
            } catch (e: Exception) {
                null
            }
        }
    }

    @TypeConverter
    fun toGiftInfo(value: String?): GiftInfo? {
        return value?.let {
            try {
                Json.decodeFromString<GiftInfo>(it)
            } catch (e: Exception) {
                null
            }
        }
    }
}