package com.score.callmetest.db.dao

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import com.score.callmetest.db.entity.MessageListRoomEntity
import com.score.callmetest.entity.MessageStatus
import kotlinx.coroutines.flow.Flow

/**
 * 消息列表DAO接口
 */
@Dao
interface MessageListDao {
    /**
     * 插入一条消息列表项
     * @param messageList 消息列表项实体
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertMessageList(messageList: MessageListRoomEntity)
    
    /**
     * 批量插入消息列表项
     * @param messageLists 消息列表项实体列表
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertMessageLists(messageLists: List<MessageListRoomEntity>)
    
    /**
     * 更新一条消息列表项
     * @param messageList 消息列表项实体
     */
    @Update
    suspend fun updateMessageList(messageList: MessageListRoomEntity)
    
    /**
     * 删除一条消息列表项
     * @param messageList 消息列表项实体
     */
    @Delete
    suspend fun deleteMessageList(messageList: MessageListRoomEntity)
    
    /**
     * 根据用户ID删除一条消息列表项
     * @param userId 用户ID
     */
    @Query("DELETE FROM message_list WHERE userId = :userId")
    suspend fun deleteMessageListById(userId: String)
    
    /**
     * 获取所有消息列表项，按是否置顶和时间排序
     * @return 消息列表项流
     */
    @Query("SELECT * FROM message_list ORDER BY isPinned DESC, timeInMillis DESC")
    fun getAllMessageLists(): Flow<List<MessageListRoomEntity>>

    /**
     * 获取当前用户的所有消息列表项，按是否置顶和时间排序
     * @param currentUserId 当前用户ID
     * @return 消息列表项流
     */
    @Query("SELECT * FROM message_list WHERE currentUserId = :currentUserId ORDER BY isPinned DESC, timeInMillis DESC")
    fun getAllMessageListsByCurrentUserId(currentUserId: String): Flow<List<MessageListRoomEntity>>

    /**
     * 根据用户ID获取一条消息列表项
     * @param userId 用户ID
     * @param currentUserId
     * @return 消息列表项实体
     */
    @Query("SELECT * FROM message_list WHERE userId = :userId  AND currentUserId = :currentUserId")
    suspend fun getMessageListById(userId: String,currentUserId: String): MessageListRoomEntity?

    /**
     * 根据用户ID获取一条消息列表项
     * @param userId 用户ID
     * @return 消息列表项实体
     */
    @Query("SELECT * FROM message_list WHERE userId = :userId")
    suspend fun getMessageListById(userId: String): MessageListRoomEntity?

    /**
     * 更新未读消息数
     * @param userId 用户ID
     * @param unreadCount 未读消息数
     */
    @Query("UPDATE message_list SET unreadCount = :unreadCount WHERE userId = :userId")
    suspend fun updateUnreadCount(userId: String, unreadCount: Int)

    /**
     * 更新未读消息数
     * @param userId 用户ID
     * @param unreadCount 未读消息数
     * @param currentUserId
     */
    @Query("UPDATE message_list SET unreadCount = :unreadCount WHERE userId = :userId AND currentUserId = :currentUserId")
    suspend fun updateUnreadCount(userId: String, unreadCount: Int,currentUserId: String)

    /**
     * 更新是否置顶
     * @param userId 用户ID
     * @param currentUserId 当前用户id
     * @param isPinned 是否置顶
     */
    @Query("UPDATE message_list SET isPinned = :isPinned WHERE userId = :userId AND currentUserId = :currentUserId")
    suspend fun updatePinStatus(userId: String, currentUserId: String,isPinned: Boolean)

    /**
     * 更新是否置顶
     * @param userId 用户ID
     * @param isPinned 是否置顶
     */
    @Query("UPDATE message_list SET isPinned = :isPinned WHERE userId = :userId ")
    suspend fun updatePinStatus(userId: String, isPinned: Boolean)


    /**
     * 更新状态
     * @param userId 用户ID
     * @param currentUserId 当前用户id
     * @param status 状态
     */
    @Query("UPDATE message_list SET onlineStatus = :status WHERE userId = :userId AND currentUserId = :currentUserId")
    suspend fun updateStatus(userId: String, currentUserId: String,status: String)

    /**
     * 更新状态
     * @param userId 用户ID
     * @param status 状态
     */
    @Query("UPDATE message_list SET onlineStatus = :status WHERE userId = :userId ")
    suspend fun updateStatus(userId: String, status: String)


    /**
     * 清空所有消息列表
     */
    @Query("DELETE FROM message_list")
    suspend fun clearAllMessageLists()

    /**
     * 清空当前用户的所有消息列表
     * @param currentUserId 当前用户ID
     */
    @Query("DELETE FROM message_list WHERE currentUserId = :currentUserId")
    suspend fun clearAllMessageListsByCurrentUserId(currentUserId: String)
} 