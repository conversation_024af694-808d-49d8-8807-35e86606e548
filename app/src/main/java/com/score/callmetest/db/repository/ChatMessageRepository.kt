package com.score.callmetest.db.repository

import com.score.callmetest.db.dao.ChatMessageDao
import com.score.callmetest.db.entity.ChatMessageRoomEntity
import com.score.callmetest.entity.ChatMessageEntity
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.withContext

/**
 * 聊天消息仓库，负责处理聊天消息相关的数据操作
 */
class ChatMessageRepository(private val chatMessageDao: ChatMessageDao) {
    /**
     * 插入一条聊天消息
     * @param chatMessage 聊天消息实体
     */
    suspend fun insertChatMessage(chatMessage: ChatMessageEntity) = withContext(Dispatchers.IO) {
        chatMessageDao.insertChatMessage(ChatMessageRoomEntity.fromEntity(chatMessage))
    }
    
    /**
     * 批量插入聊天消息
     * @param chatMessages 聊天消息实体列表
     */
    suspend fun insertChatMessages(chatMessages: List<ChatMessageEntity>) = withContext(Dispatchers.IO) {
        chatMessageDao.insertChatMessages(chatMessages.map { ChatMessageRoomEntity.fromEntity(it) })
    }
    
    /**
     * 更新一条聊天消息
     * @param chatMessage 聊天消息实体
     */
    suspend fun updateChatMessage(chatMessage: ChatMessageEntity) = withContext(Dispatchers.IO) {
        chatMessageDao.updateChatMessage(ChatMessageRoomEntity.fromEntity(chatMessage))
    }
    
    /**
     * 删除一条聊天消息
     * @param chatMessage 聊天消息实体
     */
    suspend fun deleteChatMessage(chatMessage: ChatMessageEntity) = withContext(Dispatchers.IO) {
        chatMessageDao.deleteChatMessage(ChatMessageRoomEntity.fromEntity(chatMessage))
    }
    
    /**
     * 根据消息ID删除一条聊天消息
     * @param messageId 消息ID
     */
    suspend fun deleteChatMessageById(messageId: String) = withContext(Dispatchers.IO) {
        chatMessageDao.deleteChatMessageById(messageId)
    }
    
    /**
     * 获取所有聊天消息
     * @return 聊天消息流
     */
    suspend fun getAllChatMessages(): List<ChatMessageEntity> = withContext(Dispatchers.IO) {
        chatMessageDao.getAllChatMessages().map { it.toEntity() }
    }

    /**
     * 获取特定用户的所有聊天消息，按时间排序
     * @param currentUserId 当前用户ID
     * @param userId 用户ID
     * @return 聊天消息流
     */
    suspend fun getChatMessagesByUserId(currentUserId: String, userId: String): List<ChatMessageEntity> = withContext(Dispatchers.IO) {
        chatMessageDao.getChatMessagesByUserId(currentUserId, userId).map {it.toEntity()}
    }

    /**
     * 根据消息ID获取一条聊天消息
     * @param messageId 消息ID
     * @return 聊天消息实体
     */
    suspend fun getChatMessageById(messageId: String): ChatMessageEntity? = withContext(Dispatchers.IO) {
        chatMessageDao.getChatMessageById(messageId)?.toEntity()
    }

    /**
     * 根据融云消息ID更新localPath
     * @param rcMsgId rc消息ID
     * @param localPath 下载到本地的文件路径
     */
    suspend fun updateLocalPathByRcId(rcMsgId: String,localPath: String) = withContext(Dispatchers.IO) {
        chatMessageDao.updateLocalPathByRcId(rcMsgId,localPath)
    }

    /**
     * 更新状态
     * @param messageId 消息ID
     * @param status 状态
     */
    suspend fun updateSendStatus(messageId: String, status: String) = withContext(Dispatchers.IO) {
        chatMessageDao.updateSendStatus(messageId,status)
    }

    /**
     * 清空所有聊天消息
     */
    suspend fun clearAllChatMessages() = withContext(Dispatchers.IO) {
        chatMessageDao.clearAllChatMessages()
    }

    /**
     * 清空当前用户所有聊天消息
     */
    suspend fun clearCurrentUserAllChatMessages(currentUserId: String) = withContext(Dispatchers.IO) {
        chatMessageDao.clearCurrentUserAllChatMessages(currentUserId)
    }

    /**
     * 清空当前用户与特定用户之间的所有聊天消息
     * @param currentUserId 当前用户ID
     * @param userId 用户ID
     */
    suspend fun clearChatMessagesByUserId(currentUserId: String, userId: String) = withContext(Dispatchers.IO) {
        chatMessageDao.clearChatMessagesByUserId(currentUserId, userId)
    }

    // ==================== 分页查询接口 ====================


    /**
     * 根据messageId进行分页查询特定用户的聊天消息（向前查询更早的消息）
     * @param currentUserId 当前用户ID
     * @param userId 对话用户ID
     * @param messageId 起始消息ID，查询此消息之前的消息
     * @param limit 每页数量
     * @return 聊天消息实体列表
     */
    suspend fun getChatMessagesBeforeMessageIdByUserId(currentUserId: String, userId: String, messageId: String, limit: Int): List<ChatMessageEntity> = withContext(Dispatchers.IO) {
        chatMessageDao.getChatMessagesBeforeMessageIdByUserId(currentUserId, userId, messageId, limit).map { it.toEntity() }
    }


    /**
     * 获取特定用户的最新聊天消息（用于初始化分页）
     * @param currentUserId 当前用户ID
     * @param userId 对话用户ID
     * @param limit 每页数量
     * @return 聊天消息实体列表
     */
    suspend fun getLatestChatMessagesByUserId(currentUserId: String, userId: String, limit: Int): List<ChatMessageEntity> = withContext(Dispatchers.IO) {
        chatMessageDao.getLatestChatMessagesByUserId(currentUserId, userId, limit).map { it.toEntity() }
    }

}