package com.score.callmetest.entity

import com.score.callmetest.network.UserInfo

/**
 * 聊天列表项的统一数据类型
 * 用于在RecyclerView中统一处理主播card和消息
 */
sealed class ChatListItem {
    
    /**
     * 获取唯一标识符，用于DiffUtil比较
     */
    abstract fun getItemId(): String
    
    /**
     * 主播card项
     */
    data class BroadcasterCard(
        val userInfo: UserInfo
    ) : ChatListItem() {

        override fun getItemId(): String = "broadcaster_card_${userInfo.userId}"

        companion object {
            const val ITEM_TYPE = "broadcaster_card"
        }
    }
    
    /**
     * 消息项
     */
    data class Message(
        val messageEntity: ChatMessageEntity
    ) : ChatListItem() {

        override fun getItemId(): String = messageEntity.messageId

        companion object {
            const val ITEM_TYPE = "message"
        }
    }

    /**
     * 空状态项
     */
    object EmptyState : ChatListItem() {

        override fun getItemId(): String = "empty_state"

        const val ITEM_TYPE = "empty_state"
    }
}
