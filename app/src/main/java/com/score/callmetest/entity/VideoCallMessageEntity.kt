package com.score.callmetest.entity

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * 视频通话消息实体类
 * 用于视频通话过程中的聊天消息展示
 * 
 * @property messageId 消息唯一ID
 * @property senderId 发送者ID
 * @property senderName 发送者名称
 * @property senderAvatar 发送者头像URL
 * @property receiverId 接收者ID
 * @property content 消息内容
 * @property timestamp 时间戳
 * @property isCurrentUser 是否是当前用户发送的消息
 * @property translateContent 翻译后的内容（可选）
 * @property showTranslation 是否显示翻译内容
 */
@Parcelize
data class VideoCallMessageEntity(
    val messageId: String,
    val senderId: String,
    val senderName: String,
    val senderAvatar: String,
    val receiverId: String,
    val content: String,
    val timestamp: Long = System.currentTimeMillis(),
    val isCurrentUser: Boolean,
    var translateContent: String? = null,
    var showTranslation: Boolean = false
) : Parcelable {
    
    /**
     * 获取显示的消息内容
     * 格式：发送者名称: 消息内容
     */
    fun getDisplayContent(): String {
        return "$senderName: $content"
    }
    
    /**
     * 获取显示的翻译内容
     * 格式：发送者名称: 翻译内容
     */
    fun getDisplayTranslateContent(): String? {
        return translateContent?.let { "$senderName: $it" }
    }
    
    /**
     * 是否需要显示底部内容（分割线和翻译）
     * 只有对方发送的消息且有翻译内容时才显示
     */
    fun shouldShowBottomContent(): Boolean {
        return !isCurrentUser && showTranslation && !translateContent.isNullOrEmpty()
    }
    
    companion object {
        /**
         * 创建发送的消息
         */
        fun createSentMessage(
            messageId: String,
            senderId: String,
            senderName: String,
            senderAvatar: String,
            receiverId: String,
            content: String
        ): VideoCallMessageEntity {
            return VideoCallMessageEntity(
                messageId = messageId,
                senderId = senderId,
                senderName = senderName,
                senderAvatar = senderAvatar,
                receiverId = receiverId,
                content = content,
                isCurrentUser = true
            )
        }
        
        /**
         * 创建接收的消息
         */
        fun createReceivedMessage(
            messageId: String,
            senderId: String,
            senderName: String,
            senderAvatar: String,
            receiverId: String,
            content: String,
            translateContent: String? = null
        ): VideoCallMessageEntity {
            return VideoCallMessageEntity(
                messageId = messageId,
                senderId = senderId,
                senderName = senderName,
                senderAvatar = senderAvatar,
                receiverId = receiverId,
                content = content,
                isCurrentUser = false,
                translateContent = translateContent,
                showTranslation = !translateContent.isNullOrEmpty()
            )
        }
    }
}
