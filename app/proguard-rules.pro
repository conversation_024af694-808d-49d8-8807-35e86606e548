# ===============================================================================
# Callme 项目混淆配置文件
# ===============================================================================

# 保留行号信息，便于调试崩溃日志
-keepattributes SourceFile,LineNumberTable
-renamesourcefileattribute SourceFile

# 保留注解信息
-keepattributes *Annotation*

# 保留泛型信息
-keepattributes Signature

# 保留异常信息
-keepattributes Exceptions

# 保留内部类信息
-keepattributes InnerClasses

# ===============================================================================
# Kotlin 相关配置
# ===============================================================================
# 保留 Kotlin 元数据
-keepattributes *Annotation*, InnerClasses
-dontnote kotlinx.serialization.AnnotationsKt

# 保留 Kotlin 协程相关
-keepnames class kotlinx.coroutines.internal.MainDispatcherFactory {}
-keepnames class kotlinx.coroutines.CoroutineExceptionHandler {}
-keepclassmembers class kotlinx.coroutines.** {
    volatile <fields>;
}

# 保留 Kotlin 反射
-keep class kotlin.reflect.jvm.internal.** { *; }
-keep class kotlin.Metadata { *; }

# ===============================================================================
# Android 系统相关
# ===============================================================================
# 保留四大组件
-keep public class * extends android.app.Activity
-keep public class * extends android.app.Application
-keep public class * extends android.app.Service
-keep public class * extends android.content.BroadcastReceiver
-keep public class * extends android.content.ContentProvider
-keep public class * extends android.app.backup.BackupAgentHelper
-keep public class * extends android.preference.Preference
-keep public class * extends android.view.View

# 保持自定义控件类不被混淆
-keep public class * extends android.view.View{
    *** get*();
    void set*(***);
    public <init>(android.content.Context);
    public <init>(android.content.Context, android.util.AttributeSet);
    public <init>(android.content.Context, android.util.AttributeSet, int);
}
-keepclasseswithmembers class * {
    public <init>(android.content.Context, android.util.AttributeSet);
    public <init>(android.content.Context, android.util.AttributeSet, int);
}

# 保留 Parcelable
-keep class * implements android.os.Parcelable {
    public static final android.os.Parcelable$Creator *;
}

# 保留 Serializable
-keepnames class * implements java.io.Serializable
-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    !static !transient <fields>;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}

-keep class **.R$* { #R类不混淆
 *;
}

-keepclassmembers class * {
    void *(**On*Event);
}

# ===============================================================================
# 网络请求相关 - Retrofit & OkHttp
# ===============================================================================
# Retrofit
-keepattributes Signature, InnerClasses, EnclosingMethod
-keepattributes RuntimeVisibleAnnotations, RuntimeVisibleParameterAnnotations
-keepattributes AnnotationDefault

-keepclassmembers,allowshrinking,allowobfuscation interface * {
    @retrofit2.http.* <methods>;
}

-dontwarn org.codehaus.mojo.animal_sniffer.IgnoreJRERequirement
-dontwarn javax.annotation.**
-dontwarn kotlin.Unit
-dontwarn retrofit2.KotlinExtensions
-dontwarn retrofit2.KotlinExtensions$*

-if interface * { @retrofit2.http.* <methods>; }
-keep,allowobfuscation interface <1>

-if interface * { @retrofit2.http.* <methods>; }
-keep,allowobfuscation interface * extends <1>

-keep,allowobfuscation,allowshrinking class kotlin.coroutines.Continuation

-if interface * { @retrofit2.http.* public *** *(...); }
-keep,allowoptimization,allowshrinking,allowobfuscation class <3>

-keep,allowobfuscation,allowshrinking interface retrofit2.Call
-keep,allowobfuscation,allowshrinking class retrofit2.Response

# OkHttp
# R8自动加了混淆在aar里面
-dontwarn javax.annotation.**
-dontwarn javax.inject.**
-dontwarn sun.misc.Unsafe
-keepnames class okhttp3.internal.publicsuffix.PublicSuffixDatabase
-dontwarn org.codehaus.mojo.animal_sniffer.*
-dontwarn okhttp3.internal.platform.ConscryptPlatform
-dontwarn org.conscrypt.ConscryptHostnameVerifier
-dontwarn okio.**

# ===============================================================================
# JSON 序列化相关
# ===============================================================================
# Gson
-keepattributes Signature
-keepattributes *Annotation*
-dontwarn sun.misc.**
-keep class com.google.gson.** { *; }
-keep class * implements com.google.gson.TypeAdapter
-keep class * implements com.google.gson.TypeAdapterFactory
-keep class * implements com.google.gson.JsonSerializer
-keep class * implements com.google.gson.JsonDeserializer

# Kotlinx Serialization
-keepattributes *Annotation*, InnerClasses
-dontnote kotlinx.serialization.AnnotationsKt
-dontnote kotlinx.serialization.SerializationKt

-keep,includedescriptorclasses class com.score.callmetest.**$$serializer { *; }
-keepclassmembers class com.score.callmetest.** {
    *** Companion;
}
-keepclasseswithmembers class com.score.callmetest.** {
    kotlinx.serialization.KSerializer serializer(...);
}

# ===============================================================================
# 第三方 SDK 配置
# ===============================================================================
# Agora RTC SDK
-keep class io.agora.**{*;}
-dontwarn javax.**
-dontwarn com.google.devtools.build.android.**
-keep class com.herewhite.** { *; }
-keep class cn.shengwang.** {*;}

# Adjust SDK
-keep class com.adjust.sdk.** { *; }
-keep class com.google.android.gms.common.ConnectionResult {
    int SUCCESS;
}
-keep class com.google.android.gms.ads.identifier.AdvertisingIdClient {
    com.google.android.gms.ads.identifier.AdvertisingIdClient$Info
    getAdvertisingIdInfo(android.content.Context);
}
-keep class com.google.android.gms.ads.identifier.AdvertisingIdClient$Info {
    java.lang.String getId();
    boolean isLimitAdTrackingEnabled();
}
-keep public class com.android.installreferrer.** { *; }

# 融云 SDK
-keep class io.rong.** {*;}
-keep class cn.rongcloud.** {*;}
-keep class * implements io.rong.imlib.model.MessageContent {*;}
-dontwarn io.rong.push.**
-dontnote com.xiaomi.**
-dontnote com.google.android.gms.gcm.**
-dontnote io.rong.**
## 当代码中有继承 PushMessageReceiver 的子类时，需 keep 所创建的子类广播
#-keep class io.rong.app.DemoNotificationReceiver {*;}


# Google Play Services
-keep class com.google.android.gms.** { *; }
-dontwarn com.google.android.gms.**

# Google Billing
-keep class com.android.billingclient.** { *; }
-dontwarn com.android.billingclient.**

# ===============================================================================
# 图片加载相关 - Glide
# ===============================================================================
# Glide说在R8中aar已经加上了相应规则，无需再加
#-keep public class * implements com.bumptech.glide.module.GlideModule
#-keep class * extends com.bumptech.glide.module.AppGlideModule {
#    <init>(...);
#}
#-keep public enum com.bumptech.glide.load.ImageHeaderParser$** {
#    **[] $VALUES;
#    public *;
#}
#-keep class com.bumptech.glide.load.data.ParcelFileDescriptorRewinder$InternalRewinder {
#    *** rewind();
#}

# ===============================================================================
# 数据库相关 - Room
# ===============================================================================
#-keep class * extends androidx.room.RoomDatabase
#-keep @androidx.room.Entity class *
#-dontwarn androidx.room.paging.**
-keep class * extends androidx.room.RoomDatabase { <init>(); }

# ===============================================================================
# 其他第三方库
# ===============================================================================
# UCrop
-dontwarn com.yalantis.ucrop**
-keep class com.yalantis.ucrop** { *; }
-keep interface com.yalantis.ucrop** { *; }


# PhotoView
-keep class com.github.chrisbanes.photoview.** { *; }

# SVGA Player
-keep class com.squareup.wire.** { *; }
-keep class com.opensource.svgaplayer.proto.** { *; }
#-keep class com.opensource.svgaplayer.** { *; }
#-dontwarn com.opensource.svgaplayer.**

# Socket.IO
-keep class io.socket.** { *; }
-dontwarn io.socket.**

# FlexboxLayout
-keep class com.google.android.flexbox.** { *; }

# CircleImageView
#-keep class de.hdodenhof.circleimageview.** { *; }

# Timber
#-keep class timber.log.** { *; }

# Media3 ExoPlayer
#-keep class androidx.media3.** { *; }
#-dontwarn androidx.media3.**

# ===============================================================================
# WebView 相关
# ===============================================================================
-keepclassmembers class fqcn.of.javascript.interface.for.webview {
    public *;
}
-keepclassmembers class * extends android.webkit.WebViewClient {
    public void *(android.webkit.WebView, java.lang.String, android.graphics.Bitmap);
    public boolean *(android.webkit.WebView, java.lang.String);
}
-keepclassmembers class * extends android.webkit.WebChromeClient {
    public void *(android.webkit.WebView, java.lang.String);
}

# ===============================================================================
# 项目特定配置
# ===============================================================================
# 保留项目中的数据模型类（根据实际情况调整包名）
#-keep class com.score.callmetest.model.** { *; }
#-keep class com.score.callmetest.bean.** { *; }
-keep class com.score.callmetest.entity.** { *; }
#-keep class com.score.callmetest.data.** { *; }

# 保留网络相关类 - 包含序列化注解的类必须保留
#-keep class com.score.callmetest.network.** { *; }
#-keep interface com.score.callmetest.network.** { *; }

# 特别保留带有 @Serializable 注解的数据类
-keep @kotlinx.serialization.Serializable class * { *; }

# 保留带有 @Parcelize 注解的数据类
-keep @kotlinx.parcelize.Parcelize class * { *; }

# 保留 Room 数据库相关类
#-keep class com.score.callmetest.db.** { *; }
-keep @androidx.room.Entity class * { *; }
-keep @androidx.room.Dao class * { *; }
-keep @androidx.room.Database class * { *; }

# 保留 Application 类
#-keep class com.score.callmetest.CallmeApplication { *; }

# 保留自定义 View
#-keep class com.score.callmetest.view.** { *; }
#-keep class com.score.callmetest.widget.** { *; }

# 保留 ViewModel 类
#-keep class com.score.callmetest.viewmodel.** { *; }

# 保留 Fragment 和 Activity 类
#-keep class com.score.callmetest.ui.** { *; }
#-keep class com.score.callmetest.activity.** { *; }
#-keep class com.score.callmetest.fragment.** { *; }

# 保留工具类
#-keep class com.score.callmetest.util.** { *; }

# 保留常量类
#-keep class com.score.callmetest.constant.** { *; }
#-keep class com.score.callmetest.Constant { *; }

# 保留枚举类
#-keep enum com.score.callmetest.** { *; }

# ===============================================================================
# 调试和优化配置
# ===============================================================================
# 移除日志输出（可选，根据需要启用）
 -assumenosideeffects class android.util.Log {
     public static boolean isLoggable(java.lang.String, int);
     public static int v(...);
     public static int i(...);
     public static int w(...);
     public static int d(...);
     public static int e(...);
 }

# 移除 Timber 日志（可选，根据需要启用）
# -assumenosideeffects class timber.log.Timber* {
#     public static *** v(...);
#     public static *** d(...);
#     public static *** i(...);
#     public static *** w(...);
#     public static *** e(...);
# }

# 优化配置
-optimizations !code/simplification/arithmetic,!code/simplification/cast,!field/*,!class/merging/* # 混淆时所采用的算法
-optimizationpasses 5
-dontskipnonpubliclibraryclassmembers # 是否使用大小写混合
-ignorewarnings #忽略警告日志
-printmapping proguardMapping.txt #混淆对应
-allowaccessmodification
-dontpreverify

# 不混淆枚举
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

# ===============================================================================
# 警告抑制
# ===============================================================================
-dontwarn java.lang.invoke.**
-dontwarn **$$serializer
-dontwarn java.lang.ClassValue
-dontwarn org.bouncycastle.**
-dontwarn org.conscrypt.**
-dontwarn org.openjsse.**

