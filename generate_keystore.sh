#!/bin/bash

# 生成Android测试签名文件脚本
# 使用方法: ./generate_keystore.sh

echo "正在生成Android测试签名文件..."

# 检查是否已存在签名文件
if [ -f "app/linkduo-test.keystore" ]; then
    echo "警告: 签名文件 app/linkduo-test.keystore 已存在"
    read -p "是否要覆盖现有文件? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "操作已取消"
        exit 1
    fi
    rm app/linkduo-test.keystore
fi

# 生成签名文件
keytool -genkey -v \
    -keystore app/linkduo-test.keystore \
    -alias linkduo-test \
    -keyalg RSA \
    -keysize 2048 \
    -validity 10000 \
    -storepass test123 \
    -keypass test123 \
    -dname "CN=Test,OU=Test,O=Test,L=Test,ST=Test,C=CN"

if [ $? -eq 0 ]; then
    echo "✅ 签名文件生成成功: app/linkduo-test.keystore"
    echo ""
    echo "签名信息:"
    echo "  文件路径: app/linkduo-test.keystore"
    echo "  别名: linkduo-test"
    echo "  密码: test123"
    echo "  有效期: 10000天"
    echo ""
    echo "⚠️  注意: 这是测试签名文件，请勿用于生产环境!"
else
    echo "❌ 签名文件生成失败"
    exit 1
fi
