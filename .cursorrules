{"rules": {"code_style": {"naming": {"variables": "camelCase", "member_variables": "mCamelCase", "static_variables": "sCamelCase", "constants": "UPPER_CASE", "private_constants": "_camelCase", "classes": "PascalCase", "interfaces": "IPascalCase", "enums": "PascalCase", "resource_files": "kebab-case", "packages": "kebab-case"}, "avoid_abbreviations": true}, "architecture": {"pattern": "MVVM", "principles": ["composition_over_inheritance", "small_focused_components", "dry_principle", "solid_principles", "separation_of_concerns"]}, "project_structure": {"base_package": "com.score.callmetest", "directories": {"entity": "entity_module", "manager": "global_managers", "support": "support_module", "network": "network_related", "ui": {"description": "ui_modules", "subdirectories": {"base": "base_components", "broadcaster": "broadcaster_module", "chat": "chat_module", "home": "home_module", "login": "login_module", "main": "main_module", "match": "match_module", "message": "message_module", "mine": "mine_module", "preview": "preview_module", "profile": "profile_module", "videocall": "videocall_module", "web": "webview_module", "widget": "widgets_module"}}, "util": "global_utilities"}}, "tech_stack": {"frameworks": {"architecture": "mvvm", "agp_version": "8.10.1", "min_sdk_version": 21, "target_sdk_version": 34}, "languages": {"kotlin_version": "2.1.20", "gradle_version": "8.10.1"}}, "logging": {"tag_prefix": "dsc--", "guidelines": ["所有点击事件必须记录日志", "所有复杂逻辑必须记录日志", "所有网络请求必须记录请求和响应日志", "所有错误必须记录日志"], "log_tool": "timber", "log_levels": {"debug": "d", "info": "i", "warning": "w", "error": "e"}, "event_types": {"click": {"description": "用户点击事件", "format": "按钮名称 + 点击"}, "navigation": {"description": "页面跳转", "format": "来源页面 + 跳转到 + 目标页面 + [可选参数]"}, "data_load": {"description": "数据加载", "format": "加载 + 数据类型 + 完成/失败 + [数量/原因]"}, "user_action": {"description": "用户操作", "format": "操作类型 + 操作对象 + [可选参数]"}, "error": {"description": "错误信息", "format": "错误类型 + 错误信息 + [可选:错误代码]"}}}}, "formatting": {"indent_size": 4, "line_width": 100, "use_spaces": true}}