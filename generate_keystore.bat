@echo off
chcp 65001 >nul
echo 正在生成Android测试签名文件...

REM 检查是否已存在签名文件
if exist "app\callme-test.keystore" (
    echo 警告: 签名文件 app\callme-test.keystore 已存在
    set /p choice="是否要覆盖现有文件? (y/N): "
    if /i not "%choice%"=="y" (
        echo 操作已取消
        pause
        exit /b 1
    )
    del app\callme-test.keystore
)

REM 生成签名文件
keytool -genkey -v ^
    -keystore app\callme-test.keystore ^
    -alias callme-test ^
    -keyalg RSA ^
    -keysize 2048 ^
    -validity 10000 ^
    -storepass test123 ^
    -keypass test123 ^
    -dname "CN=Test,OU=Test,O=Test,L=Test,ST=Test,C=CN"

if %errorlevel% equ 0 (
    echo.
    echo ✅ 签名文件生成成功: app\callme-test.keystore
    echo.
    echo 签名信息:
    echo   文件路径: app\callme-test.keystore
    echo   别名: callme-test
    echo   密码: test123
    echo   有效期: 10000天
    echo.
    echo ⚠️  注意: 这是测试签名文件，请勿用于生产环境!
) else (
    echo ❌ 签名文件生成失败
    pause
    exit /b 1
)

pause
