# Android Studio
.idea/
*.iml
*.ipr
*.iws

# Gradle
.gradle/
build/
!gradle/wrapper/gradle-wrapper.jar
!gradle/wrapper/gradle-wrapper.properties

# Local configuration
local.properties

# Log/OS files
*.log
.DS_Store

# APK/Output
*.apk
*.ap_*

# Generated files
captures/
output.json

# Kotlin/Java
*.class

# Misc
*.swp
*.swo

# Ignore test result files
*.ec

# Ignore crashlytics and fabric files
crashlytics-build.properties
fabric.properties

# Ignore Proguard folder generated by Eclipse
proguard/

# Ignore IntelliJ project files
*.idea/
*.iws
*.iml
*.ipr

# Ignore Android Studio Navigation editor temp files
.navigation/

# Ignore Google Services JSON file (if not checked in)
# google-services.json

# Ignore fastlane metadata, build, and screenshots
fastlane/metadata/
fastlane/screenshots/
fastlane/test_output/

# Ignore NDK build folders
obj/
.externalNativeBuild/

# Ignore CMake build folders
.cxx/

# Ignore virtual device files
*.avd
*.ini

# Ignore Mac system files
Icon?
._*

# Ignore VS Code settings
.vscode/

