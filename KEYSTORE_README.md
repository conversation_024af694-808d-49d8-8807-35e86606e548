# Android 测试签名文件说明

## 快速生成签名文件

### 方法1：使用脚本（推荐）

**Linux/macOS:**
```bash
chmod +x generate_keystore.sh
./generate_keystore.sh
```

**Windows:**
```cmd
generate_keystore.bat
```

### 方法2：手动生成

```bash
keytool -genkey -v \
    -keystore app/linkduo-test.keystore \
    -alias linkduo-test \
    -keyalg RSA \
    -keysize 2048 \
    -validity 10000 \
    -storepass test123 \
    -keypass test123 \
    -dname "CN=Test,OU=Test,O=Test,L=Test,ST=Test,C=CN"
```

## 签名配置信息

| 配置项 | 值 |
|--------|-----|
| 文件路径 | `app/linkduo-test.keystore` |
| 别名 | `linkduo-test` |
| 密码 | `test123` |
| 算法 | `RSA` |
| 密钥长度 | `2048` |
| 有效期 | `10000天` |

## build.gradle.kts 配置

签名配置已自动添加到 `app/build.gradle.kts` 中：

```kotlin
signingConfigs {
    create("test") {
        keyAlias = "linkduo-test"
        keyPassword = "test123"
        storeFile = file("linkduo-test.keystore")
        storePassword = "test123"
    }
}

buildTypes {
    debug {
        signingConfig = signingConfigs.getByName("test")
    }
    release {
        signingConfig = signingConfigs.getByName("test")
    }
}
```

## 使用方法

1. **生成签名文件**（如果还没有）：
   ```bash
   ./generate_keystore.sh  # Linux/macOS
   # 或
   generate_keystore.bat   # Windows
   ```

2. **构建签名APK**：

   ***Local***
   ```bash
   ./gradlew assembleLocalRelease
   ```
   ***所有flavors,若需要打包指定flavor，自行参考local更改***
   ```bash
   ./gradlew assembleRelease
   ```

3. **安装到设备**：

   ***Local***
   ```bash
   ./gradlew installLocalRelease
   ```
   
   ```bash
   ./gradlew installRelease
   ```

## 注意事项

⚠️ **重要提醒**：
- 这是**测试签名文件**，仅用于开发和测试
- **请勿用于生产环境**
- 生产环境请使用安全的密码和证书信息
- 签名文件应该妥善保管，不要提交到版本控制系统

## 生产环境签名

对于生产环境，建议：

1. 使用强密码
2. 妥善保管签名文件
3. 不要将签名信息硬编码在代码中
4. 使用环境变量或安全的配置文件

示例生产环境配置：
```kotlin
signingConfigs {
    create("release") {
        keyAlias = System.getenv("RELEASE_KEY_ALIAS")
        keyPassword = System.getenv("RELEASE_KEY_PASSWORD")
        storeFile = file(System.getenv("RELEASE_STORE_FILE") ?: "release.keystore")
        storePassword = System.getenv("RELEASE_STORE_PASSWORD")
    }
}
```

## 验证签名

验证APK签名：
```bash
# 查看签名信息
keytool -list -v -keystore app/linkduo-test.keystore -storepass test123

# 验证APK签名
jarsigner -verify -verbose -certs app/build/outputs/apk/release/app-release.apk
```

## 故障排除

### 常见问题

1. **keytool命令不存在**
   - 确保Java JDK已安装并配置环境变量

2. **权限错误**
   - Linux/macOS: `chmod +x generate_keystore.sh`

3. **文件已存在**
   - 脚本会提示是否覆盖现有文件

4. **构建失败**
   - 检查签名文件路径是否正确
   - 确认密码配置无误
