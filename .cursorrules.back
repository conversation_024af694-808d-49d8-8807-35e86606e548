你是一个专业的编程助手，在协助编码时请遵循以下准则:

沟通风格：
    - 不要道歉
    - 不要提供理解反馈
    - 不要建议空格
    - 不要总结
    - 不要在明确请求之外进行发明创造

核心原则：
    1. 代码质量标准
        - 编写干净、易维护、有良好文档的代码
        - 使用有意义的变量和函数名称
        - 为复杂逻辑添加注释
        - 实现适当的错误处理
        - 编写可测试的代码
        - 遵循语言/框架最佳实践
        - 使用合适的模式

    2. 设计理念
        - 优先组合而不是继承
        - 保持组件小而专注
        - 遵循DRY原则
        - 遵循SOLID原则
        - 保持关注点分离

    3. 架构模式
        - 定义清淅的项目结构
        - 提供可扩展的解决方案
        - 使用适当的设计模式
        - 实现适当的关注点分离
        - 为可扩展性做计划
        - 考虑模块化和可重用性

    4. 最佳实钱
        - 建议使用现代高效的解决方案
        #- 为关键功能编写测试
        - 记录复杂的逻辑
        - 在需要时优化性能
        - 遵循安全最佳实践

技术栈和版本:
    1.框架版本
        - mvvm架构
        - agp_version8.10.1
        - minSdkVersion21
        - targetSdkVersion34

    2. 语言版本
        - kotlin_version2.1.20
        - gradle_version8.10.1

    3. 关键依赖项
        - 版本参考文件"libs.versions.toml"
        - 依赖项参考文件"build.gradle"

    4. 开发工具
        - Android Studio Narwhal | 2025.1.1
        #- 列出推荐的扩展
        #- 指定构建工具
        #- 记录CI/CD要求
        
代码风格和结构
    1. 命名约定
        - 变量和函数名使用 camelCase 命名法
        - 成员变量使用m开头的camelCase命名法，如mName
        - 静态成员变量使用s开头的camelCase命名法,如sName
        - 静态常量使用 UPPER_CASE 命名法,如 MESSAGE_TYPE_TEXT
        - 普通常量使用_开头，驼峰命名法,如_name
        - 类名使用 PascalCase 命名法
        - 接口名使用 I 前缀，如 IUserRepository
        - 枚举名使用 PascalCase 命名法
        - 资源文件名使用 kebab-case 命名法，如 user_repository.xml
        - 包名使用 kebab-case 命名法，如 com.example.callme
        - 避免使用缩写，如使用 user 而不是 User
        #- 定义大小写标准
        #- 指定命名模式
        #- 记录缩写
        #- 列出禁止使用的名称

    2. 文件组织
        #- 定义目录结构
        .app/src/main/java/com/score/callmetest
            /manager - 全局管理类
            /network - 网络请求相关类
            /ui - 主要UI及功能模块(按功能模块组织)
                /base - 基础UI组件
                /broadcaster - 主播模块
                /home - 首页模块
                /login - 登录模块
                /main - 主页面模块
                /match - 匹配模块
                /message - 消息模块
                /mine - 我的模块
                /preview - 预览模块
                /profile - 个人资料模块
                /videocall - 视频通话模块
                /web - 网页视图模块
                /widget - 小部件模块
                
            /util - 全局工具类
        #- 指定文件命名
        #- 记录模块组织
        #- 定义导入顺序

    #3. 代码格式化规则
        # - 设置缩进标准
        # - 定义行长限制
        # - 指定空格规则
        # - 记录注释风格

    #4. 文档标准
        # - 定义文档注释格式
        # - 指定所需的文档
        # - 列出示例格式
        # - 定义API文档
